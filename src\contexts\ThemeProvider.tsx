import React, { createContext, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { fetchCurrentUserBrandingSettings } from "@/data/settings";

export interface ThemeContextType {
  primaryColor: string;
  secondaryColor: string;
  isLoading: boolean;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(
  undefined
);

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Fetch current user's branding settings
  const { data: brandingSettings, isLoading } = useQuery({
    queryKey: ["currentUserBrandingSettings"],
    queryFn: fetchCurrentUserBrandingSettings,
  });

  // Apply CSS custom properties when branding settings change
  useEffect(() => {
    if (brandingSettings) {
      const root = document.documentElement;

      // Helper function to convert hex to RGB
      const hexToRgb = (hex: string) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result
          ? `${parseInt(result[1], 16)} ${parseInt(result[2], 16)} ${parseInt(
              result[3],
              16
            )}`
          : "243 244 246"; // fallback to gray-100
      };

      // Set primary color (replaces gray background)
      root.style.setProperty(
        "--color-primary-bg",
        hexToRgb(brandingSettings.primaryColor)
      );

      // Set secondary color (replaces white backgrounds)
      root.style.setProperty(
        "--color-secondary-bg",
        hexToRgb(brandingSettings.secondaryColor)
      );
    }
  }, [brandingSettings]);

  const value: ThemeContextType = {
    primaryColor: brandingSettings?.primaryColor || "#f3f4f6", // default gray
    secondaryColor: brandingSettings?.secondaryColor || "#ffffff", // default white
    isLoading,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};
