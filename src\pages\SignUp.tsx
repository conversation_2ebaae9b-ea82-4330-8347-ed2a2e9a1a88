import AuthLayout from "@/components/auth/AuthLayout";
import SignUpForm from "@/components/auth/SignUpForm";
import { useAuth } from "@/contexts/AuthContext";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const SignUp = () => {
  const navigate = useNavigate();
  const { session, loading } = useAuth();

  useEffect(() => {
    if (session && !loading) {
      navigate("/");
    }
  }, [loading, navigate, session]);

  const handleToggleMode = () => {
    navigate("/signin");
  };

  if (loading) {
    return (
      <AuthLayout>
        <div className="flex justify-center items-center p-8">
          <div className="text-center">Loading...</div>
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout>
      <SignUpForm onToggleMode={handleToggleMode} />
    </AuthLayout>
  );
};

export default SignUp;
