import { supabase } from "@/integrations/supabase/client";
import { fetchWithToken } from "@/lib/fetchWithToken";

/* eslint-disable @typescript-eslint/no-explicit-any */
export const fetchAllUsers = async () => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/users`
  );

  if (!response.ok) {
    throw new Error("Failed to fetch users");
  }

  const data = await response.json();

  return data.data;
};

interface Invite {
  userId: string | null;
  userName: string | null;
  role:
    | "ADMIN"
    | "EMPLOYEE"
    | "STORE_MANAGER"
    | "AREA_MANAGER"
    | "AUDITOR"
    | "CHECKER"
    | "VIEWER"
    | null;
  email: string | null;
  branch: string | null;
  branchId: string | null;
  status: "active" | "inactive";
}
interface InviteEdit {
  userId: string | null;
  userName: string | null;
  role:
    | "ADMIN"
    | "EMPLOYEE"
    | "STORE_MANAGER"
    | "AREA_MANAGER"
    | "AUDITOR"
    | "CHECKER"
    | "VIEWER"
    | null;
  email: string | null;
  branchIds: string[] | null;
  status: "active" | "inactive";
}

interface InviteProps {
  page?: string;
  limit?: string;
  status?: string;
  role?: string;
  branchId?: string;
  userId?: string;
  userName?: string;
}
interface InviteResponse {
  data: Invite[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export async function fetchAllInvites(
  props?: InviteProps
): Promise<InviteResponse> {
  const {
    page = "1",
    limit = "10",
    status,
    role,
    branchId,
    userId,
    userName,
  } = props || {};
  const params = new URLSearchParams();
  params.set("page", page);
  params.set("limit", limit);
  if (status) params.set("status", status);
  if (role) params.set("role", role);
  if (branchId) params.set("branchId", branchId);
  if (userId) params.set("userId", userId);
  if (userName) params.set("userName", userName);
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/users-invite?${params.toString()}`
  );

  if (!response.ok) {
    throw new Error("Failed to fetch users");
  }

  const data = await response.json();

  return data;
}

export const fetchInviteById = async (id: string): Promise<Invite> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/users-invite/${id}`
  );

  if (!response.ok) {
    throw new Error("Failed to fetch users");
  }

  const data = await response.json();

  return data.data;
};

export const fetchInviteByIdForEdit = async (
  id: string
): Promise<InviteEdit> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/users-invite/${id}`
  );

  if (!response.ok) {
    throw new Error("Failed to fetch users");
  }

  const data = (await response.json()).data;

  const transformedData = {
    userId: data[0].userId,
    userName: data[0].userName,
    role: data[0].role,
    email: data[0].email,
    status: data[0].status,
    branchIds: data.map((item: { branchId: string }) => item.branchId),
  };

  return transformedData;
};

export const deleteInvite = async (id: string) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/users-invite/${id}`,
    {
      method: "DELETE",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to delete user");
  }

  return response.json();
};

interface UpdateInviteResponse {
  userId: string | null;
  userName: string | null;
  role:
    | "ADMIN"
    | "EMPLOYEE"
    | "STORE_MANAGER"
    | "AREA_MANAGER"
    | "AUDITOR"
    | "CHECKER"
    | "VIEWER"
    | null;
  email: string | null;
  branchId: string | null;
  branch: string | null;
  status: "active" | "inactive";
}

export const updateInvite = async (
  data: any
): Promise<UpdateInviteResponse[]> => {
  const transformedData = {
    ...data,
    status: data.status ? "active" : "inactive",
  };
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/users-invite`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(transformedData),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to update user");
  }

  return response.json();
};

export const inviteUserToBranches = async (data: any) => {
  const transformedData = {
    ...data,
    status: data.status ? "active" : "inactive",
  };

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/users-invite`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(transformedData),
    }
  );
  console.log("RESPONSE: ", response);

  if (!response.ok) {
    throw new Error(
      `${response.statusText} - ${(await response.json()).message}`
    );
  }

  const resData = await response.json();
  console.log("RES DATA: ", resData);

  return resData;
};

export interface CurrentUserProfile {
  id: string;
  email: string;
  name: string;
  phone: string;
  role: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export const fetchCurrentUser = async (): Promise<CurrentUserProfile> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/users/me`
  );

  if (!response.ok) {
    throw new Error("Failed to fetch current user profile");
  }

  const data = await response.json();
  return data.data;
};

export const deleteUserInvite = async (id: string) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/users-invite/${id}`,
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to delete user invite");
  }

  return response.json();
};
