import { useAuth } from "@/contexts/AuthContext";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const Auth = () => {
  const navigate = useNavigate();
  const { session, loading } = useAuth();

  useEffect(() => {
    if (session && !loading) {
      navigate("/");
    } else if (!loading) {
      // Redirect to signin page by default
      navigate("/signin");
    }
  }, [loading, navigate, session]);

  // Show loading while checking auth state
  return (
    <div className="flex justify-center items-center min-h-screen">
      <div className="text-center">Loading...</div>
    </div>
  );
};

export default Auth;
