import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import TemplateForm from "@/components/TemplateForm";

const NewTemplate = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleSuccess = () => {
    navigate("/templates");
  };

  const handleCancel = () => {
    navigate("/templates");
  };

  return (
    <div className="min-h-screen">
      <div className="p-6 w-full">
        <div className="flex items-center space-x-4 mb-6">
          <Link to="/templates">
            <Button variant="ghost" size="sm">
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </Link>
        </div>

        <h1 className="mb-6 font-bold text-gray-900 text-2xl">
          {t("templateBuilder.title")}
        </h1>

        <TemplateForm
          isEdit={false}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};

export default NewTemplate;
