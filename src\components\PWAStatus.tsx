import React from "react";
import { useP<PERSON> } from "@/hooks/usePWA";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Download,
  Smartphone,
  Wifi,
  WifiOff,
  RotateCcw,
  Share,
  CheckCircle,
  Info,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";

const PWAStatus: React.FC = () => {
  const { t } = useTranslation();
  const {
    isInstallable,
    isInstalled,
    isOnline,
    updateAvailable,
    installPWA,
    updateApp,
    shareContent,
    getInstallInstructions,
    canShare,
  } = usePWA();

  const handleInstall = async () => {
    const success = await installPWA();
    if (success) {
      toast.success(t("pwa.installSuccess"));
    } else {
      toast.error(t("pwa.installFailed"));
    }
  };

  const handleUpdate = () => {
    toast.info(t("pwa.updatingApp"));
    updateApp();
  };

  const handleShare = async () => {
    const success = await shareContent({
      title: "Y-Verify",
      text: t("pwa.shareText"),
      url: window.location.origin,
    });
    if (success) {
      toast.success(t("pwa.shareSuccess"));
    } else {
      // Fallback to copy URL
      try {
        await navigator.clipboard.writeText(window.location.origin);
        toast.success(t("pwa.urlCopied"));
      } catch {
        toast.error(t("pwa.shareFailed"));
      }
    }
  };

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <Card
        className={`border-l-4 ${
          isOnline ? "border-l-green-500" : "border-l-red-500"
        }`}
      >
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            {isOnline ? (
              <Wifi className="w-5 h-5 text-green-600" />
            ) : (
              <WifiOff className="w-5 h-5 text-red-600" />
            )}
            <div>
              <p className="font-medium">
                {isOnline ? t("pwa.online") : t("pwa.offline")}
              </p>
              <p className="text-gray-600 text-sm">
                {isOnline
                  ? t("pwa.onlineDescription")
                  : t("pwa.offlineDescription")}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Installation Status */}
      {isInstalled ? (
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="font-medium">{t("pwa.appInstalled")}</p>
                  <p className="text-gray-600 text-sm">
                    {t("pwa.installedDescription")}
                  </p>
                </div>
              </div>
              {canShare && (
                <Button variant="outline" size="sm" onClick={handleShare}>
                  <Share className="mr-2 w-4 h-4" />
                  {t("pwa.share")}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : isInstallable ? (
        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <Download className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="font-medium">{t("pwa.installAvailable")}</p>
                  <p className="text-gray-600 text-sm">
                    {t("pwa.installDescription")}
                  </p>
                </div>
              </div>
              <Button onClick={handleInstall} size="sm">
                <Smartphone className="mr-2 w-4 h-4" />
                {t("pwa.install")}
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <Info className="w-5 h-5 text-gray-600" />
                <div>
                  <p className="font-medium">{t("pwa.installNotAvailable")}</p>
                  <p className="text-gray-600 text-sm">
                    {t("pwa.manualInstallHint")}
                  </p>
                </div>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Info className="mr-2 w-4 h-4" />
                    {t("pwa.instructions")}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{t("pwa.howToInstall")}</DialogTitle>
                    <DialogDescription>
                      <div className="space-y-4 text-left">
                        <p>{getInstallInstructions()}</p>
                        <div className="bg-blue-50 p-3 rounded-lg">
                          <h4 className="mb-2 font-medium">
                            {t("pwa.features")}:
                          </h4>
                          <ul className="space-y-1 text-sm list-disc list-inside">
                            <li>{t("pwa.feature1")}</li>
                            <li>{t("pwa.feature2")}</li>
                            <li>{t("pwa.feature3")}</li>
                            <li>{t("pwa.feature4")}</li>
                          </ul>
                        </div>
                      </div>
                    </DialogDescription>
                  </DialogHeader>
                </DialogContent>
              </Dialog>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Update Available */}
      {updateAvailable && (
        <Card className="bg-orange-50 border-l-4 border-l-orange-500">
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <RotateCcw className="w-5 h-5 text-orange-600" />
                <div>
                  <p className="font-medium text-orange-900">
                    {t("pwa.updateAvailable")}
                  </p>
                  <p className="text-orange-700 text-sm">
                    {t("pwa.updateDescription")}
                  </p>
                </div>
              </div>
              <Button variant="default" size="sm" onClick={handleUpdate}>
                <RotateCcw className="mr-2 w-4 h-4" />
                {t("pwa.update")}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* PWA Features */}
      <Card>
        <CardContent className="p-4">
          <h3 className="mb-3 font-medium">{t("pwa.appFeatures")}</h3>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant={isOnline ? "default" : "secondary"}>
                {isOnline ? t("pwa.online") : t("pwa.offline")}
              </Badge>
              <span className="text-gray-600 text-sm">
                {t("pwa.offlineSupport")}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={isInstalled ? "default" : "secondary"}>
                {isInstalled ? t("pwa.installed") : t("pwa.browser")}
              </Badge>
              <span className="text-gray-600 text-sm">
                {t("pwa.nativeExperience")}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="default">{t("pwa.responsive")}</Badge>
              <span className="text-gray-600 text-sm">
                {t("pwa.allDevices")}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PWAStatus;
