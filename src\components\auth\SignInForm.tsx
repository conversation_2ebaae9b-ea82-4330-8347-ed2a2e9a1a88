import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeOff } from "lucide-react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { signInSchema, type SignInFormData } from "@/lib/validations";
import { useAuth } from "@/contexts/AuthContext";

interface SignInFormProps {
  onToggleMode: () => void;
}

const SignInForm = ({ onToggleMode }: SignInFormProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const { signIn, signInWithGoogle } = useAuth();
  const { t } = useTranslation();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
  });

  const rememberMe = watch("rememberMe");

  const onSubmit = async (data: SignInFormData) => {
    await signIn(data.email, data.password);
  };

  const handleGoogleSignIn = async () => {
    await signInWithGoogle();
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="font-semibold text-gray-900 text-2xl">
          {t("login.title")}
        </h2>
        <p className="mt-2 text-gray-500 text-sm">{t("login.subtitle")}</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <Label htmlFor="email" className="font-medium text-gray-700 text-sm">
            {t("login.email")}
          </Label>
          <div className="mt-1">
            <Input
              {...register("email")}
              type="email"
              placeholder={t("auth.emailPlaceholder")}
              className="shadow-sm px-3 py-2 border border-gray-300 focus:border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 w-full placeholder-gray-400"
            />
            {errors.email && (
              <p className="mt-1 text-red-600 text-sm">
                {errors.email.message}
              </p>
            )}
          </div>
        </div>

        <div>
          <Label
            htmlFor="password"
            className="font-medium text-gray-700 text-sm"
          >
            {t("login.password")}
          </Label>
          <div className="relative mt-1">
            <Input
              {...register("password")}
              type={showPassword ? "text" : "password"}
              placeholder="••••••"
              className="shadow-sm px-3 py-2 pr-10 border border-gray-300 focus:border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 w-full placeholder-gray-400"
            />
            <button
              type="button"
              className="right-0 absolute inset-y-0 flex items-center pr-3"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="w-4 h-4 text-gray-400" />
              ) : (
                <Eye className="w-4 h-4 text-gray-400" />
              )}
            </button>
            {errors.password && (
              <p className="mt-1 text-red-600 text-sm">
                {errors.password.message}
              </p>
            )}
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="rememberMe"
              checked={rememberMe}
              onCheckedChange={(checked) => setValue("rememberMe", !!checked)}
            />
            <Label htmlFor="rememberMe" className="text-gray-700 text-sm">
              {t("login.rememberMe")}
            </Label>
          </div>
          <button
            type="button"
            className="text-gray-600 hover:text-gray-900 text-sm underline"
          >
            {t("login.forgotPassword")}
          </button>
        </div>

        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-gray-800 hover:bg-gray-900 px-4 py-2 rounded-md w-full text-white transition-colors duration-200"
        >
          {isSubmitting ? t("login.loggingIn") : t("login.logIn")}
        </Button>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="border-gray-300 border-t w-full" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="px-2 text-gray-500">
              {t("login.orContinueWith")}
            </span>
          </div>
        </div>

        <Button
          type="button"
          variant="outline"
          onClick={handleGoogleSignIn}
          className="px-4 py-2 border border-gray-300 rounded-md w-full text-gray-700 transition-colors duration-200 hover:"
        >
          <svg className="mr-2 w-5 h-5" viewBox="0 0 24 24">
            <path
              fill="#4285F4"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="#34A853"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="#FBBC05"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="#EA4335"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          {t("login.continueWithGoogle")}
        </Button>

        <div className="text-center">
          <span className="text-gray-500 text-sm">{t("login.noAccount")}</span>
          <button
            type="button"
            onClick={onToggleMode}
            className="font-medium text-gray-900 hover:text-gray-700 text-sm underline"
          >
            {t("login.signUp")}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SignInForm;
