import { StoreOverviewDashboard } from "@/data/analytics";
import { Check<PERSON>ir<PERSON>, Clock, Percent, TrendingDown } from "lucide-react";

interface MetricCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  iconBgColor: string;
}

function MetricCard({ title, value, icon, iconBgColor }: MetricCardProps) {
  return (
    <div className="flex flex-1 items-center gap-3 shadow-sm p-4 rounded-2xl min-w-[280px]">
      <div
        className={`w-12 h-12 rounded-full flex items-center justify-center ${iconBgColor}`}
      >
        {icon}
      </div>
      <div className="flex flex-col gap-1.5">
        <div className="font-medium text-dashboard-gray">{title}</div>
        <div className="font-bold text-dashboard-primary text-xl">{value}</div>
      </div>
    </div>
  );
}

export function MetricsCards({ data }: { data: StoreOverviewDashboard }) {
  if (!data) {
    return null;
  }
  return (
    <div className="gap-4 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4">
      <MetricCard
        title="Completion rate"
        value={data.store_overview.completion_rate + "%"}
        icon={<CheckCircle className="w-6 h-6 text-white" />}
        iconBgColor="bg-dashboard-primary"
      />

      <MetricCard
        title="On time"
        value={data.store_overview.on_time_rate + "%"}
        icon={<Clock className="w-6 h-6 text-white" />}
        iconBgColor="bg-dashboard-green"
      />

      <MetricCard
        title="Average score"
        value={`${data.store_overview.average_score}`}
        icon={<Percent className="w-6 h-6 text-white" />}
        iconBgColor="bg-dashboard-yellow"
      />

      <MetricCard
        title="Failed Items"
        value={`${data.store_overview.failed_items_count}`}
        icon={<TrendingDown className="w-6 h-6 text-white" />}
        iconBgColor="bg-dashboard-red"
      />
    </div>
  );
}
