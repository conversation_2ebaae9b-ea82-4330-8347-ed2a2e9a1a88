// src/layouts/MainLayout.tsx
import { Sidebar } from "@/components/Sidebar";
import Header from "@/components/Header";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/contexts/AuthContext";

const MainLayout = () => {
  const { i18n } = useTranslation();
  const direction = i18n.dir(i18n.language) || "ltr";
  document.dir = direction;
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  if (
    !loading &&
    !user &&
    pathname !== "/signin" &&
    pathname !== "/signup" &&
    pathname !== "/auth"
  ) {
    navigate("/signin");
    return null; // Redirecting to SignIn page
  }
  return (
    <div className="bg-primary-bg min-h-screen">
      {!loading && user && <Header />}
      <main className="flex p-4 w-full min-h-screen">
        <Sidebar />
        <div className="w-full">
          <Outlet />
        </div>
      </main>
    </div>
  );
};

export default MainLayout;
