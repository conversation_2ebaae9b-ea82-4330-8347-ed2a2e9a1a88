import { supabase } from "@/integrations/supabase/client";

export async function fetchWithToken(url: string, options: RequestInit = {}) {
  return supabase.auth.getSession().then(({ data }) => {
    const token = data.session?.access_token;
    if (!token) {
      throw new Error("User is not authenticated");
    }

    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
      ...options.headers,
    };

    return fetch(url, { ...options, headers });
  });
}
