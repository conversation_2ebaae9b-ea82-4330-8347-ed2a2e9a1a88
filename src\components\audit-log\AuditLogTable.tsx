import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ider,
  TooltipTrigger,
} from "../ui/tooltip";

export default function AuditLogTable() {
  // Sample data matching the design
  const auditData = Array(12)
    .fill(null)
    .map((_, index) => ({
      timestamp: "May 17, 2025 - 09:43 AM",
      user: "<PERSON><PERSON> (Auditor)",
      actionType: "Checklist edited",
    }));

  return (
    <div className="flex flex-col pt-8">
      {/* Header Section */}
      <div className="mb-6">
        <h1 className="mb-11 font-satoshi font-bold text-[22px] text-black-text">
          Audit log
        </h1>

        {/* Search and Controls */}
        <div className="flex justify-between items-center">
          {/* Search Bar */}
          <div className="flex items-center gap-2.5 shadow-[0_4px_11px_-5px_rgba(206,212,213,0.21)] px-[15px] py-2.5 border border-field-stroke rounded-[40px] w-[283px]">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path
                d="M14.5833 14.584L18.3333 18.334"
                stroke="#878C94"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M16.6667 9.16601C16.6667 5.02388 13.3088 1.66602 9.16666 1.66602C5.02452 1.66602 1.66666 5.02388 1.66666 9.16601C1.66666 13.3082 5.02452 16.666 9.16666 16.666C13.3088 16.666 16.6667 13.3082 16.6667 9.16601Z"
                stroke="#878C94"
                strokeWidth="1.5"
                strokeLinejoin="round"
              />
            </svg>
            <span className="font-satoshi font-medium text-gray-text text-base">
              Search
            </span>
          </div>

          {/* Controls */}
          <div className="flex items-center gap-[15px]">
            <TooltipProvider>
              {/* Filters Button */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex justify-between items-center px-[15px] py-2.5 border border-field-stroke rounded-[40px] w-[106px] cursor-pointer">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                      <path
                        d="M2.5 5.83398H17.5"
                        stroke="#121B2A"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                      />
                      <path
                        d="M5 10H15"
                        stroke="#121B2A"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                      />
                      <path
                        d="M8.33337 14.166H11.6667"
                        stroke="#121B2A"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                      />
                    </svg>
                    <span className="font-satoshi font-medium text-black-text text-base">
                      Filters
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Apply filters to audit log</p>
                </TooltipContent>
              </Tooltip>

              {/* Export Button */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2 bg-primary-dark px-[22px] py-2.5 rounded-[40px] cursor-pointer">
                    <span className="font-satoshi font-bold text-white text-base">
                      Export{" "}
                    </span>
                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none">
                      <path
                        d="M5.5 8.25L10.2929 13.0429C10.6262 13.3762 10.7929 13.5429 11 13.5429C11.2071 13.5429 11.3738 13.3762 11.7071 13.0429L16.5 8.25"
                        stroke="white"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Export audit log data</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="relative shadow-[0_4px_20px_-22px_rgba(5,10,20,1)] rounded-xl overflow-hidden">
        <div className="px-[18px] py-[18px]">
          {/* Table Header */}
          <div className="flex justify-between mb-[21px]">
            <div className="w-[185px] font-satoshi font-medium text-gray-text text-base">
              Timestamp
            </div>
            <div className="w-[187px] font-satoshi font-medium text-gray-text text-base">
              User
            </div>
            <div className="w-[116px] font-satoshi font-medium text-gray-text text-base">
              Action type
            </div>
          </div>

          {/* Table Rows */}
          {auditData.map((item, index) => (
            <div key={index}>
              <div className="flex justify-between items-start py-[16px]">
                <div className="w-[185px] font-satoshi font-medium text-black-text text-base">
                  {item.timestamp}
                </div>
                <div className="w-[187px] font-satoshi font-medium text-black-text text-base">
                  {item.user}
                </div>
                <div className="w-[116px] font-satoshi font-medium text-black-text text-base">
                  {item.actionType}
                </div>
              </div>
              {index < auditData.length - 1 && (
                <div className="bg-table-divider w-full h-px" />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
