import { supabase } from "@/integrations/supabase/client";
import { fetchWithToken } from "@/lib/fetchWithToken";
import {
  Assignment,
  ChecklistAssignment,
  CreateAssignmentProps,
  UpdateChecklistHierarchyInput,
} from "@/lib/types";

// API functions
export const fetchAssignments = async ({
  branchId,
  status,
  page = 1,
  limit = 10,
  search,
}: {
  branchId?: string;
  status?: string;
  page: number;
  limit: number;
  search?: string;
}): Promise<{ data: ChecklistAssignment[]; totalPages: number }> => {
  const params = new URLSearchParams();
  params.set("page", page.toString());
  params.set("limit", limit.toString());
  if (search) params.set("search", search);
  if (branchId && branchId !== "all") params.set("branchId", branchId);
  if (status && status !== "all") params.set("status", status);
  const res = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/assignments?${params}`
  );

  if (!res.ok) throw new Error("Failed to fetch templates");

  const response = await res.json();
  return {
    data: response.data,
    totalPages: response.pagination.totalPages ?? 1,
  };
};
export const deleteAssignment = async (id: string) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/assignments/${id}`,
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to delete assignment");
  }

  return;
}; // API functions
export const fetchAssignmentById = async (id: string): Promise<Assignment> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/assignments/${id}`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch assignment");
  }
  const data = await response.json();
  console.log(data);
  return data.data;
};

export const createAssignment = async ({
  templateId,
  branchId,
  assignedTo,
  assignedBy,
  dueDate,
  startDate,
  status,
  frequency,
  notes,
  sections,
  passRate,
}: CreateAssignmentProps) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/assignments`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        templateId,
        branchId,
        assignedTo,
        assignedBy,
        dueDate,
        startDate,
        status,
        frequency,
        notes,
        sections,
        passRate,
      }),
    }
  );

  if (!response.ok) {
    throw new Error((await response.json()).message);
  }

  const responseData = await response.json();
  return responseData.data;
};

export interface CalendarReturn {
  id: string;
  calendarString: string;
  templateName: string;
  storeName: string;
  branchLocation: string;
  startTime: string | null;
  startDate: string | null;
  dueDate: string | null;
  assignee: string | null;
  completedAt: string | null;
}

// Function to get calendar events
export const fetchCalendarAssignments = async (
  selectedDate?: Date
): Promise<CalendarReturn[]> => {
  const currentDate = selectedDate || new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const startDate = new Date(year, month, 1);
  const endDate = new Date(year, month + 1, 0);
  const params = new URLSearchParams();
  params.set("startDate", startDate.toISOString());
  params.set("endDate", endDate.toISOString());
  const res = await fetchWithToken(
    `${
      process.env.API_ENDPOINT
    }/api/assignments/calendar/events?${params.toString()}`,
    {
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!res.ok) throw new Error("Failed to fetch templates");

  const response = await res.json();

  return response.data;
};

// Function to update assignment
export const updateAssignment = async (
  id: string,
  data: UpdateChecklistHierarchyInput
) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/assignments/${id}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to update assignment");
  }

  const responseData = await response.json();
  return responseData.data;
};

// Function to update assignment section
export const updateAssignmentSection = async (
  sectionId: string,
  data: { name?: string; description?: string; order?: number }
) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/assignments/sections/${sectionId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to update assignment section");
  }

  const responseData = await response.json();
  return responseData.data;
};

// Function to update assignment field
export const updateAssignmentField = async (
  fieldId: string,
  data: {
    question?: string;
    questionType?: string;
    required?: boolean;
    requiresEvidence?: boolean;
    score?: string;
    hint?: string;
    order?: number;
    preferredAnswer?: string;
  }
) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/assignments/fields/${fieldId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to update assignment field");
  }

  const responseData = await response.json();
  return responseData.data;
};
