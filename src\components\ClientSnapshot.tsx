import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { fetchClientSnapshot } from "@/data/dashboard";
import { ClientSnapshot } from "@/lib/types";
import { useQuery } from "@tanstack/react-query";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

export interface Store {
  id: string;
  name: string;
  storeAdminId: string | null;
  businessType: string | null;
  status: "active" | "inactive" | "archived";
  numberOfBranches: number;
  createdAt: Date;
  updatedAt: Date;
  adminEmail: string | null;
}

export interface PaginatedResponse {
  clients: ClientSnapshot[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export function ClientSnapshotComponent() {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedStore, setSelectedStore] = useState<ClientSnapshot | null>(
    null
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Get pagination params from URL, with defaults
  const currentPage = parseInt(searchParams.get("page") || "1", 10);
  const pageSize = parseInt(searchParams.get("limit") || "8", 10);
  const navigate = useNavigate();

  const { data, isLoading, error } = useQuery({
    queryKey: ["clients-snapshot", currentPage, pageSize],
    queryFn: async () => await fetchClientSnapshot(currentPage, pageSize),
    staleTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: 1000,
  });

  // Modal handlers
  const handleStoreClick = (client: ClientSnapshot) => {
    setSelectedStore(client);
    navigate(`/companies/${client.id}/branches`);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedStore(null);
  };

  // Navigation functions
  const goToPage = (page: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("page", page.toString());
    setSearchParams(newParams);
  };

  const goToNextPage = () => {
    if (data?.pagination?.hasNext) {
      goToPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (data?.pagination?.hasPrev) {
      goToPage(currentPage - 1);
    }
  };

  const changePageSize = (newLimit: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("limit", newLimit.toString());
    newParams.set("page", "1"); // Reset to first page when changing page size
    setSearchParams(newParams);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="font-semibold text-gray-900 text-lg">
            {t("dashboard.clientSnapshot")}
          </h3>
          {data?.pagination && (
            <p className="mt-1 text-gray-500 text-sm">
              {t("global.showingToOfResults", {
                "0": (currentPage - 1) * pageSize + 1,
                "1": Math.min(currentPage * pageSize, data?.pagination.total),
                "2": data?.pagination.total,
              })}
            </p>
          )}
        </div>
        {/* Page size selector */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <label htmlFor="pageSize" className="text-gray-600 text-sm">
              {t("clientSnapshot.show")}:
            </label>
            <select
              id="pageSize"
              value={pageSize}
              onChange={(e) => changePageSize(parseInt(e.target.value))}
              className="px-2 py-1 border border-gray-300 rounded text-sm"
            >
              <option value={4}>4</option>
              <option value={8}>8</option>
              <option value={12}>12</option>
              <option value={20}>20</option>
            </select>
          </div>
          <button className="font-medium text-blue-600 hover:text-blue-800 text-sm">
            {t("global.showAll")}
          </button>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="gap-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: pageSize }).map((_, i) => (
            <Card key={i} className="space-y-4 p-4 animate-pulse">
              <div className="bg-gray-200 rounded-lg h-16"></div>
              <div className="bg-gray-200 rounded w-3/4 h-4"></div>
              <div className="bg-gray-100 rounded w-1/2 h-3"></div>
            </Card>
          ))}
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 p-4 border border-red-200 rounded font-medium text-red-600">
          {t("clientSnapshot.failedToLoad")}
        </div>
      )}

      {/* Data State */}
      {!isLoading && !error && (
        <>
          <div className="gap-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-6">
            {data?.clients.map((client) => (
              <Card
                key={client.id}
                className="hover:shadow-md p-4 transition-shadow cursor-pointer"
                onClick={() => handleStoreClick(client)}
              >
                <div className="mb-3">
                  <div className="flex justify-center items-center bg-gray-100 rounded-lg w-full h-16">
                    <div className="bg-gray-300 rounded-full w-8 h-8"></div>
                  </div>
                </div>
                <div>
                  <h4 className="mb-1 font-medium text-gray-900">
                    {client.name}
                  </h4>
                  <p className="text-gray-500 text-sm">
                    {client.numberOfBranches} {t("clientSnapshot.branch")}
                    {client.numberOfBranches !== 1 &&
                      t("clientSnapshot.branchPlural")}
                  </p>
                </div>
              </Card>
            ))}
          </div>

          {/* Pagination Controls */}
          {data?.pagination && data?.pagination.totalPages > 1 && (
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={!data?.pagination.hasPrev}
                  className="flex items-center gap-1"
                >
                  <ChevronLeft className="w-4 h-4" />
                  {t("global.previous")}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={!data?.pagination.hasNext}
                  className="flex items-center gap-1"
                >
                  {t("global.next")}
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
              {/* Page numbers */}
              <div className="flex items-center gap-1">
                {Array.from(
                  { length: Math.min(5, data?.pagination.totalPages) },
                  (_, i) => {
                    let pageNum: number;
                    if (data?.pagination.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= data?.pagination.totalPages - 2) {
                      pageNum = data?.pagination.totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }
                    return (
                      <Button
                        key={pageNum}
                        variant={
                          currentPage === pageNum ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => goToPage(pageNum)}
                        className="p-0 w-8 h-8"
                      >
                        {pageNum}
                      </Button>
                    );
                  }
                )}
              </div>
              <div className="text-gray-500 text-sm">
                {t("global.pageOf", {
                  "0": currentPage,
                  "1": data?.pagination.totalPages,
                })}
              </div>
            </div>
          )}
        </>
      )}

      {/* Modal */}
      {/* <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedStore?.name} - Branches</DialogTitle>
          </DialogHeader>
          {selectedStore && (
            <BranchesModal
              storeId={selectedStore.id}
              storeName={selectedStore.name}
            />
          )}
        </DialogContent>
      </Dialog> */}
    </div>
  );
}
