import { DeleteSectionModal } from "@/components/DeleteSectionModal";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  fetchTemplates,
  fetchBusinessTypes,
  deleteTemplate,
} from "@/data/templates";
import { setParam } from "@/lib/utils";
import { canUserAccess } from "@/data/permissions";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Edit,
  Eye,
  Loader2,
  Pencil,
  Plus,
  Search,
  Trash,
  Trash2,
} from "lucide-react";
import { useState } from "react";
import {
  Link,
  SetURLSearchParams,
  useNavigate,
  useSearchParams,
} from "react-router-dom";
import { useTranslation } from "react-i18next";

const Templates = () => {
  const { t } = useTranslation();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");
  const [deletingTemplateId, setDeletingTemplateId] = useState<string | null>(
    null
  );
  const [searchParams, setSearchParams] = useSearchParams();
  const queryClient = useQueryClient();

  const currentPage = parseInt(searchParams.get("page") || "1", 10);
  const search = searchParams.get("search") || "";
  const businessType = searchParams.get("businessType") || "all";

  // Pagination logic
  const ITEMS_PER_PAGE = 10;

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["templates", currentPage, businessType],
    queryFn: () =>
      fetchTemplates({
        page: currentPage,
        limit: ITEMS_PER_PAGE,
        search,
        businessType,
      }),
  });
  const {
    data: businessTypes,
    isLoading: businessTypesAreLoading,
    isError: businessTypesIsError,
    error: businessTypesError,
  } = useQuery({
    queryKey: ["business-types"],
    queryFn: fetchBusinessTypes,
  });

  // Check if user can create templates
  const { data: canCreateTemplates = false } = useQuery({
    queryKey: ["user-can-access", "crud_templates"],
    queryFn: () => canUserAccess("crud_templates"),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const templates = data?.data ?? [];
  const totalPages = data?.totalPages ?? 1;

  // Mutation to delete template
  const deleteTemplateMutation = useMutation({
    mutationFn: deleteTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
      setDeletingTemplateId(null);
    },
    onError: (error) => {
      console.error("Error deleting template:", error);
      setDeletingTemplateId(null);
    },
  });

  const handleDeleteTemplate = (templateId: string) => {
    setDeletingTemplateId(templateId);
    setShowDeleteModal(true);
  };
  const confirmDelete = () => {
    if (deletingTemplateId) {
      deleteTemplateMutation.mutate(deletingTemplateId);
      setDeletingTemplateId(null);
    }
  };

  if (isLoading) {
    return (
      <div className="flex bg-primary-bg w-full min-h-screen">
        <div className="flex flex-col w-full">
          <div className="flex-1 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="font-bold text-gray-900 text-2xl">
                {t("templates.title")}
              </h2>
              {canCreateTemplates && (
                <Link to="/templates/new">
                  <Button className="bg-slate-700 hover:bg-slate-800">
                    <Plus className="mr-2 w-4 h-4" />
                    {t("templates.newTemplate")}
                  </Button>
                </Link>
              )}
            </div>
            <div className="flex justify-center items-center min-h-96">
              <Loader2 className="w-8 h-8 animate-spin" />
              <span className="ml-2 text-gray-600">
                {t("templates.loadingTemplates")}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex bg-primary-bg w-full min-h-screen">
        <div className="flex flex-col w-full">
          <div className="flex-1 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="font-bold text-gray-900 text-2xl">
                {t("templates.title")}
              </h2>
              {canCreateTemplates && (
                <Link to="/templates/new">
                  <Button className="bg-slate-700 hover:bg-slate-800">
                    <Plus className="mr-2 w-4 h-4" />
                    {t("templates.newTemplate")}
                  </Button>
                </Link>
              )}
            </div>
            <div className="flex justify-center items-center min-h-96">
              <div className="text-center">
                <p className="mb-2 text-red-600">
                  {t("templates.errorLoading")}
                </p>
                <p className="mb-4 text-gray-600 text-sm">
                  {error instanceof Error
                    ? error.message
                    : t("templates.unknownError")}
                </p>
                <Button
                  variant="outline"
                  onClick={() =>
                    queryClient.refetchQueries({ queryKey: ["templates"] })
                  }
                >
                  {t("templates.retry")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex bg-primary-bg w-full min-h-screen">
      {/*  Main Content */}
      <div className="flex flex-col w-full">
        {/* Main Content */}
        <div className="flex-1 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="font-bold text-gray-900 text-2xl">
              {t("templates.title")}
            </h2>
            {canCreateTemplates && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link to="/templates/new">
                      <Button className="bg-slate-700 hover:bg-slate-800">
                        <Plus className="mr-2 w-4 h-4" />
                        {t("templates.newTemplate")}
                      </Button>
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("templates.tooltips.createNew")}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1 items-center max-w-md">
              <Search className="top-1/2 left-3 absolute w-4 h-4 text-gray-400 -translate-y-1/2 transform" />
              <Input
                value={search}
                onChange={(e) =>
                  setParam(
                    "search",
                    e.target.value,
                    searchParams,
                    setSearchParams
                  )
                }
                placeholder={t("templates.searchPlaceholder")}
                className="pl-10"
              />
            </div>
            <Button
              onClick={() => {
                queryClient.invalidateQueries({ queryKey: ["templates"] });
              }}
            >
              {t("templates.search")}
            </Button>
            <div className="flex items-center space-x-2">
              <span className="text-gray-600 text-sm">
                {t("templates.industryType")}:
              </span>
              <Select
                value={businessType}
                onValueChange={(value) =>
                  setParam("businessType", value, searchParams, setSearchParams)
                }
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("templates.all")}</SelectItem>
                  {businessTypesAreLoading ? (
                    <SelectItem value="loading">
                      {t("common.loading")}
                    </SelectItem>
                  ) : businessTypesIsError ? (
                    <SelectItem value="error">
                      {t("templates.error")}
                    </SelectItem>
                  ) : businessTypes.data.length === 0 ? (
                    <SelectItem value="none">
                      {t("templates.noBusinessTypesFound")}
                    </SelectItem>
                  ) : (
                    businessTypes.data.map((industry) => (
                      <SelectItem key={industry} value={industry}>
                        {industry}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-600 text-sm">
                {t("templates.status")}:
              </span>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("templates.all")}</SelectItem>
                  <SelectItem value="Published">
                    {t("templates.published")}
                  </SelectItem>
                  <SelectItem value="Draft">{t("templates.draft")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Templates Table */}
          <Card className="overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr>
                    <th className="px-4 py-3 font-medium text-gray-700 text-left">
                      {t("templates.templateName")}
                    </th>
                    <th className="px-4 py-3 font-medium text-gray-700 text-left">
                      {t("templates.industryType")}
                    </th>
                    <th className="px-4 py-3 font-medium text-gray-700 text-left">
                      {t("templates.sectionCount")}
                    </th>
                    <th className="px-4 py-3 font-medium text-gray-700 text-left">
                      {t("templates.status")}
                    </th>
                    <th className="px-4 py-3 font-medium text-gray-700 text-left">
                      {t("templates.action")}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {templates.length === 0 ? (
                    <tr>
                      <td
                        colSpan={5}
                        className="px-4 py-8 text-gray-500 text-center"
                      >
                        {templates.length === 0 && templates.length > 0
                          ? t("templates.noMatchingTemplates")
                          : t("templates.noTemplatesFound")}
                      </td>
                    </tr>
                  ) : (
                    templates.map((template) => (
                      <tr
                        key={template.id}
                        className="border-b transition-colors hover:"
                      >
                        <td className="px-4 py-3 text-gray-900">
                          {template.title}
                        </td>
                        <td className="px-4 py-3 text-gray-600">
                          {template.businessType}
                        </td>
                        <td className="px-4 py-3 text-gray-600">
                          {template.sectionsCount}
                        </td>
                        <td className="px-4 py-3">
                          {/* <div className="flex items-center space-x-2">
                            <Switch
                              checked={template.status === "Published"}
                              onCheckedChange={(checked) =>
                                handleStatusChange(template.id, checked)
                              }
                              disabled={updateTemplateMutation.isPending}
                              className="scale-75"
                            />
                            <span className="text-gray-600 text-sm">
                              {template.status}
                            </span>
                            {updateTemplateMutation.isPending && (
                              <Loader2 className="w-3 h-3 animate-spin" />
                            )}
                          </div> */}
                          {t("templates.published")}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-4">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Link to={`/templates/${template.id}`}>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="p-0"
                                    >
                                      <Eye className="w-3 h-3" />
                                    </Button>
                                  </Link>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{t("templates.tooltips.viewTemplate")}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            {canCreateTemplates && (
                              <>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Link
                                        to={`/templates/${template.id}/edit`}
                                      >
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="p-0"
                                        >
                                          <Pencil className="w-3 h-3" />
                                        </Button>
                                      </Link>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>
                                        {t("templates.tooltips.editTemplate")}
                                      </p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="p-0"
                                        onClick={() =>
                                          handleDeleteTemplate(template.id)
                                        }
                                      >
                                        <Trash className="w-3 h-3" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>
                                        {t("templates.tooltips.deleteTemplate")}
                                      </p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </Card>
          <div className="flex justify-center space-x-2 mt-4">
            {Array.from({ length: totalPages }, (_, i) => (
              <Button
                key={i + 1}
                size="sm"
                variant={currentPage === i + 1 ? "default" : "outline"}
                onClick={() =>
                  setParam(
                    "page",
                    (i + 1).toString(),
                    searchParams,
                    setSearchParams
                  )
                }
                disabled={currentPage === i + 1}
              >
                {i + 1}
              </Button>
            ))}
          </div>
        </div>
      </div>
      {/* Delete Modal */}
      <DeleteSectionModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={confirmDelete}
        labelToDelete={t("templates.templateLabel")}
        isPending={deleteTemplateMutation.isPending}
      />
    </div>
  );
};

export default Templates;
