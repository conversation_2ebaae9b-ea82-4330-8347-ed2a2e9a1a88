import { ActivityLog } from "@/components/ActivityLog";
import { ClientsChart } from "@/components/ClientsChart";
import { DashboardHeader } from "@/components/DashboardHeader";
import { MetricsCards } from "@/components/MetricsCards";
import { SidebarProvider } from "@/components/ui/sidebar";
import { ClientSnapshotComponent } from "./ClientSnapshot";
import { PermissionWrapper } from "@/components/PermissionWrapper";
import MaintainerDashboardWidget from "@/components/MaintainerDashboardWidget";

const Dashboard = () => {
  return (
    <PermissionWrapper
      requiredPermission="access_dashboard"
      fallbackPath="/assignments"
      showForbidden={false}
    >
      <SidebarProvider>
        <div className="flex w-full min-h-screen">
          <main className="flex-1 overflow-hidden">
            <div className="p-6">
              <DashboardHeader />

              <div className="space-y-8">
                <MetricsCards />

                <div className="flex flex-row items-start gap-8">
                  <ClientsChart />
                  <ActivityLog />
                  <MaintainerDashboardWidget />
                </div>

                <ClientSnapshotComponent />
              </div>
            </div>
          </main>
        </div>
      </SidebarProvider>
    </PermissionWrapper>
  );
};

export default Dashboard;
