import { Header } from "@/components/CompanyHeader";
import { Sidebar } from "@/components/Sidebar";
import { NewCompanyForm } from "@/components/NewCompanyForm";
import { useTranslation } from "react-i18next";

const NewCompany = () => {
  const { t } = useTranslation();

  return (
    <div className="flex min-h-screen">
      <div className="flex-1">
        <div className="min-h-screen">
          <main className="p-6">
            <div className="mb-8">
              <h1 className="mb-2 font-semibold text-slate-900 text-2xl">
                {t("company.newCompanyTitle")}
              </h1>
            </div>

            <NewCompanyForm />
          </main>
        </div>
      </div>
    </div>
  );
};

export default NewCompany;
