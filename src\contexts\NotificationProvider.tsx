import {
  getMessaging,
  getToken,
  onMessage,
  isSupported,
} from "firebase/messaging";
import { useEffect, useState, useRef, ReactNode } from "react";
import { app } from "@/firebaseConfig";
import { useAuth } from "./AuthContext";
import { useNotifications } from "@/components/NotificationContext";

interface NotificationProviderProps {
  children?: ReactNode;
}

const NotificationProvider = ({ children }: NotificationProviderProps) => {
  const auth = useAuth();
  const { addNotification } = useNotifications();
  const [isTokenFound, setTokenFound] = useState(false);
  const tokenRef = useRef<string | null>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const messagingRef = useRef<any>(null);
  const registrationRef = useRef<ServiceWorkerRegistration | null>(null);
  const deviceIdRef = useRef<string>("");

  // Generate a unique device ID using browser crypto API
  const generateDeviceId = () => {
    const array = new Uint32Array(5);
    window.crypto.getRandomValues(array);
    return Array.from(array, (dec) => dec.toString(36).padStart(6, "0")).join(
      ""
    );
  };

  // Initialize device ID
  useEffect(() => {
    let deviceId = localStorage.getItem("fcm_device_id");
    if (!deviceId) {
      deviceId = generateDeviceId();
      localStorage.setItem("fcm_device_id", deviceId);
    }
    deviceIdRef.current = deviceId;
  }, []);

  // Store token to server
  const storeTokenToServer = async (token: string) => {
    try {
      if (!auth.user?.id) {
        console.warn("User ID not available. Token not stored.");
        return;
      }

      await fetch(`${process.env.API_ENDPOINT}/api/notifications/token`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId: auth.user.id,
          token,
          deviceId: deviceIdRef.current,
        }),
      });
      console.log("Token stored on server");
    } catch (error) {
      console.error("Failed to store token:", error);
    }
  };

  // Check for token changes
  const checkToken = async () => {
    if (!messagingRef.current || !registrationRef.current || !auth.user?.id) {
      console.warn("Skipping token check - missing dependencies");
      return;
    }

    try {
      const currentToken = await getToken(messagingRef.current, {
        serviceWorkerRegistration: registrationRef.current,
        vapidKey:
          "BG8VckAscpEW-mJBJi2MEl3QWilW_jhvJH2VxqssnHHN5ZxnjU55bCPBMmj_3g16Xsd9Z_dtl0tD4gTam69vwDQ",
      });

      if (currentToken && currentToken !== tokenRef.current) {
        tokenRef.current = currentToken;
        await storeTokenToServer(currentToken);
      }
    } catch (error) {
      console.error("Token check error:", error);
    }
  };

  useEffect(() => {
    const setupNotifications = async () => {
      try {
        const fcmSupported = await isSupported();
        console.log("FCM supported:", fcmSupported);
        if (!fcmSupported) return;

        const registration = await navigator.serviceWorker.ready;
        registrationRef.current = registration;
        console.log("Service Worker registration:", registration);

        const messaging = getMessaging(app);
        messagingRef.current = messaging;

        // Request permission (optional for in-app notifications)
        const permission = await Notification.requestPermission();
        console.log("Notification permission:", permission);

        await checkToken();
        const tokenCheckInterval = setInterval(checkToken, 5 * 60 * 1000);

        // Handle foreground messages - Show in-app notifications
        onMessage(messaging, (payload) => {
          console.log("Foreground message received:", payload);

          if (payload.notification) {
            // Show in-app notification
            addNotification({
              title: payload.notification.title || "New Message",
              body: payload.notification.body || "You have a new message",
              image: payload.notification.icon,
              data: {
                ...payload.data,
                messageId: payload.messageId,
                from: payload.from,
                actionUrl: payload.data?.url, // Optional action URL
              },
            });

            // Optional: Also try browser notification if permission granted and tab not focused
            if (Notification.permission === "granted" && !document.hasFocus()) {
              try {
                new Notification(payload.notification.title || "New Message", {
                  body: payload.notification.body,
                  icon: payload.notification.icon || "/favicon.ico",
                  data: payload.data || {},
                });
              } catch (error) {
                console.log(
                  "Browser notification failed, using in-app only:",
                  error
                );
              }
            }
          }
        });

        return () => clearInterval(tokenCheckInterval);
      } catch (error) {
        console.error("Notification setup error:", error);
      } finally {
        setTokenFound(true);
      }
    };

    if (!isTokenFound && auth.user?.id) {
      setupNotifications();
    }
  }, [isTokenFound, auth.user?.id, addNotification]);

  return <>{children}</>;
};

export default NotificationProvider;
