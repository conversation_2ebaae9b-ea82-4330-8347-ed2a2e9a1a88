import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import {
  checkMultipleRolePermissions,
  updateMultiplePermissions,
  type Feature,
  type PermissionUpdate,
} from "@/data/permissions";
import { roles } from "@/lib/constants";
import { Roles } from "@/lib/types";

// All available features from the backend enum
const allFeatures: Feature[] = [
  "view_reports",
  "create_edit_checklist",
  "assign_template",
  "submit_checklist",
  "access_calendar_view",
  "access_dashboard",
  // New view permissions
  "view_checklist_assignments",
  "view_assignment_responses",
  "view_templates",
  "view_stores",
  "view_branches",
  // New CRUD permissions
  "crud_checklist_assignments",
  "crud_assignment_responses",
  "crud_templates",
  "crud_stores",
  "crud_branches",
  // Admin-only permissions
  "view_modify_settings",
  "role_permission_matrix",
  "user_invite",
];

// Map backend feature names to display names
const featureDisplayNames: Record<Feature, string> = {
  view_reports: "View Reports",
  create_edit_checklist: "Create/Edit Checklists",
  assign_template: "Assign Templates",
  submit_checklist: "Submit Checklists",
  access_calendar_view: "Access Calendar View",
  access_dashboard: "Access Dashboard",
  // New view permissions
  view_checklist_assignments: "View Checklist Assignments",
  view_assignment_responses: "View Assignment Responses",
  view_templates: "View Templates",
  view_stores: "View Stores",
  view_branches: "View Branches",
  // New CRUD permissions
  crud_checklist_assignments: "CRUD Checklist Assignments",
  crud_assignment_responses: "CRUD Assignment Responses",
  crud_templates: "CRUD Templates",
  crud_stores: "CRUD Stores",
  crud_branches: "CRUD Branches",
  // Admin-only permissions
  view_modify_settings: "View/Modify Settings",
  role_permission_matrix: "Role & Permission Matrix",
  user_invite: "User Invite",
};

// Map backend role names to display names
const roleDisplayNames: Record<Roles, string> = {
  ADMIN: "Admin",
  STORE_MANAGER: "Store Manager",
  BRANCH_MANAGER: "Branch Manager",
  AUDITOR: "Auditor",
  CHECKER: "Checker",
  MAINTAINER: "Maintainer",
  VIEWER: "Viewer",
};

interface PermissionMatrix {
  [key: string]: {
    [role in Roles]: boolean;
  };
}

export default function RolePermissionMatrix() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [permissionMatrix, setPermissionMatrix] = useState<PermissionMatrix>(
    {} as PermissionMatrix
  );

  // Fetch permissions for all roles and features
  const {
    data: permissionsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["rolePermissions"],
    queryFn: async () => {
      const matrix: PermissionMatrix = {} as PermissionMatrix;

      // Fetch permissions for each role
      const rolePermissions = await Promise.all(
        roles.map(async (role) => {
          const permissions = await checkMultipleRolePermissions(
            role,
            allFeatures
          );
          return { role, permissions };
        })
      );

      // Transform data into matrix format
      allFeatures.forEach((feature) => {
        matrix[feature] = {} as Record<Roles, boolean>;
        rolePermissions.forEach(({ role, permissions }) => {
          matrix[feature][role] = permissions[feature];
        });
      });

      return matrix;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update permissions mutation
  const updatePermissionsMutation = useMutation({
    mutationFn: async (updates: PermissionUpdate[]) => {
      return await updateMultiplePermissions(updates);
    },
    onSuccess: () => {
      toast.success("Permissions updated successfully!");
      queryClient.invalidateQueries({ queryKey: ["rolePermissions"] });
    },
    onError: (error: Error) => {
      toast.error(`Failed to update permissions: ${error.message}`);
    },
  });

  // Update local state when data is fetched
  useEffect(() => {
    if (permissionsData) {
      setPermissionMatrix(permissionsData);
    }
  }, [permissionsData]);

  const updatePermission = (feature: Feature, role: Roles, value: boolean) => {
    setPermissionMatrix((prev) => ({
      ...prev,
      [feature]: {
        ...prev[feature],
        [role]: value,
      },
    }));
  };

  const handleSave = () => {
    if (!permissionsData) return;

    // Calculate what permissions have changed
    const updates: PermissionUpdate[] = [];

    allFeatures.forEach((feature) => {
      roles.forEach((role) => {
        const currentValue = permissionMatrix[feature]?.[role];
        const originalValue = permissionsData[feature]?.[role];

        if (currentValue !== originalValue) {
          updates.push({
            role,
            feature,
            value: currentValue,
          });
        }
      });
    });

    if (updates.length === 0) {
      toast.info("No changes to save");
      navigate("/management");
      return;
    }

    console.log("Saving permission updates:", updates);
    updatePermissionsMutation.mutate(updates);
  };

  const handleCancel = () => {
    navigate("/management");
  };

  if (isLoading) {
    return (
      <div className="space-y-6 p-8">
        <h2 className="font-bold text-primary text-2xl">
          Role & Permission Matrix
        </h2>
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500">Loading permissions...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6 p-8">
        <h2 className="font-bold text-primary text-2xl">
          Role & Permission Matrix
        </h2>
        <div className="flex justify-center items-center h-64">
          <div className="text-red-500">
            Error loading permissions: {(error as Error).message}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-8">
      {/* Page Title */}
      <h2 className="font-bold text-primary text-2xl">
        Role & Permission Matrix
      </h2>

      {/* Matrix Table */}
      <div className="bg-secondary-bg shadow-sm border border-border rounded-xl overflow-hidden">
        {/* Header */}
        <div className="bg-[#E7EAEE] px-6 py-4 border-b border-border">
          <div
            className="items-center gap-6 grid"
            style={{
              gridTemplateColumns: `1fr repeat(${roles.length}, 120px)`,
            }}
          >
            <div className="font-bold text-primary text-lg">Feature / Role</div>
            {roles.map((role) => (
              <div
                key={role}
                className="font-bold text-primary text-lg text-center"
              >
                {roleDisplayNames[role]}
              </div>
            ))}
          </div>
        </div>

        {/* Permission Rows */}
        <div className="divide-y divide-border">
          {allFeatures.map((feature) => (
            <div key={feature} className="px-6 py-4">
              <div
                className="items-center gap-6 grid"
                style={{
                  gridTemplateColumns: `1fr repeat(${roles.length}, 120px)`,
                }}
              >
                {/* Feature Name */}
                <div className="font-bold text-primary">
                  {featureDisplayNames[feature]}
                </div>

                {/* Role Checkboxes */}
                {roles.map((role) => (
                  <div key={role} className="flex justify-center">
                    <Checkbox
                      checked={permissionMatrix[feature]?.[role] || false}
                      onCheckedChange={(checked) =>
                        updatePermission(feature, role, checked as boolean)
                      }
                      className="data-[state=checked]:bg-primary data-[state=checked]:border-primary w-5 h-5"
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-center items-center gap-4 pt-8">
        <Button
          variant="outline"
          onClick={handleCancel}
          className="bg-[#F5F6F8] hover:bg-[#F5F6F8]/80 px-8 py-2 border-primary rounded-full text-primary"
          disabled={updatePermissionsMutation.isPending}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          className="bg-primary hover:bg-primary/90 px-8 py-2 rounded-full text-white"
          disabled={updatePermissionsMutation.isPending}
        >
          {updatePermissionsMutation.isPending ? "Saving..." : "Save"}
        </Button>
      </div>
    </div>
  );
}
