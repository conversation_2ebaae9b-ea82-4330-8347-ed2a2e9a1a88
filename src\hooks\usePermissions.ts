import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { canUserAccess, type Feature } from "@/data/permissions";
import { Roles } from "@/lib/types";

// Hook to check permissions in components
export const usePermissions = () => {
  const [userRole, setUserRole] = useState<Roles | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const getUserRole = async () => {
      try {
        const { data } = await supabase.auth.getSession();
        const role = data.session?.user.user_metadata?.role as Roles;
        setUserRole(role);
      } catch (error) {
        console.error("Error getting user role:", error);
      } finally {
        setIsLoading(false);
      }
    };

    getUserRole();
  }, []);

  const checkPermission = async (permission: Feature): Promise<boolean> => {
    try {
      return await canUserAccess(permission);
    } catch (error) {
      console.error("Error checking permission:", error);
      return false;
    }
  };

  return {
    userRole,
    isLoading,
    checkPermission,
  };
};
