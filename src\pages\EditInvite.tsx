import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { z } from "zod";

import { fetchAllBranches } from "@/data/branches";
import { fetchInviteByIdForEdit, updateInvite } from "@/data/users";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { MultiSelect } from "@/components/ui/multi-select";
import { Switch } from "@/components/ui/switch";
import { ChevronRight } from "lucide-react";
import { useEffect } from "react";

const formSchema = z.object({
  email: z.string().email(),
  addBranchIds: z.array(z.string()),
  removeBranchIds: z.array(z.string()),
  selectedBranchIds: z.array(z.string()),
  status: z.boolean(),
});

type FormSchema = z.infer<typeof formSchema>;

export default function InviteUserEdit() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { id } = useParams<{ id: string }>();

  const {
    data: inviteData,
    isLoading: isInviteLoading,
    error: inviteError,
  } = useQuery({
    queryKey: ["invite", id],
    queryFn: () => fetchInviteByIdForEdit(id!),
    enabled: !!id,
  });

  const { data: branchesResult, isLoading: branchesLoading } = useQuery({
    queryKey: ["branches"],
    queryFn: fetchAllBranches,
    staleTime: 5 * 60 * 1000,
  });

  const branchesOptions =
    branchesResult?.map((branch) => ({
      label: `${branch.storeName} - ${branch.location}`,
      value: branch.id,
    })) ?? [];

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      addBranchIds: [],
      removeBranchIds: [],
      selectedBranchIds: [],
      status: true,
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: FormSchema) => updateInvite(data),
    onSuccess: () => {
      navigate("/user-management");
      queryClient.invalidateQueries({ queryKey: ["all-users"] });
    },
  });

  const handleCancel = () => navigate("/user-management");

  const onSubmit = (data: FormSchema) => {
    updateMutation.mutate(data);
  };

  useEffect(() => {
    if (inviteData) {
      form.setValue("email", inviteData.email);
      form.setValue("status", inviteData.status === "active");
      // Set initial state to show current branches
      form.setValue("addBranchIds", []);
      form.setValue("removeBranchIds", []);
      form.setValue("selectedBranchIds", inviteData.branchIds);
    }
  }, [inviteData, form]);

  if (isInviteLoading || branchesLoading) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  if (inviteError) {
    return <div className="p-8 text-red-500">Error loading invite data</div>;
  }

  const originalBranchIds = inviteData.branchIds;

  return (
    <div className="space-y-6 p-8">
      <div className="flex items-center gap-2 text-lg">
        <span className="font-medium text-muted-foreground">
          User management
        </span>
        <ChevronRight className="w-6 h-6 text-muted-foreground" />
        <span className="font-medium text-primary">Edit User</span>
      </div>

      <h2 className="font-bold text-primary text-2xl">Edit User</h2>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="gap-6 grid grid-cols-1 lg:grid-cols-2"
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-primary">Email</FormLabel>
                <FormControl>
                  <input
                    type="email"
                    {...field}
                    className="shadow-sm px-4 py-3 border border-border rounded-full w-full"
                    disabled
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="addBranchIds"
            render={() => (
              <FormItem>
                <FormLabel className="text-primary">
                  Branch Assignment
                </FormLabel>
                <FormControl>
                  <MultiSelect
                    options={branchesOptions}
                    selected={form.watch("selectedBranchIds")}
                    onChange={(newSelected) => {
                      const original = inviteData.branchIds;
                      const add = newSelected.filter(
                        (id) => !original.includes(id)
                      );
                      const remove = original.filter(
                        (id) => !newSelected.includes(id)
                      );

                      form.setValue("selectedBranchIds", newSelected);
                      form.setValue("addBranchIds", add);
                      form.setValue("removeBranchIds", remove);
                    }}
                    isLoading={branchesLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-primary">Status</FormLabel>
                <FormControl>
                  <div className="flex items-center gap-4 pt-2">
                    <span className="text-muted-foreground text-sm">
                      Inactive
                    </span>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-primary"
                    />
                    <span className="text-primary text-sm">Active</span>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
        </form>
      </Form>

      <div className="flex justify-center items-center gap-4 pt-8">
        <Button
          variant="outline"
          onClick={handleCancel}
          className="bg-[#F5F6F8] hover:bg-[#F5F6F8]/80 px-8 py-2 border-primary rounded-full text-primary"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          onClick={form.handleSubmit(onSubmit)}
          className="bg-primary hover:bg-primary/90 px-8 py-2 rounded-full text-white"
        >
          Save
        </Button>
      </div>
    </div>
  );
}
