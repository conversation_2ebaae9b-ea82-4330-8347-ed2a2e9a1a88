import { fetchWithToken } from "@/lib/fetchWithToken";
import { GranularityType } from "@/lib/types";

interface BranchRanking {
  branch_id: string;
  branch_name: string;
  completion_rate: number;
}
interface TopEmployee {
  user_id: string;
  employee_name: string;
  completion_rate: number;
}
interface BranchRankings {
  best_branch: BranchRanking | null;
  worst_branch: BranchRanking | null;
  all_branches: BranchRanking[];
}
interface StoreOverview {
  completion_rate: number;
  on_time_rate: number;
  average_score: number;
  failed_items_count: number;
}
export interface StoreOverviewDashboard {
  store_overview: StoreOverview;
  branch_rankings: BranchRankings;
  top_employees: TopEmployee | null;
}

export default async function fetchStoreOverviewDashboard(
  storeId: string
): Promise<StoreOverviewDashboard> {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/analytics/stores/${storeId}/dashboard`
    );
    const data = await response.json();

    return data.data;
  } catch (err) {
    console.error(err);
  }
}

export interface BranchesList {
  branch_id: string;
  branch_name: string;
  completion_rate: number;
  pass_rate: number;
  on_time_rate: number;
  failed_questions_count: number;
}
export async function fetchBranchesList(
  storeId: string
): Promise<BranchesList[]> {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/analytics/stores/${storeId}/branches/detailed-list`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch branches list");
    }
    const data = await response.json();
    return data.data;
  } catch (err) {
    console.error(err);
  }
}

interface FailedQuestionData {
  assignment_id: string;
  template_name: string;
  question: string;
  failed_count: number;
  total_responses: number;
  failure_rate: string;
}

export async function fetchFailedQuestionsByTemplate(
  storeId: string,
  templateId: string
): Promise<FailedQuestionData[]> {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/analytics/stores/${storeId}/templates/failed-questions?templateId=${templateId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    if (!response.ok) {
      throw new Error("Failed to fetch failed questions by template");
    }
    const data = await response.json();
    return data.data;
  } catch (err) {
    console.error(err);
  }
}
interface SubmissionDistributionData {
  name: string;
  submission_count: number;
  percentage: number;
}

export async function fetchSubmissionDistribution(
  filter: "template" | "branch" | "assignment",
  storeId: string
): Promise<SubmissionDistributionData[]> {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/analytics/stores/${storeId}/submissions/distribution?type=${filter}`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch submission distribution");
    }
    const data = await response.json();
    return data.data;
  } catch (err) {
    console.error(err);
  }
}

interface AdvancedAnalyticsData {
  assignment_id: string;
  template_name: string;
  total_submissions: number;
  completion_rate: number;
  average_score: number;
  on_time_rate: number;
}

export async function fetchAdvancedAnalytics(
  branchId: string,
  templateId: string
): Promise<AdvancedAnalyticsData[]> {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/analytics/branches/${branchId}/templates/overview?templateId=${templateId}`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch advanced analytics data");
    }
    const data = await response.json();
    return data.data;
  } catch (err) {
    console.error(err);
  }
}

interface BranchSubmissionTrend {
  date: string;
  submission_count: number;
  assignment_count: number;
}
export async function fetchBranchSubmissionTrends({
  branchId,
  granularity,
  startDate,
  endDate,
}: {
  branchId: string;
  granularity: GranularityType;
  startDate?: string;
  endDate?: string;
}): Promise<BranchSubmissionTrend[]> {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/analytics/branches/${branchId}/submission-trends?granularity=${granularity}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    if (!response.ok) {
      throw new Error("Failed to fetch branch submission trends");
    }
    console.log(response);
    const data = await response.json();
    console.log(data);
    return data.data;
  } catch (err) {
    console.error(err);
  }
}

interface BranchPassRateChartData {
  date: string;
  value: number;
}

export async function fetchBranchPassRateChart({
  storeId,
  branchId,
  granularity = "monthly",
}: {
  storeId: string;
  branchId: string;
  granularity?: GranularityType;
}): Promise<BranchPassRateChartData[]> {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/analytics/stores/${storeId}/charts/completion-rate/${branchId}?granularity=${granularity}`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch branch pass rate chart");
    }
    const data = await response.json();
    return data.data;
  } catch (err) {
    console.error(err);
  }
}
