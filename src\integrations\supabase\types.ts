export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      activity_log: {
        Row: {
          activity_type: string;
          company_name: string;
          created_at: string | null;
          date: string | null;
          id: string;
          new_data: J<PERSON> | null;
          old_data: Json | null;
          performer_by: string;
          record_id: string | null;
          table_name: string;
        };
        Insert: {
          activity_type: string;
          company_name: string;
          created_at?: string | null;
          date?: string | null;
          id?: string;
          new_data?: Json | null;
          old_data?: Json | null;
          performer_by?: string;
          record_id?: string | null;
          table_name: string;
        };
        Update: {
          activity_type?: string;
          company_name?: string;
          created_at?: string | null;
          date?: string | null;
          id?: string;
          new_data?: Json | null;
          old_data?: Json | null;
          performer_by?: string;
          record_id?: string | null;
          table_name?: string;
        };
        Relationships: [
          {
            foreignKeyName: "activity_log_performer_by_fkey";
            columns: ["performer_by"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      branches: {
        Row: {
          branch_manager_id: string | null;
          created_at: string;
          id: string;
          latitude: number | null;
          location: string | null;
          longitude: number | null;
          status: Database["public"]["Enums"]["branch_status"];
          store_id: string;
          updated_at: string;
        };
        Insert: {
          branch_manager_id?: string | null;
          created_at?: string;
          id?: string;
          latitude?: number | null;
          location?: string | null;
          longitude?: number | null;
          status?: Database["public"]["Enums"]["branch_status"];
          store_id: string;
          updated_at?: string;
        };
        Update: {
          branch_manager_id?: string | null;
          created_at?: string;
          id?: string;
          latitude?: number | null;
          location?: string | null;
          longitude?: number | null;
          status?: Database["public"]["Enums"]["branch_status"];
          store_id?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "branches_branch_manager_id_users_id_fk";
            columns: ["branch_manager_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "branches_store_id_stores_id_fk";
            columns: ["store_id"];
            isOneToOne: false;
            referencedRelation: "stores";
            referencedColumns: ["id"];
          }
        ];
      };
      checklist_assignments: {
        Row: {
          assigned_by: string;
          assigned_to: string | null;
          branch_id: string;
          created_at: string;
          due_date: string | null;
          id: string;
          status: Database["public"]["Enums"]["response_status"];
          template_id: string;
          updated_at: string;
        };
        Insert: {
          assigned_by: string;
          assigned_to?: string | null;
          branch_id: string;
          created_at?: string;
          due_date?: string | null;
          id?: string;
          status?: Database["public"]["Enums"]["response_status"];
          template_id: string;
          updated_at?: string;
        };
        Update: {
          assigned_by?: string;
          assigned_to?: string | null;
          branch_id?: string;
          created_at?: string;
          due_date?: string | null;
          id?: string;
          status?: Database["public"]["Enums"]["response_status"];
          template_id?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "checklist_assignments_assigned_by_users_id_fk";
            columns: ["assigned_by"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "checklist_assignments_assigned_to_users_id_fk";
            columns: ["assigned_to"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "checklist_assignments_branch_id_branches_id_fk";
            columns: ["branch_id"];
            isOneToOne: false;
            referencedRelation: "branches";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "checklist_assignments_template_id_checklist_templates_id_fk";
            columns: ["template_id"];
            isOneToOne: false;
            referencedRelation: "checklist_templates";
            referencedColumns: ["id"];
          }
        ];
      };
      checklist_fields: {
        Row: {
          checklist_section_id: string;
          created_at: string;
          hint: string | null;
          id: string;
          order: number;
          question: string | null;
          question_type: Database["public"]["Enums"]["question_type"];
          required: boolean;
          requires_evidence: boolean;
          score: number | null;
          updated_at: string;
        };
        Insert: {
          checklist_section_id: string;
          created_at?: string;
          hint?: string | null;
          id?: string;
          order?: number;
          question?: string | null;
          question_type: Database["public"]["Enums"]["question_type"];
          required?: boolean;
          requires_evidence?: boolean;
          score?: number | null;
          updated_at?: string;
        };
        Update: {
          checklist_section_id?: string;
          created_at?: string;
          hint?: string | null;
          id?: string;
          order?: number;
          question?: string | null;
          question_type?: Database["public"]["Enums"]["question_type"];
          required?: boolean;
          requires_evidence?: boolean;
          score?: number | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "checklist_fields_checklist_section_id_checklist_sections_id_fk";
            columns: ["checklist_section_id"];
            isOneToOne: false;
            referencedRelation: "checklist_sections";
            referencedColumns: ["id"];
          }
        ];
      };
      checklist_response_fields: {
        Row: {
          answer: string | null;
          checklist_assignments_id: string;
          checklist_field_id: string;
          checklist_section_id: string;
          created_at: string;
          description: string | null;
          id: string;
          score: number | null;
          status: Database["public"]["Enums"]["field_status"];
          updated_at: string;
        };
        Insert: {
          answer?: string | null;
          checklist_assignments_id: string;
          checklist_field_id: string;
          checklist_section_id: string;
          created_at?: string;
          description?: string | null;
          id?: string;
          score?: number | null;
          status?: Database["public"]["Enums"]["field_status"];
          updated_at?: string;
        };
        Update: {
          answer?: string | null;
          checklist_assignments_id?: string;
          checklist_field_id?: string;
          checklist_section_id?: string;
          created_at?: string;
          description?: string | null;
          id?: string;
          score?: number | null;
          status?: Database["public"]["Enums"]["field_status"];
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "checklist_response_fields_checklist_assignments_id_checklist_as";
            columns: ["checklist_assignments_id"];
            isOneToOne: false;
            referencedRelation: "checklist_assignments";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "checklist_response_fields_checklist_field_id_checklist_fields_i";
            columns: ["checklist_field_id"];
            isOneToOne: false;
            referencedRelation: "checklist_fields";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "checklist_response_fields_checklist_section_id_checklist_sectio";
            columns: ["checklist_section_id"];
            isOneToOne: false;
            referencedRelation: "checklist_sections";
            referencedColumns: ["id"];
          }
        ];
      };
      checklist_responses: {
        Row: {
          branch_id: string;
          checklist_assignments_id: string;
          completed_at: string | null;
          created_at: string;
          id: string;
          latitude: number | null;
          longitude: number | null;
          score: number | null;
          submitted_at: string | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          branch_id: string;
          checklist_assignments_id: string;
          completed_at?: string | null;
          created_at?: string;
          id?: string;
          latitude?: number | null;
          longitude?: number | null;
          score?: number | null;
          submitted_at?: string | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          branch_id?: string;
          checklist_assignments_id?: string;
          completed_at?: string | null;
          created_at?: string;
          id?: string;
          latitude?: number | null;
          longitude?: number | null;
          score?: number | null;
          submitted_at?: string | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "checklist_responses_branch_id_branches_id_fk";
            columns: ["branch_id"];
            isOneToOne: false;
            referencedRelation: "branches";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "checklist_responses_checklist_assignments_id_checklist_assignme";
            columns: ["checklist_assignments_id"];
            isOneToOne: false;
            referencedRelation: "checklist_assignments";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "checklist_responses_user_id_users_id_fk";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      checklist_sections: {
        Row: {
          checklist_template_id: string;
          created_at: string;
          description: string | null;
          id: string;
          name: string;
          order: number;
          updated_at: string;
        };
        Insert: {
          checklist_template_id: string;
          created_at?: string;
          description?: string | null;
          id?: string;
          name: string;
          order?: number;
          updated_at?: string;
        };
        Update: {
          checklist_template_id?: string;
          created_at?: string;
          description?: string | null;
          id?: string;
          name?: string;
          order?: number;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "checklist_sections_checklist_template_id_checklist_templates_id";
            columns: ["checklist_template_id"];
            isOneToOne: false;
            referencedRelation: "checklist_templates";
            referencedColumns: ["id"];
          }
        ];
      };
      checklist_templates: {
        Row: {
          business_type: string | null;
          created_at: string;
          created_by: string;
          description: string | null;
          id: string;
          title: string;
          updated_at: string;
        };
        Insert: {
          business_type?: string | null;
          created_at?: string;
          created_by: string;
          description?: string | null;
          id?: string;
          title: string;
          updated_at?: string;
        };
        Update: {
          business_type?: string | null;
          created_at?: string;
          created_by?: string;
          description?: string | null;
          id?: string;
          title?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "checklist_templates_created_by_users_id_fk";
            columns: ["created_by"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      media: {
        Row: {
          checklist_response_fields_id: string | null;
          created_at: string;
          id: string;
          name: string | null;
          updated_at: string;
          url: string | null;
        };
        Insert: {
          checklist_response_fields_id?: string | null;
          created_at?: string;
          id?: string;
          name?: string | null;
          updated_at?: string;
          url?: string | null;
        };
        Update: {
          checklist_response_fields_id?: string | null;
          created_at?: string;
          id?: string;
          name?: string | null;
          updated_at?: string;
          url?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "media_checklist_response_fields_id_checklist_response_fields_id";
            columns: ["checklist_response_fields_id"];
            isOneToOne: false;
            referencedRelation: "checklist_response_fields";
            referencedColumns: ["id"];
          }
        ];
      };
      store_employees: {
        Row: {
          branch_id: string;
          created_at: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          branch_id: string;
          created_at?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          branch_id?: string;
          created_at?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "store_employees_branch_id_branches_id_fk";
            columns: ["branch_id"];
            isOneToOne: false;
            referencedRelation: "branches";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "store_employees_user_id_users_id_fk";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      stores: {
        Row: {
          business_type: string | null;
          created_at: string;
          id: string;
          name: string;
          number_of_branches: number;
          status: Database["public"]["Enums"]["store_status"];
          store_admin_id: string | null;
          updated_at: string;
        };
        Insert: {
          business_type?: string | null;
          created_at?: string;
          id?: string;
          name: string;
          number_of_branches?: number;
          status?: Database["public"]["Enums"]["store_status"];
          store_admin_id?: string | null;
          updated_at?: string;
        };
        Update: {
          business_type?: string | null;
          created_at?: string;
          id?: string;
          name?: string;
          number_of_branches?: number;
          status?: Database["public"]["Enums"]["store_status"];
          store_admin_id?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "stores_store_admin_id_users_id_fk";
            columns: ["store_admin_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      users: {
        Row: {
          created_at: string;
          email: string;
          id: string;
          name: string;
          phone: string | null;
          role: Database["public"]["Enums"]["user_role"];
          token: string | null;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          email: string;
          id?: string;
          name: string;
          phone?: string | null;
          role?: Database["public"]["Enums"]["user_role"];
          token?: string | null;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          email?: string;
          id?: string;
          name?: string;
          phone?: string | null;
          role?: Database["public"]["Enums"]["user_role"];
          token?: string | null;
          updated_at?: string;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      get_activity_log: {
        Args: {
          company_filter?: string;
          limit_count?: number;
          offset_count?: number;
        };
        Returns: {
          id: string;
          company_name: string;
          activity_type: string;
          date: string;
          performer_by: string;
          table_name: string;
          record_id: string;
        }[];
      };
    };
    Enums: {
      branch_status: "active" | "inactive" | "archived";
      field_status: "not_yet" | "in_progress" | "success";
      question_type: "YES/NO" | "Text";
      response_status: "pending" | "in_progress" | "completed";
      store_status: "active" | "inactive" | "archived";
      user_role: "ADMIN" | "EMPLOYEE" | "STORE_MANAGER" | "AREA_MANAGER";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      branch_status: ["active", "inactive", "archived"],
      field_status: ["not_yet", "in_progress", "success"],
      question_type: ["YES/NO", "Text"],
      response_status: ["pending", "in_progress", "completed"],
      store_status: ["active", "inactive", "archived"],
      user_role: ["ADMIN", "EMPLOYEE", "STORE_MANAGER", "AREA_MANAGER"],
    },
  },
} as const;

export type Country = {
  code: string; // 2‑letter ISO code
  flag: string; // Emoji flag
  name: string; // English name (via Intl)
};
