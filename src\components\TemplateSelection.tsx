import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { fetchTemplates } from "@/data/templates";
import { useSearchParams } from "react-router-dom";
import { useState } from "react";
import { setParam } from "@/lib/utils";
import { AlertCircle } from "lucide-react";
import { useTranslation } from "react-i18next";

interface TemplateSelectionProps {
  onTemplateSelect: (templateId: string) => void;
  onCreateCustom: () => void;
  selectedTemplateId: string | null;
  setSelectedTemplateId: React.Dispatch<React.SetStateAction<string | null>>;
}

export const TemplateSelection = ({
  onTemplateSelect,
  onCreateCustom,
  selectedTemplateId,
  setSelectedTemplateId,
}: TemplateSelectionProps) => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const currentPage = parseInt(searchParams.get("page") || "1", 10);
  const ITEMS_PER_PAGE = 9;

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["templates", currentPage],
    queryFn: () => fetchTemplates({ page: currentPage, limit: ITEMS_PER_PAGE }),
  });

  const templates = data?.data ?? [];
  const totalPages = data?.totalPages ?? 1;

  return (
    <div className="mx-auto px-6 py-8 container">
      <div className="mb-8">
        <h1 className="mb-2 font-semibold text-gray-900 text-2xl">
          {t("templateSelection.assignChecklistForm")}
        </h1>
        <div className="flex gap-4">
          <Button variant="outline" className="border-blue-600 text-blue-600">
            {t("templateSelection.usePrebuiltTemplate")}
          </Button>
          <Button variant="ghost" onClick={onCreateCustom}>
            {t("templateSelection.createCustomTemplate")}
          </Button>
        </div>
      </div>

      {/* Error Feedback */}
      {isError && (
        <div className="flex items-center gap-2 bg-red-50 mb-6 p-4 border border-red-200 rounded-md text-red-700">
          <AlertCircle className="w-5 h-5 text-red-500" />
          <span>{t("templateSelection.failedToLoadTemplates")}</span>
        </div>
      )}

      {/* Loading Skeleton */}
      <div className="gap-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {isLoading
          ? Array.from({ length: ITEMS_PER_PAGE }).map((_, idx) => (
              <div
                key={idx}
                className="flex justify-center items-center bg-gray-200 p-6 rounded-md h-[100px] animate-pulse"
              >
                <div className="bg-gray-300 rounded w-3/4 h-5"></div>
              </div>
            ))
          : templates.map((template) => (
              <Card
                key={template.id}
                className={`p-6 cursor-pointer transition-all hover:shadow-md ${
                  template.id === selectedTemplateId
                    ? "bg-blue-100 border-blue-300 shadow-md"
                    : "bg-gray-100 hover:"
                }`}
                onClick={() => {
                  setSelectedTemplateId(template.id);
                }}
              >
                <div className="text-center">
                  <p className="font-medium text-gray-900">{template.title}</p>
                </div>
              </Card>
            ))}
      </div>
      {/* Continue button */}
      {!isLoading && !isError && selectedTemplateId && (
        <div className="flex justify-start my-8 w-full">
          <Button
            onClick={() => onTemplateSelect(selectedTemplateId)}
            className="bg-slate-800 hover:bg-slate-900 text-white"
          >
            {t("global.continue")}
          </Button>
        </div>
      )}
      {/* Pagination */}
      {!isLoading && !isError && (
        <div className="flex justify-center space-x-2 mt-4">
          {Array.from({ length: totalPages }, (_, i) => (
            <Button
              key={i + 1}
              size="sm"
              variant={currentPage === i + 1 ? "default" : "outline"}
              onClick={() =>
                setParam(
                  "page",
                  (i + 1).toString(),
                  searchParams,
                  setSearchParams
                )
              }
              disabled={currentPage === i + 1}
            >
              {i + 1}
            </Button>
          ))}
        </div>
      )}
    </div>
  );
};
