import { useEffect, useState } from "react";

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: "accepted" | "dismissed";
    platform: string;
  }>;
  prompt(): Promise<void>;
}

export const usePWA = () => {
  const [isInstallable, setIsInstallable] = useState(false);
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const isStandalone =
      window.matchMedia("(display-mode: standalone)").matches ||
      ("standalone" in window.navigator &&
        (window.navigator as { standalone?: boolean }).standalone) ||
      document.referrer.includes("android-app://");
    setIsInstalled(isStandalone);

    // Register service worker
    if ("serviceWorker" in navigator) {
      window.addEventListener("load", async () => {
        try {
          const registration = await navigator.serviceWorker.register("/sw.js");
          console.log(
            "PWA: Service Worker registered successfully:",
            registration.scope
          );

          // Listen for updates
          registration.addEventListener("updatefound", () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener("statechange", () => {
                if (
                  newWorker.state === "installed" &&
                  navigator.serviceWorker.controller
                ) {
                  console.log("PWA: New content available");
                  setUpdateAvailable(true);
                }
              });
            }
          });
        } catch (registrationError) {
          console.error(
            "PWA: Service Worker registration failed:",
            registrationError
          );
        }
      });
    }

    // Handle install prompt
    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      console.log("PWA: Install prompt triggered");
      e.preventDefault();
      setDeferredPrompt(e);
      setIsInstallable(true);
    };

    // Handle app installed
    const handleAppInstalled = () => {
      console.log("PWA: App installed successfully");
      setIsInstalled(true);
      setIsInstallable(false);
      setDeferredPrompt(null);
    };

    // Handle online/offline status
    const handleOnline = () => {
      console.log("PWA: App is online");
      setIsOnline(true);
    };

    const handleOffline = () => {
      console.log("PWA: App is offline");
      setIsOnline(false);
    };

    // Add event listeners
    window.addEventListener(
      "beforeinstallprompt",
      handleBeforeInstallPrompt as EventListener
    );
    window.addEventListener("appinstalled", handleAppInstalled);
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener(
        "beforeinstallprompt",
        handleBeforeInstallPrompt as EventListener
      );
      window.removeEventListener("appinstalled", handleAppInstalled);
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  const installPWA = async () => {
    if (!deferredPrompt) {
      console.log("PWA: No install prompt available");
      return false;
    }

    try {
      console.log("PWA: Showing install prompt");
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;

      console.log(`PWA: User choice: ${outcome}`);

      if (outcome === "accepted") {
        setDeferredPrompt(null);
        setIsInstallable(false);
        return true;
      }
      return false;
    } catch (error) {
      console.error("PWA: Install failed:", error);
      return false;
    }
  };

  const updateApp = () => {
    if (navigator.serviceWorker && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({ type: "SKIP_WAITING" });
      window.location.reload();
    }
  };

  const shareContent = async (data: {
    title?: string;
    text?: string;
    url?: string;
  }) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: data.title || "Y-Verify",
          text:
            data.text ||
            "Check out Y-Verify - Branch Report & Audit Management System",
          url: data.url || window.location.href,
        });
        return true;
      } catch (error) {
        console.error("PWA: Share failed:", error);
        return false;
      }
    }
    return false;
  };

  const getInstallInstructions = () => {
    const userAgent = navigator.userAgent.toLowerCase();

    if (userAgent.includes("chrome") && !userAgent.includes("edge")) {
      return "Chrome: Click the install button in the address bar or menu → 'Install Y-Verify'";
    } else if (userAgent.includes("firefox")) {
      return "Firefox: Menu → 'Install this site as an app'";
    } else if (userAgent.includes("safari")) {
      return "Safari: Share button → 'Add to Home Screen'";
    } else if (userAgent.includes("edge")) {
      return "Edge: Menu → 'Apps' → 'Install Y-Verify'";
    }

    return "Look for 'Add to Home Screen' or 'Install App' in your browser menu";
  };

  return {
    isInstallable,
    isInstalled,
    isOnline,
    updateAvailable,
    installPWA,
    updateApp,
    shareContent,
    getInstallInstructions,
    canShare: !!navigator.share,
  };
};
