import { Feature } from "@/data/permissions";

// Map routes to required permissions
export const routePermissionMap: Record<string, Feature> = {
  // Dashboard/Home
  "/": "access_dashboard",

  // Reports
  "/reporting-dashboard": "view_reports",
  "/advanced-reporting": "view_reports",
  "/assignment-response/history": "view_assignment_responses",
  "/assignment-response/response": "view_assignment_responses",

  // Templates
  "/templates": "view_templates",
  "/templates/new": "crud_templates",
  "/templates/edit": "crud_templates",

  // Assignments
  "/assignments": "view_checklist_assignments",
  "/assignments/new": "crud_checklist_assignments",
  "/assignments/edit": "crud_checklist_assignments",

  // Assignment Response (Submit) - order matters, more specific first
  "/assignment-response/edit": "submit_checklist",
  "/assignment-response": "submit_checklist",
  "/response-successful": "submit_checklist",

  // Calendar
  "/calendar": "access_calendar_view",

  // Store/Company management
  "/companies": "view_stores",
  "/companies/new": "crud_stores",
  "/companies/edit": "crud_stores",

  // Branch management
  "/companies/branches": "view_branches",
  "/companies/branches/new": "crud_branches",
  "/companies/branches/edit": "crud_branches",

  // User management and invites
  "/user-management": "user_invite",
  "/invite-user": "user_invite",
  "/edit-invite": "user_invite",

  // Role and permission matrix - admin only
  "/roles": "role_permission_matrix",

  // Settings - admin and store manager only
  "/settings": "view_modify_settings",

  // Audit log
  "/audit-log": "access_dashboard",

  // Branch Report System
  "/report": "view_reports",
  "/report-submission": "view_reports",
  "/report/edit": "view_reports",

  // My Assignments (Maintainer)
  "/my-assignments": "view_reports",
};

// Get required permission for a route
export const getRequiredPermission = (pathname: string): Feature | null => {
  // Check exact matches first
  if (routePermissionMap[pathname]) {
    return routePermissionMap[pathname];
  }

  // Check pattern matches for dynamic routes
  for (const [route, permission] of Object.entries(routePermissionMap)) {
    if (route.includes(":") || route.includes("*")) {
      // Convert route pattern to regex
      const pattern = route
        .replace(/:[^/]+/g, "[^/]+") // Replace :param with regex
        .replace(/\*/g, ".*"); // Replace * with regex

      if (new RegExp(`^${pattern}$`).test(pathname)) {
        return permission;
      }
    } else if (pathname.startsWith(route) && route !== "/") {
      // For routes that start with the pattern (but not root)
      return permission;
    }
  }

  return null;
};
