<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Y-Verify - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .offline-container {
            text-align: center;
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 1rem;
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            background: #ff6b6b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }
        
        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .retry-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .retry-btn:hover {
            background: #5a67d8;
        }
        
        .features-list {
            text-align: left;
            margin: 1.5rem 0;
            padding-left: 1rem;
        }
        
        .features-list li {
            color: #666;
            margin-bottom: 0.5rem;
            position: relative;
        }
        
        .features-list li:before {
            content: "✓";
            color: #4ade80;
            font-weight: bold;
            position: absolute;
            left: -1rem;
        }
        
        @media (max-width: 480px) {
            .offline-container {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1>You're Offline</h1>
        <p>It looks like you've lost your internet connection. Don't worry - Y-Verify works offline too!</p>
        
        <div class="features-list">
            <h3 style="margin-bottom: 0.5rem; color: #333;">What you can still do:</h3>
            <ul style="list-style: none;">
                <li>View cached reports and assignments</li>
                <li>Work on offline forms</li>
                <li>Browse previously loaded data</li>
            </ul>
        </div>
        
        <p style="font-size: 0.9rem;">Your changes will sync automatically once you're back online.</p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            Try Again
        </button>
    </div>

    <script>
        // Auto-retry when online
        window.addEventListener('online', () => {
            window.location.reload();
        });
        
        // Show online/offline status
        function updateOnlineStatus() {
            const isOnline = navigator.onLine;
            if (isOnline) {
                window.location.reload();
            }
        }
        
        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);
    </script>
</body>
</html>
