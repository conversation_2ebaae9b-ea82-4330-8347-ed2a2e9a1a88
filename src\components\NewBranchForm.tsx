import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { z } from "zod";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { fetchCompanies, createBranch, updateBranch } from "@/data/branches";
import { Branch, FetchedBranch } from "@/lib/types";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { LocationPickerModal } from "@/components/LocationPickerModal";
import { usePlaceDetails } from "@/hooks/usePlaceDetails";
import { useDateFormatter } from "@/hooks/useLocalizationSettings";
import { MapPin, Search, Star } from "lucide-react";

// Zod schema for form validation
const newBranchSchema = z.object({
  storeId: z.string(),
  location: z.string().optional(),
  latitude: z.string().optional(),
  longitude: z.string().optional(),
  branchManagerEmail: z.string().email("Please enter a valid email address"),
  country: z.string().optional(),
  status: z.enum(["active", "inactive", "archived"]).default("active"),
});

export type NewBranchFormData = z.infer<typeof newBranchSchema>;

export function NewBranchForm({ branch }: { branch?: FetchedBranch }) {
  const { t } = useTranslation();
  const { formatDate } = useDateFormatter();
  const [isMapModalOpen, setIsMapModalOpen] = useState(false);
  const [selectedMapLocation, setSelectedMapLocation] = useState<{
    lat: number;
    lng: number;
    address?: string;
    placeDetails?: google.maps.places.PlaceResult;
  } | null>(null);
  const { details, error: placeError, fetchDetails } = usePlaceDetails();

  const {
    data: companiesResult,
    isLoading: companiesLoading,
    error: companiesError,
  } = useQuery({
    queryKey: ["companies"],
    queryFn: fetchCompanies,
    staleTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: 1000,
  });
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const form = useForm<NewBranchFormData>({
    resolver: zodResolver(newBranchSchema),
    defaultValues: {
      storeId: undefined,
      location: "",
      latitude: undefined,
      longitude: undefined,
      branchManagerEmail: "",
      status: "active",
    },
  });

  useEffect(() => {
    if (branch) {
      form.reset({
        storeId: branch.storeId,
        location: branch.location ?? "",
        latitude: branch.latitude ?? "",
        longitude: branch.longitude ?? "",
        branchManagerEmail: branch.managerEmail ?? "",
        status: branch.status ?? "active",
      });

      // Set initial map location if coordinates exist
      if (branch.latitude && branch.longitude) {
        setSelectedMapLocation({
          lat: parseFloat(branch.latitude),
          lng: parseFloat(branch.longitude),
          address: branch.location ?? undefined,
        });
      }
    }
  }, [branch, form]);

  // Updated to handle place details with ratings and reviews
  const handleMapLocationSelect = (location: {
    lat: number;
    lng: number;
    address?: string;
    placeDetails?: google.maps.places.PlaceResult;
  }) => {
    setSelectedMapLocation(location);

    // Update form fields
    if (location.address) {
      form.setValue("location", location.address);
    }
    form.setValue("latitude", location.lat.toString());
    form.setValue("longitude", location.lng.toString());

    // Log place details for debugging
    if (location.placeDetails) {
      console.log("Place details received:", location.placeDetails);
    }
  };

  const openMapPicker = () => {
    setIsMapModalOpen(true);
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setSelectedMapLocation(location);
          form.setValue("latitude", location.lat.toString());
          form.setValue("longitude", location.lng.toString());

          // Reverse geocode to get address
          const geocoder = new google.maps.Geocoder();
          geocoder.geocode({ location }, (results, status) => {
            if (status === "OK" && results && results[0]) {
              const address = results[0].formatted_address;
              form.setValue("location", address);
              setSelectedMapLocation((prev) =>
                prev ? { ...prev, address } : null
              );
            }
          });
        },
        (error) => {
          toast.error("Unable to get your current location");
        }
      );
    } else {
      toast.error("Geolocation is not supported by this browser");
    }
  };

  const createStoreMutation = useMutation({
    mutationFn: async (formData: NewBranchFormData) => {
      return await createBranch(formData);
    },
    onSuccess: (_, variables) => {
      toast.success(t("branch.createSuccess"));
      queryClient.invalidateQueries({ queryKey: ["branches"] });
      navigate(`/companies/${variables.storeId}/branches`);
    },
    onError: (error) => {
      console.error(error);
      toast.error(error.message || t("branch.createError"));
    },
  });

  const updateStoreMutation = useMutation({
    mutationFn: async (formData: NewBranchFormData) => {
      return await updateBranch(branch?.id, formData);
    },
    onSuccess: (_, variables) => {
      toast.success(t("branch.updateSuccess"));
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === "branches" ||
          (query.queryKey[0] === "branch" && query.queryKey[1] === branch?.id),
      });
      navigate(`/companies/${variables.storeId}/branches`);
    },
    onError: (error) => {
      toast.error(error.message || t("branch.updateError"));
    },
  });

  const onSubmit = async (data: NewBranchFormData) => {
    const result = branch
      ? await updateStoreMutation.mutateAsync(data)
      : await createStoreMutation.mutateAsync(data);
    console.log(result);
  };

  const companies = companiesResult?.data || [];

  if (companiesError) {
    console.error(companiesError);
  }

  const getInitialMapLocation = () => {
    if (selectedMapLocation) return selectedMapLocation;
    if (branch?.latitude && branch?.longitude) {
      return {
        lat: parseFloat(branch.latitude),
        lng: parseFloat(branch.longitude),
      };
    }
    return undefined;
  };

  // Helper function to render stars
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="fill-yellow-400 w-4 h-4 text-yellow-400" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Star
          key="half"
          className="fill-yellow-400/50 w-4 h-4 text-yellow-400"
        />
      );
    }

    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-4 h-4 text-gray-300" />);
    }

    return stars;
  };

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
            {/* Store Id Select */}
            <FormField
              control={form.control}
              name="storeId"
              render={({ field }) => (
                <FormItem key={field.value}>
                  <FormLabel className="font-medium text-slate-700 text-sm">
                    {t("branch.selectStore")}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    key={field.value}
                    {...field}
                  >
                    <FormControl>
                      <SelectTrigger className="min-w-[180px]" ref={field.ref}>
                        <SelectValue placeholder={t("branch.selectStore")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {companiesLoading ? (
                        <SelectItem value="null">
                          {t("common.loading")}
                        </SelectItem>
                      ) : (
                        companies.map((company) => (
                          <SelectItem key={company.id} value={company.id}>
                            {company.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Assign admin email */}
            <FormField
              control={form.control}
              name="branchManagerEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-slate-700 text-sm">
                    {t("branch.branchManagerEmail")}
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      className="border-slate-200"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="gap-6 grid grid-cols-1">
            {/* Location with Map Picker */}
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-slate-700 text-sm">
                    {t("branch.location")}
                  </FormLabel>
                  <FormControl>
                    <div className="space-y-3">
                      <Input
                        {...field}
                        className="border-slate-200"
                        placeholder="Location address will appear here..."
                        readOnly
                      />

                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={openMapPicker}
                          className="flex items-center gap-2"
                        >
                          <MapPin className="w-4 h-4" />
                          Select on Map
                        </Button>

                        <Button
                          type="button"
                          variant="outline"
                          onClick={getCurrentLocation}
                          className="flex items-center gap-2"
                        >
                          <Search className="w-4 h-4" />
                          Use Current Location
                        </Button>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Status */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-slate-700 text-sm">
                    {t("branch.status")}:
                  </FormLabel>
                  <FormControl>
                    <div className="flex items-center space-x-4 pt-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-slate-500 text-sm">
                          {t("branch.inactive")}
                        </span>
                        <Switch
                          checked={field.value === "active"}
                          onCheckedChange={(val) => {
                            field.onChange(val ? "active" : "inactive");
                          }}
                          className="data-[state=checked]:bg-slate-800"
                        />
                        <span className="font-medium text-slate-900 text-sm">
                          {t("branch.active")}
                        </span>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
            {/* Longitude */}
            <FormField
              control={form.control}
              name="longitude"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-slate-700 text-sm">
                    {t("branch.longitude")}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} className="border-slate-200" readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Latitude */}
            <FormField
              control={form.control}
              name="latitude"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-slate-700 text-sm">
                    {t("branch.latitude")}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} className="border-slate-200" readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Enhanced Location Preview with Place Details */}
          {selectedMapLocation && (
            <div className="space-y-4 p-4 rounded-lg">
              <h3 className="font-medium text-slate-800">Selected Location</h3>

              {selectedMapLocation.address && (
                <p className="text-slate-600 text-sm">
                  <strong>Address:</strong> {selectedMapLocation.address}
                </p>
              )}

              <p className="text-slate-600 text-sm">
                <strong>Coordinates:</strong>{" "}
                {selectedMapLocation.lat.toFixed(6)},{" "}
                {selectedMapLocation.lng.toFixed(6)}
              </p>

              {/* Place Details Section */}
              {selectedMapLocation.placeDetails && (
                <div className="pt-3 border-slate-200 border-t">
                  <h4 className="mb-3 font-semibold text-slate-800 text-sm">
                    Business Information
                  </h4>

                  {selectedMapLocation.placeDetails.name && (
                    <div className="mb-2">
                      <span className="font-medium text-slate-700">Name: </span>
                      <span className="text-slate-600">
                        {selectedMapLocation.placeDetails.name}
                      </span>
                    </div>
                  )}

                  {selectedMapLocation.placeDetails.rating && (
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium text-slate-700">
                        Rating:{" "}
                      </span>
                      <div className="flex items-center gap-1">
                        {renderStars(selectedMapLocation.placeDetails.rating)}
                        <span className="ml-1 text-slate-600">
                          {selectedMapLocation.placeDetails.rating} (
                          {selectedMapLocation.placeDetails
                            .user_ratings_total || 0}{" "}
                          reviews)
                        </span>
                      </div>
                    </div>
                  )}

                  {selectedMapLocation.placeDetails.opening_hours && (
                    <div className="mb-2">
                      <span className="font-medium text-slate-700">
                        Status:{" "}
                      </span>
                      <span
                        className={`text-sm ${
                          selectedMapLocation.placeDetails.opening_hours
                            .open_now
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {selectedMapLocation.placeDetails.opening_hours.open_now
                          ? "Open now"
                          : "Closed"}
                      </span>
                    </div>
                  )}

                  {selectedMapLocation.placeDetails.price_level !==
                    undefined && (
                    <div className="mb-3">
                      <span className="font-medium text-slate-700">
                        Price Level:{" "}
                      </span>
                      <span className="text-slate-600">
                        {"$".repeat(
                          selectedMapLocation.placeDetails.price_level || 1
                        )}{" "}
                        (
                        {selectedMapLocation.placeDetails.price_level === 0
                          ? "Free"
                          : selectedMapLocation.placeDetails.price_level === 1
                          ? "Inexpensive"
                          : selectedMapLocation.placeDetails.price_level === 2
                          ? "Moderate"
                          : selectedMapLocation.placeDetails.price_level === 3
                          ? "Expensive"
                          : "Very Expensive"}
                        )
                      </span>
                    </div>
                  )}

                  {selectedMapLocation.placeDetails.reviews &&
                    selectedMapLocation.placeDetails.reviews.length > 0 && (
                      <div>
                        <span className="font-medium text-slate-700">
                          Recent Reviews:
                        </span>
                        <div className="space-y-3 mt-2 max-h-40 overflow-y-auto">
                          {selectedMapLocation.placeDetails.reviews
                            .slice(0, 2)
                            .map((review, index) => (
                              <div
                                key={index}
                                className="p-3 border border-slate-200 rounded"
                              >
                                <div className="flex justify-between items-center mb-1">
                                  <span className="font-medium text-slate-800 text-sm">
                                    {review.author_name}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    {renderStars(review.rating)}
                                    <span className="ml-1 text-slate-500 text-sm">
                                      {review.rating}
                                    </span>
                                  </div>
                                </div>
                                <p className="text-slate-600 text-sm line-clamp-2 leading-relaxed">
                                  {review.text}
                                </p>
                                {review.time && (
                                  <p className="mt-1 text-slate-400 text-xs">
                                    {formatDate(new Date(review.time * 1000))}
                                  </p>
                                )}
                              </div>
                            ))}
                        </div>
                      </div>
                    )}
                </div>
              )}
            </div>
          )}

          {/* Form actions */}
          <div className="flex justify-end items-center space-x-4 pt-8">
            <Button
              type="button"
              variant="outline"
              className="px-8 py-2 border-slate-200 text-slate-600 hover:"
              onClick={() => {
                form.reset();
                setSelectedMapLocation(null);
              }}
            >
              {t("branch.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={
                createStoreMutation.isPending || updateStoreMutation.isPending
              }
              className="bg-slate-800 hover:bg-slate-900 px-8 py-2 text-white"
            >
              {createStoreMutation.isPending
                ? t("branch.creating")
                : updateStoreMutation.isPending
                ? t("branch.updating")
                : branch
                ? t("branch.update")
                : t("branch.create")}
            </Button>
          </div>
        </form>
      </Form>

      {/* Location Picker Modal */}
      <LocationPickerModal
        isOpen={isMapModalOpen}
        onClose={() => setIsMapModalOpen(false)}
        onLocationSelect={handleMapLocationSelect}
        initialLocation={getInitialMapLocation()}
      />
    </>
  );
}
