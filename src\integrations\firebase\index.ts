// src/integrations/firebase/index.ts
import { initializeApp } from "firebase/app";
import { getMessaging, getToken, onMessage, isSupported } from "firebase/messaging";
import { firebaseConfig } from "./config";

const firebaseApp = initializeApp(firebaseConfig);
console.log(firebaseApp);
export const messagingPromise = isSupported().then(supported => supported ? getMessaging(firebaseApp) : null);
export { getToken, onMessage };
