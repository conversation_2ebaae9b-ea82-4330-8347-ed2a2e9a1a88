import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  fetchChurnRate,
  fetchClientsCount,
  fetchTemplatesCount,
  fetchUsersCount,
} from "@/data/dashboard";
import { useQuery } from "@tanstack/react-query";
import { FileText, TrendingDown, User, Users } from "lucide-react";
import { useTranslation } from "react-i18next";

// Loading skeleton component
const MetricCardSkeleton = () => (
  <Card className="p-6">
    <div className="flex justify-between items-center mb-4">
      <Skeleton className="rounded-full w-12 h-12" />
    </div>
    <div className="space-y-2">
      <Skeleton className="w-20 h-4" />
      <div className="flex justify-between items-center">
        <Skeleton className="w-16 h-8" />
        <Skeleton className="w-12 h-4" />
      </div>
    </div>
  </Card>
);

// Error component
const MetricCardError = ({ title }: { title: string }) => {
  const { t } = useTranslation();
  return (
    <Card className="p-6 border-red-200">
      <div className="flex justify-between items-center mb-4">
        <div className="bg-red-100 p-3 rounded-full">
          <TrendingDown className="w-6 h-6 text-red-700" />
        </div>
      </div>
      <div className="space-y-2">
        <p className="font-medium text-gray-600 text-sm">{title}</p>
        <div className="flex justify-between items-center">
          <p className="font-bold text-red-600 text-sm">{t("error.title")}</p>
        </div>
      </div>
    </Card>
  );
};

export function MetricsCards() {
  const { t } = useTranslation();
  // React Query hooks
  const {
    data: clients = 0,
    isLoading: clientsLoading,
    error: clientsError,
  } = useQuery({
    queryKey: ["clients-count"],
    queryFn: fetchClientsCount,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const {
    data: usersTotal = 0,
    isLoading: usersLoading,
    error: usersError,
  } = useQuery({
    queryKey: ["users-count"],
    queryFn: fetchUsersCount,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const {
    data: templates = 0,
    isLoading: templatesLoading,
    error: templatesError,
  } = useQuery({
    queryKey: ["templates-count"],
    queryFn: fetchTemplatesCount,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const {
    data: churnRate = 0,
    isLoading: churnRateLoading,
    error: churnRateError,
  } = useQuery({
    queryKey: ["churn-rate"],
    queryFn: fetchChurnRate,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const metrics = [
    {
      title: t("dashboard.stats.clients"),
      value: clients,
      changeType: "positive" as const,
      icon: Users,
      iconBg: "bg-slate-100",
      iconColor: "text-slate-700",
      isLoading: clientsLoading,
      error: clientsError,
    },
    {
      title: t("dashboard.stats.users"),
      value: usersTotal,
      changeType: "positive" as const,
      icon: User,
      iconBg: "bg-green-100",
      iconColor: "text-green-700",
      isLoading: usersLoading,
      error: usersError,
    },
    {
      title: t("dashboard.stats.activeTemplates"),
      value: templates,
      changeType: "positive" as const,
      icon: FileText,
      iconBg: "bg-yellow-100",
      iconColor: "text-yellow-700",
      isLoading: templatesLoading,
      error: templatesError,
    },
    {
      title: t("dashboard.stats.churnRate"),
      value: `${churnRate}%`,
      changeType: "negative" as const,
      icon: TrendingDown,
      iconBg: "bg-red-100",
      iconColor: "text-red-700",
      isLoading: churnRateLoading,
      error: churnRateError,
    },
  ];

  return (
    <div className="gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-8">
      {metrics.map((metric) => {
        // Show loading skeleton
        if (metric.isLoading) {
          return <MetricCardSkeleton key={metric.title} />;
        }

        // Show error state
        if (metric.error) {
          return <MetricCardError key={metric.title} title={metric.title} />;
        }

        // Show normal card
        return (
          <Card
            key={metric.title}
            className="bg-primary-bg hover:shadow-md p-6 transition-shadow"
          >
            <div className="flex justify-between items-center mb-4">
              <div className={`p-3 rounded-full ${metric.iconBg}`}>
                <metric.icon className={`w-6 h-6 ${metric.iconColor}`} />
              </div>
            </div>
            <div className="space-y-2">
              <p className="font-medium text-gray-600 text-sm">
                {metric.title}
              </p>
              <div className="flex justify-between items-center">
                <p className="font-bold text-gray-900 text-2xl">
                  {metric.value}
                </p>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
}
