import {
  useQuery,
  useMutation,
  QueryClient,
  useQueryClient,
} from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "react-router-dom";

import { inviteUserToBranches } from "@/data/users";
import { fetchCompanies, fetchBranchesOfStore } from "@/data/branches";

import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { ChevronRight, Loader2 } from "lucide-react";
import { MultiSelect } from "@/components/ui/multi-select";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

const formSchema = z.object({
  email: z.string().email(),
  storeId: z.string().min(1, "Please select a store"),
  branchIds: z.array(z.string()).min(1, "Select at least one branch"),
  status: z.boolean(),
});

type FormSchema = z.infer<typeof formSchema>;

export default function InviteUser() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Fetch companies/stores
  const {
    data: companiesResult,
    isLoading: companiesLoading,
    error: companiesError,
  } = useQuery({
    queryKey: ["companies"],
    queryFn: () => fetchCompanies(),
    staleTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: 1000,
  });

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      storeId: "",
      branchIds: [],
      status: true,
    },
  });

  const selectedStoreId = form.watch("storeId");
  const email = form.watch("email");
  const branchIds = form.watch("branchIds");

  // Check if all required fields are filled
  const isFormValid = email && selectedStoreId && branchIds.length > 0;

  // Fetch branches for the selected store
  const {
    data: branchesResult,
    isLoading: branchesLoading,
    error: branchesError,
  } = useQuery({
    queryKey: ["branches", selectedStoreId],
    queryFn: () => fetchBranchesOfStore({ storeId: selectedStoreId }),
    enabled: !!selectedStoreId, // Only fetch when a store is selected
    staleTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: 1000,
  });

  const companiesOptions =
    companiesResult?.data?.map((company) => ({
      label: company.name,
      value: company.id,
    })) ?? [];

  const branchesOptions =
    branchesResult?.map((branch) => ({
      label: branch.location,
      value: branch.id,
    })) ?? [];

  const inviteMutation = useMutation({
    mutationFn: inviteUserToBranches,
    onSuccess: () => {
      navigate("/user-management");
      queryClient.invalidateQueries({
        queryKey: ["all-users"],
      });
    },
    onError: (error: Error) => {
      console.error("Error inviting user:", error);

      // Parse error message to determine which field has the error
      const errorMessage = error.message || "An unexpected error occurred";

      // Check for specific field errors based on common validation patterns
      if (errorMessage.toLowerCase().includes("email")) {
        form.setError("email", {
          type: "manual",
          message: errorMessage,
        });
      } else if (errorMessage.toLowerCase().includes("branch")) {
        form.setError("branchIds", {
          type: "manual",
          message: errorMessage,
        });
      } else if (errorMessage.toLowerCase().includes("store")) {
        form.setError("storeId", {
          type: "manual",
          message: errorMessage,
        });
      } else {
        // For general errors, set error on email field or show toast
        form.setError("root", {
          type: "manual",
          message: errorMessage,
        });
        toast.error("Failed to invite user: " + errorMessage);
      }
    },
  });

  const onSubmit = async (data: FormSchema) => {
    // Clear any existing errors before submitting
    form.clearErrors();

    // Only submit the required fields (email, branchIds, status)
    // storeId is just for UX purposes
    const submitData = {
      email: data.email,
      branchIds: data.branchIds,
      status: data.status,
    };
    await inviteMutation.mutateAsync(submitData);
  };

  // Clear branch selection when store changes
  const handleStoreChange = (storeId: string) => {
    form.setValue("storeId", storeId);
    form.setValue("branchIds", []); // Clear previously selected branches
  };

  const handleCancel = () => {
    navigate("/user-management");
  };

  return (
    <div className="space-y-6 p-8">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-lg">
        <span className="font-medium text-muted-foreground">
          {t("userManagement.title")}
        </span>
        <ChevronRight className="w-6 h-6 text-muted-foreground" />
        <span className="font-medium text-primary">
          {t("inviteUser.title")}
        </span>
      </div>

      <h2 className="font-bold text-primary text-2xl">
        {t("inviteUser.title")}
      </h2>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="gap-6 grid grid-cols-1 lg:grid-cols-2"
        >
          {/* Display root-level form errors */}
          {form.formState.errors.root && (
            <div className="lg:col-span-2 bg-red-50 p-4 border border-red-200 rounded-lg">
              <p className="font-medium text-red-800 text-sm">
                {form.formState.errors.root.message}
              </p>
            </div>
          )}

          {/* Email */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-primary">
                  {t("inviteUser.form.email.label")}
                </FormLabel>
                <FormControl>
                  <input
                    type="email"
                    {...field}
                    className="shadow-sm px-4 py-3 border border-border rounded-full w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Store Selection */}
          <FormField
            control={form.control}
            name="storeId"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-primary">
                  {t("inviteUser.form.storeId.label")}
                </FormLabel>
                <FormControl>
                  <Select
                    value={field.value}
                    onValueChange={handleStoreChange}
                    disabled={companiesLoading}
                  >
                    <SelectTrigger className="shadow-sm px-4 py-3 border border-border rounded-full w-full">
                      <SelectValue
                        placeholder={t("inviteUser.form.storeId.placeholder")}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {companiesOptions.map((company) => (
                        <SelectItem key={company.value} value={company.value}>
                          {company.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Branch Assignment - Multi Select */}
          <FormField
            control={form.control}
            name="branchIds"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-primary">
                  {t("inviteUser.form.branchIds.label")}
                </FormLabel>
                <FormControl>
                  <MultiSelect
                    options={selectedStoreId ? branchesOptions : []}
                    selected={field.value}
                    onChange={field.onChange}
                    isLoading={branchesLoading}
                    disabled={!selectedStoreId || branchesLoading}
                    placeholder={
                      !selectedStoreId
                        ? t("inviteUser.form.branchIds.selectStoreFirst")
                        : t("inviteUser.form.branchIds.placeholder")
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Status */}
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-primary">
                  {t("inviteUser.form.status.label")}
                </FormLabel>
                <FormControl>
                  <div className="flex items-center gap-4 pt-2">
                    <span className="text-muted-foreground text-sm">
                      {t("inviteUser.form.status.inactive")}
                    </span>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-primary"
                    />
                    <span className="text-primary text-sm">
                      {t("inviteUser.form.status.active")}
                    </span>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
        </form>
      </Form>

      {/* Buttons */}
      <div className="flex justify-center items-center gap-4 pt-8">
        <Button
          variant="outline"
          onClick={handleCancel}
          disabled={inviteMutation.isPending}
          className="bg-[#F5F6F8] hover:bg-[#F5F6F8]/80 disabled:opacity-50 px-8 py-2 border-primary rounded-full text-primary"
        >
          {t("inviteUser.buttons.cancel")}
        </Button>
        <Button
          type="submit"
          onClick={form.handleSubmit(onSubmit)}
          disabled={!isFormValid || inviteMutation.isPending}
          className="bg-primary hover:bg-primary/90 disabled:opacity-50 px-8 py-2 rounded-full text-white disabled:cursor-not-allowed"
        >
          {inviteMutation.isPending && (
            <Loader2 className="mr-2 w-4 h-4 animate-spin" />
          )}
          {inviteMutation.isPending
            ? t("inviteUser.buttons.saving")
            : t("inviteUser.buttons.save")}
        </Button>
      </div>
    </div>
  );
}
