import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ChevronLeft, Upload, X, FileImage } from "lucide-react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMemo, useState, useEffect } from "react";
import { CreateChecklistResponseInput } from "@/lib/types";
import {
  updateChecklistAssignmentResponse,
  fetchResponseById,
} from "@/data/response";
import {
  fetchDefaultsSettingsByUserId,
  fetchDefaultDefaultsSettings,
} from "@/data/settings";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";
import Logo from "@/components/Logo";

const EditAssignmentResponse = () => {
  const { t } = useTranslation();
  const { responseId } = useParams<{ responseId: string }>();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Query to fetch existing response data
  const {
    data: existingResponse,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["response", responseId],
    queryFn: () => fetchResponseById(responseId!),
    enabled: !!responseId,
  });

  // Fetch user's defaults settings with fallback to default settings
  const { data: userDefaultsSettings } = useQuery({
    queryKey: ["defaultsSettings"],
    queryFn: fetchDefaultsSettingsByUserId,
    retry: false,
  });

  const { data: defaultDefaultsSettings } = useQuery({
    queryKey: ["defaultDefaultsSettings"],
    queryFn: fetchDefaultDefaultsSettings,
  });

  // Get the current settings (user's or defaults)
  const currentDefaultsSettings =
    userDefaultsSettings || defaultDefaultsSettings;

  console.log("EXISTING RESPONSE: ", existingResponse);
  // Extract response data
  const response = existingResponse?.response;
  const assignmentId = response?.checklistAssignmentsId;

  const mutation = useMutation({
    mutationFn: async (formData: CreateChecklistResponseInput) => {
      return await updateChecklistAssignmentResponse(responseId!, formData);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ["assignment", assignmentId],
      });
      queryClient.invalidateQueries({
        queryKey: ["assignments"],
      });
      queryClient.invalidateQueries({
        queryKey: ["responses"],
      });
      queryClient.invalidateQueries({
        queryKey: ["response", responseId],
      });

      toast.success(t("assignmentResponse.assignmentUpdatedSuccess"));
      navigate("/response-successful");
    },
    onError: (error) => {
      toast.error(
        t("assignmentResponse.failedToUpdateAssignment") + ": " + error
      );
      console.error("Error updating assignment response:", error);
    },
  });

  // Create dynamic schema based on response data
  const formSchema = useMemo(() => {
    if (!response?.sections) return z.object({});

    const schemaFields: Record<string, z.ZodTypeAny> = {};

    response.sections.forEach((section) => {
      if (section.fields) {
        section.fields.forEach((field, index) => {
          const fieldKey = field.fieldId; // Use fieldId instead of id

          if (field.questionType === "Text") {
            schemaFields[fieldKey] = z
              .string()
              .min(1, t("assignmentResponse.fieldRequired"));
          } else if (field.questionType === "YES/NO") {
            schemaFields[fieldKey] = z.enum(["yes", "no"], {
              required_error: t("assignmentResponse.pleaseSelectOption"),
            });
          }
        });
      }
    });

    return z.object(schemaFields);
  }, [response?.sections, t]);

  type FormData = z.infer<typeof formSchema>;

  // Helper function to get default values from existing response
  const getDefaultValues = useMemo(() => {
    if (!existingResponse || !response?.sections) return {};

    const defaultValues: Record<string, string | boolean> = {};

    // Iterate through sections and fields to get the answers
    response.sections.forEach((section) => {
      if (section.fields) {
        section.fields.forEach((field) => {
          if (field.answer) {
            defaultValues[field.fieldId] = field.answer; // Use fieldId instead of id
          }
        });
      }
    });

    return defaultValues;
  }, [existingResponse, response?.sections]);

  // Initialize form with dynamic schema
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues,
  });

  // Reset form with existing response data when it loads
  useEffect(() => {
    if (existingResponse && response?.sections) {
      form.reset(getDefaultValues);
    }
  }, [existingResponse, response?.sections, form, getDefaultValues]);

  const onSubmit = async (values: FormData) => {
    if (!response?.sections || !assignmentId) return;

    // Transform the form data to match CreateChecklistResponseInput
    const answers: CreateChecklistResponseInput["answers"] = [];

    response.sections.forEach((section) => {
      if (section.fields) {
        section.fields.forEach((field) => {
          const fieldKey = field.fieldId; // Use fieldId instead of id
          const answer = values[fieldKey as keyof FormData];
          if (answer) {
            const answerData: CreateChecklistResponseInput["answers"][number] =
              {
                sectionId: section.id,
                fieldId: field.fieldId, // Use fieldId instead of id
                answer: String(answer),
                mediaIds: [], // Media editing is not supported yet
              };
            answers.push(answerData);
          }
        });
      }
    });

    const formData: CreateChecklistResponseInput = {
      assignmentId: assignmentId,
      answers: answers,
    };

    try {
      await mutation.mutateAsync(formData);
      toast(t("assignmentResponse.assignmentSubmittedSuccess"));
    } catch (error) {
      console.error("Error updating assignment:", error);
      toast(t("assignmentResponse.errorSubmittingAssignment"));
    } finally {
      queryClient.invalidateQueries({
        queryKey: ["responses"],
      });
    }
  };

  if (isLoading) {
    return (
      <div className="w-full min-h-screen">
        {/* Header */}
        <div className="px-6 py-4 border-b">
          <div className="flex justify-between items-center mx-auto animate-pulse">
            <div className="bg-slate-200 rounded w-32 h-6" />
            <div className="bg-slate-200 rounded w-16 h-8" />
          </div>
        </div>

        {/* Content */}
        <div className="mx-auto p-6 animate-pulse">
          <div className="flex items-center space-x-4 mb-6">
            <div className="bg-slate-200 rounded w-8 h-8" />
            <div className="bg-slate-200 rounded w-48 h-6" />
          </div>

          <Card className="space-y-4 mb-6 p-6">
            <div className="bg-slate-200 rounded w-64 h-8" />
            <div className="space-y-2">
              <div className="bg-slate-200 rounded w-full h-4" />
              <div className="bg-slate-200 rounded w-3/4 h-4" />
            </div>
          </Card>

          <div className="space-y-6">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="p-6">
                <div className="bg-slate-200 mb-4 rounded w-48 h-6" />
                <div className="space-y-4">
                  <div className="bg-slate-200 rounded w-full h-10" />
                  <div className="bg-slate-200 rounded w-full h-10" />
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="w-full min-h-screen">
        <div className="px-6 py-4 border-b">
          <div className="flex justify-between items-center mx-auto">
            <Link to="/assignment-response/history" className="text-primary">
              ← Back to Response History
            </Link>
          </div>
        </div>
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <h2 className="mb-4 font-semibold text-xl">
              {t("assignmentResponse.errorLoadingAssignment")}
            </h2>
            <p className="mb-4 text-gray-600">
              {error?.message || t("assignmentResponse.unknownError")}
            </p>
            <Link to="/assignment-response/history">
              <Button variant="outline">Back to Response History</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Check if user has disabled editing after submission
  if (currentDefaultsSettings?.allowEditAfterSubmission === false) {
    return (
      <div className="w-full min-h-screen">
        <div className="px-6 py-4 border-b">
          <div className="flex justify-between items-center mx-auto">
            <Link to="/assignment-response/history" className="text-primary">
              ← Back to Response History
            </Link>
          </div>
        </div>
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <h2 className="mb-4 font-semibold text-xl">
              {t("assignmentResponse.editingDisabled")}
            </h2>
            <p className="mb-4 text-gray-600">
              {t("assignmentResponse.editingDisabledMessage")}
            </p>
            <Link to="/assignment-response/history">
              <Button variant="outline">Back to Response History</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!response?.sections) {
    return (
      <div className="w-full min-h-screen">
        <div className="px-6 py-4 border-b">
          <div className="flex justify-between items-center mx-auto">
            <Link to="/assignment-response/history" className="text-primary">
              ← Back to Response History
            </Link>
          </div>
        </div>
        <div className="flex justify-center items-center min-h-96">
          <p className="text-gray-600">
            {t("assignmentResponse.assignmentNotFound")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen">
      {/* Main Content */}
      <div className="bg-gray-100 mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <Link to="/assignment-response/history">
            <Button variant="ghost" size="sm">
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </Link>
        </div>

        {/* Assignment Header */}
        <div className="mb-[1.25rem] font-bold text-[1.375rem] text-black">
          {t("assignmentResponse.editChecklistForm")}
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Assignment Sections */}
            {response.sections.map((section) => (
              <div key={section.id} className="pb-2 rounded-lg">
                <div className="flex items-center bg-[#e7eaee] mb-4 px-[0.9375rem] py-2 rounded-t-[0.75rem]">
                  <h3 className="font-medium text-gray-900 text-lg">
                    {section.name}
                  </h3>
                </div>

                {/* List Type Questions */}
                {section.fields && (
                  <ol className="space-y-6 mb-4 px-4">
                    {section.fields.map((field, index) => {
                      const fieldKey = field.fieldId; // Use fieldId instead of id
                      return (
                        <li
                          key={field.fieldId} // Use fieldId instead of id
                          className="flex items-start space-x-3"
                        >
                          <div className="flex justify-center items-center bg-blue-500 rounded-full w-6 h-6 font-medium text-white text-sm">
                            {index + 1}
                          </div>
                          <div className="flex-1">
                            <FormField
                              control={form.control}
                              name={fieldKey as keyof FormData}
                              render={({ field: formField }) => (
                                <FormItem className="space-y-3">
                                  <FormLabel className="font-medium text-gray-900 text-base leading-6">
                                    {field.question}
                                    {field.requiresEvidence && (
                                      <span className="ml-2 text-blue-600 text-sm">
                                        (
                                        {t(
                                          "assignmentResponse.evidenceRequired"
                                        )}
                                        )
                                      </span>
                                    )}
                                  </FormLabel>

                                  <FormControl>
                                    <>
                                      {field.questionType === "Text" && (
                                        <Textarea
                                          placeholder={t(
                                            "assignmentResponse.enterResponse"
                                          )}
                                          className="min-h-[100px] resize-none"
                                          {...formField}
                                        />
                                      )}

                                      {field.questionType === "YES/NO" && (
                                        <RadioGroup
                                          onValueChange={formField.onChange}
                                          value={formField.value}
                                          className="flex flex-row space-x-6"
                                        >
                                          <div className="flex items-center space-x-2">
                                            <RadioGroupItem
                                              value="yes"
                                              id={`${fieldKey}-yes`}
                                            />
                                            <Label htmlFor={`${fieldKey}-yes`}>
                                              {t("global.yes")}
                                            </Label>
                                          </div>
                                          <div className="flex items-center space-x-2">
                                            <RadioGroupItem
                                              value="no"
                                              id={`${fieldKey}-no`}
                                            />
                                            <Label htmlFor={`${fieldKey}-no`}>
                                              {t("assignmentResponse.no")}
                                            </Label>
                                          </div>
                                        </RadioGroup>
                                      )}
                                    </>
                                  </FormControl>

                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </li>
                      );
                    })}
                  </ol>
                )}
              </div>
            ))}

            <div className="flex justify-end pt-6">
              <Button
                type="submit"
                disabled={mutation.isPending}
                className="bg-blue-600 hover:bg-blue-700 px-8 py-2 rounded-lg text-white"
              >
                {mutation.isPending
                  ? t("assignmentResponse.submitting")
                  : t("global.update")}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default EditAssignmentResponse;
