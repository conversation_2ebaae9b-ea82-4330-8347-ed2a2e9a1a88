import { Activity } from "@/lib/types";
import { Client } from "@/components/ClientsChart";
import { PaginatedResponse, Store } from "@/components/ClientSnapshot";
import { ClientSnapshot, ActivityLog } from "@/lib/types";
import { fetchWithToken } from "@/lib/fetchWithToken";

// Query functions
export const fetchClientsCount = async (): Promise<number> => {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/stores/count`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch data: " + response.statusText);
    }
    const data = (await response.json()).data;
    return data.total;
  } catch (err) {
    console.error(err || err.message || "Failed to fetch data");
  }
  return 0;
};
export const fetchUsersCount = async (): Promise<number> => {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/users/count`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch data: " + response.statusText);
    }
    const data = (await response.json()).data;
    return data.total;
  } catch (err) {
    console.error(err || err.message || "Failed to fetch data");
  }
  return 0;
};
export const fetchTemplatesCount = async (): Promise<number> => {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/templates/count`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch data: " + response.statusText);
    }
    const data = (await response.json()).data;
    return data.total;
  } catch (err) {
    console.error(err || err.message || "Failed to fetch data");
  }
  return 0;
};
export const fetchChurnRate = async (): Promise<number> => {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/stores/churn-rate`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch data: " + response.statusText);
    }
    const res = await response.json();
    return res.data.churnRate;
  } catch (err) {
    console.error(err || err.message || "Failed to fetch data");
  }
  return 0;
};
export const fetchClientSnapshot = async (
  page: number,
  limit: number
): Promise<PaginatedResponse> => {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/stores?page=${page}&limit=${limit}`
    );
    const data = await response.json();
    const clients: ClientSnapshot[] = data?.data.stores?.map(
      (store: Store) => ({
        id: store.id,
        name: store.name,
        numberOfBranches: store.numberOfBranches,
      })
    );
    return {
      clients,
      pagination: data.pagination,
    };
  } catch (err) {
    console.error("🚨 Fetch error:", err);
    throw err;
  }
}; // Query functions
export const fetchClientsChartData = async (
  timePeriod: string
): Promise<Partial<Client[]>> => {
  const timePeriodFilter =
    timePeriod === "6M"
      ? new Date(new Date().setMonth(new Date().getMonth() - 6)).toISOString()
      : timePeriod === "1Y"
      ? new Date(
          new Date().setFullYear(new Date().getFullYear() - 1)
        ).toISOString()
      : timePeriod === "2Y"
      ? new Date(
          new Date().setFullYear(new Date().getFullYear() - 2)
        ).toISOString()
      : null;
  if (!timePeriodFilter) {
    throw new Error("Invalid time period");
  }
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/stores/time-period?timePeriodFilter=${timePeriodFilter}`
  );

  if (!response.ok) {
    throw new Error("Failed to fetch data");
  }

  const data = await response.json();

  return data.data.stores || [];
};
export const fetchActivityLog = async (): Promise<Activity[]> => {
  try {
    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/activity-log?page=1&limit=10`
    );

    const { activityLogs, total } = (await response.json()).data;
    const activities = activityLogs.map((row: ActivityLog) => ({
      company: row.companyName,
      activity:
        row.activityType === "checklist_assignment_created"
          ? "Created checklist"
          : row.activityType === "checklist_assignment_updated"
          ? "Updated checklist"
          : row.activityType === "checklist_assignment_deleted"
          ? "Deleted checklist"
          : row.activityType || "Unknown activity",
      date: new Date(row.date).toLocaleString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      }),
      performerBy: row.userName || row.performerBy || "N/A",
    }));
    return activities || [];
  } catch (err) {
    console.error("🚨 Fetch error:", err);
    throw err;
  }
};
