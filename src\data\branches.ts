import { ITEMS_PER_PAGE } from "@/components/BranchTable";
import { NewBranchFormData } from "@/components/NewBranchForm";
import { fetchWithToken } from "@/lib/fetchWithToken";
import { supabase } from "@/lib/supabase";
import { Branch, Filter } from "@/lib/types";

// API functions
export const fetchCompanies = async () => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/stores/companies-management`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch data");
  }

  const data = await response.json();

  const result =
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data?.data.stores.map((row: any) => ({
      id: row.id,
      name: row.name,
      business_type: row.businessType,
      number_of_branches: row.numberOfBranches,
      store_admin: row.email,
      status: row.status === "active",
      created_at: row.createdAt,
    })) || [];

  return {
    data: result,
  };
};
export async function createBranch(data: NewBranchFormData) {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/branches`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    const data = await response.json();
    throw new Error("Failed to create branch: " + data.message);
  }

  return response.json();
}
export async function updateBranch(branchId: string, data: NewBranchFormData) {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/branches/${branchId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to update branch: " + (await response.text()));
  }

  return response.json();
}
// Function to fetch all branches of a store
export const fetchBranches = async (
  filters: Filter[],
  page: number,
  storeId: string
) => {
  let queryParameter = `?page=${page}&limit=${ITEMS_PER_PAGE}&storeId=${storeId}`;
  // Apply filters
  filters.forEach((filter) => {
    switch (filter.field) {
      case "created_at": {
        // Assuming the filter value is a date string, we'll filter by date
        queryParameter += `&created_at=${filter.value}`;
        break;
      }
      case "status": {
        queryParameter += `&status=${filter.value}`;
        break;
      }
    }
  });

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/branches${queryParameter}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch data");
  }

  const data = await response.json();

  let result = data.data.branches;

  // Apply client-side filtering for store_admin_email
  const emailFilter = filters.find((f) => f.field === "store_admin_email");
  if (emailFilter) {
    result = result.filter((branch) =>
      branch.managerEmail
        ?.toLowerCase()
        .includes((emailFilter.value as string).toLowerCase())
    );
  }

  const total = Number(data.pagination.total) || 0;
  const totalPages = Math.ceil(total / ITEMS_PER_PAGE);

  return {
    data: result,
    total,
    totalPages,
  };
};
// Function to mutate status of a branch
export const mutateStatus = async ({
  id,
  value,
}: {
  id: string;
  value: boolean;
}) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/branches/${id}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        status: value ? "active" : "inactive",
      }),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to update store status");
  }

  return;
};
export const fetchBranchesOfStore = async ({
  storeId,
  status,
  location,
  page = 1,
  limit = 10,
}: {
  storeId: string;
  status?: string;
  location?: string;
  page?: number;
  limit?: number;
}): Promise<Branch[]> => {
  try {
    const searchParams = new URLSearchParams();
    searchParams.set("page", page.toString());
    searchParams.set("limit", limit.toString());
    searchParams.set("storeId", storeId);
    if (status !== undefined) {
      searchParams.set("status", status);
    }
    if (location !== undefined) {
      searchParams.set("location", location);
    }

    const response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/branches?${searchParams.toString()}`
    );

    const data = await response.json();
    const { branches } = data.data;
    return branches || [];
  } catch (err) {
    console.error("🚨 Fetch error:", err);
    throw err;
  }
};

export const fetchAllBranches = async () => {
  // Fetch all stores first
  const { data: stores } = await fetchCompanies();
  const storeIds: string[] = stores.map((store: { id: string }) => store.id);
  // Then fetch all branches for each store
  const storesWithBranches = await Promise.all(
    stores.map(async (store: { id: string; name: string }) => ({
      storeId: store.id,
      branches: await fetchBranchesOfStore({ storeId: store.id }),
      storeName: store.name,
    }))
  );
  const flatBranches = storesWithBranches.flatMap((store) => store.branches);
  return flatBranches;
};

export const fetchBranchesWithSupabase = async () => {
  const { data, error } = await supabase.from("branches").select("*");

  if (error) {
    throw new Error("Failed to fetch branches: " + error.message);
  }
  return data;
};

export const fetchBranchById = async (id: string) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/branches/${id}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch data");
  }

  const data = await response.json();

  return data.data;
};

export const deleteBranch = async (id: string) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/branches/${id}`,
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to delete branch");
  }

  return response.json();
};
