import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Filter } from "lucide-react";
import { Skeleton } from "./ui/skeleton";
import { fetchBranchesOfStore, fetchBranches } from "@/data/branches";

export function BranchesModal({
  storeId,
  status,
  location,
  storeName,
}: {
  storeId: string;
  status?: string;
  location?: string;
  storeName?: string;
}) {
  const {
    data: branches = [],
    isLoading: branchLoading,
    error: branchError,
  } = useQuery({
    queryKey: ["get-branches", storeId, status, location],
    queryFn: async () =>
      await fetchBranchesOfStore({ storeId, status, location }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: 1000,
  });

  return (
    <Card className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="font-semibold text-gray-900 text-lg">Branches</h3>
        <Button variant="outline" size="sm" className="gap-2">
          <Filter className="w-4 h-4" />
          Filters
        </Button>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-gray-200 border-b">
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                Store name
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                Location
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                Longitude
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                Latitude
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                Branch Manager
              </th>
            </tr>
          </thead>
          <tbody>
            {branchLoading ? (
              // Better loading state with multiple skeleton rows
              Array.from({ length: 3 }).map((_, index) => (
                <tr key={index} className="border-gray-100 border-b">
                  <td className="px-4 py-3">
                    <Skeleton className="w-24 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-32 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-20 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-28 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-28 h-4" />
                  </td>
                </tr>
              ))
            ) : branchError ? (
              <tr>
                <td colSpan={4} className="px-4 py-8 text-red-600 text-center">
                  Error loading branches: {String(branchError)}
                </td>
              </tr>
            ) : branches.length === 0 ? (
              <tr>
                <td colSpan={4} className="px-4 py-8 text-gray-500 text-center">
                  No branches found
                </td>
              </tr>
            ) : (
              branches.map((branch, index) => (
                <tr key={index} className="border-gray-100 border-b hover:">
                  <td className="px-4 py-3 text-gray-900 text-sm">
                    {storeName || "N/A"}
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    {branch.location}
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    {branch.latitude || "N/A"}
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    {branch.longitude || "N/A"}
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    {branch.managerEmail || "N/A"}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </Card>
  );
}
