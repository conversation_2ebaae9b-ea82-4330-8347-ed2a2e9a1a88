import React from "react";
import { GoogleMapsLoader } from "./GoogleMapsLoader";
import { MapLocationPicker } from "./MapLocationPicker";

interface LocationPickerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLocationSelect: (location: {
    lat: number;
    lng: number;
    address?: string;
  }) => void;
  initialLocation?: { lat: number; lng: number };
}

export function LocationPickerModal({
  isOpen,
  onClose,
  onLocationSelect,
  initialLocation,
}: LocationPickerModalProps) {
  if (!isOpen) return null;

  const handleLocationSelect = (location: {
    lat: number;
    lng: number;
    address?: string;
  }) => {
    onLocationSelect(location);
    onClose();
  };

  return (
    <div className="z-50 fixed inset-0 flex justify-center items-center bg-black bg-opacity-50 p-4">
      <div className="bg-primary-bg rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="p-6">
          <h2 className="mb-4 font-semibold text-xl">Select Location</h2>

          <GoogleMapsLoader>
            <MapLocationPicker
              onLocationSelect={handleLocationSelect}
              onCancel={onClose}
              initialLocation={initialLocation}
            />
          </GoogleMapsLoader>
        </div>
      </div>
    </div>
  );
}
