import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LogOut } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useTranslation } from "react-i18next";

interface UserMetadata {
  name?: string;
  email?: string;
  role?: string;
}

const ProfileDropdown: React.FC = () => {
  const { t } = useTranslation();
  const { signOut } = useAuth();
  const [userMetadata, setUserMetadata] = useState<UserMetadata | null>(null);
  const [userEmail, setUserEmail] = useState<string>("");

  useEffect(() => {
    const fetchUserData = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session?.user) {
        const metadata = data.session.user.user_metadata;
        const email = data.session.user.email || "";

        setUserMetadata(metadata);
        setUserEmail(email);
      }
    };

    fetchUserData();

    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (session?.user) {
        const metadata = session.user.user_metadata;
        const email = session.user.email || "";

        setUserMetadata(metadata);
        setUserEmail(email);
      } else {
        setUserMetadata(null);
        setUserEmail("");
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  // Generate initials from name or email
  const getInitials = (name: string, email: string): string => {
    if (name && name.trim()) {
      const nameParts = name.trim().split(" ");
      if (nameParts.length >= 2) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
      }
      return name.substring(0, 2).toUpperCase();
    }

    if (email) {
      return email.substring(0, 2).toUpperCase();
    }

    return "U";
  };

  // Transform role from UPPERCASE_UNDERSCORE to Title Case
  const formatRole = (role: string): string => {
    if (!role) return t("profile.defaultRole");
    return role
      .toLowerCase()
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const initials = getInitials(userMetadata?.name || "", userEmail);
  const displayName =
    userMetadata?.name || userEmail.split("@")[0] || t("profile.defaultName");
  const displayRole = formatRole(userMetadata?.role || "");

  const handleLogout = async () => {
    await signOut();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="relative bg-orange-400 hover:bg-orange-500 rounded-full focus-visible:ring-2 focus-visible:ring-orange-400 w-8 h-8"
        >
          <div className="flex justify-center items-center rounded-full w-8 h-8">
            <span className="font-medium text-white text-sm">{initials}</span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="font-medium text-sm leading-none">{displayName}</p>
            <p className="text-muted-foreground text-xs leading-none">
              {userEmail}
            </p>
            <p className="text-muted-foreground text-xs leading-none">
              {displayRole}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="focus:bg-red-50 text-red-600 focus:text-red-600 cursor-pointer"
          onClick={handleLogout}
        >
          <LogOut className="mr-2 w-4 h-4" />
          <span>{t("profile.logout")}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProfileDropdown;
