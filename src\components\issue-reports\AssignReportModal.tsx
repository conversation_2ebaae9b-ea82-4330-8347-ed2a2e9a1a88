import React, { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";

import { createIssueAssignment } from "@/data/issueAssignments";

const assignReportSchema = z.object({
  maintenanceEmployeeEmail: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
});

type AssignReportFormData = z.infer<typeof assignReportSchema>;

interface AssignReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  reportId: string;
  reportTitle: string;
}

const AssignReportModal: React.FC<AssignReportModalProps> = ({
  isOpen,
  onClose,
  reportId,
  reportTitle,
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const form = useForm<AssignReportFormData>({
    resolver: zodResolver(assignReportSchema),
    defaultValues: {
      maintenanceEmployeeEmail: "",
    },
  });

  const assignMutation = useMutation({
    mutationFn: (data: AssignReportFormData) =>
      createIssueAssignment({
        issueId: reportId,
        maintenanceEmployeeEmail: data.maintenanceEmployeeEmail,
        status: "pending",
        comments: `Assigned report: ${reportTitle}`,
      }),
    onSuccess: () => {
      toast.success(t("branchReport.assignSuccess"));
      queryClient.invalidateQueries({ queryKey: ["issue-reports"] });
      queryClient.invalidateQueries({ queryKey: ["issue-assignments"] });
      form.reset();
      onClose();
    },
    onError: (error) => {
      console.error("Assignment failed:", error);
      toast.error(error.message || t("branchReport.assignFailed"));
    },
  });

  const onSubmit = (data: AssignReportFormData) => {
    assignMutation.mutate(data);
  };

  const handleClose = () => {
    if (!assignMutation.isPending) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("branchReport.assignReport")}</DialogTitle>
          <DialogDescription>
            {t("branchReport.assignReportDescription", { reportTitle })}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="maintenanceEmployeeEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("branchReport.maintainerEmail")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("auth.emailPlaceholder")}
                      {...field}
                      disabled={assignMutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={assignMutation.isPending}
              >
                {t("global.cancel")}
              </Button>
              <Button type="submit" disabled={assignMutation.isPending}>
                {assignMutation.isPending && (
                  <Loader2 className="mr-2 w-4 h-4 animate-spin" />
                )}
                {t("branchReport.assignToMaintainer")}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AssignReportModal;
