import React, { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { resendVerificationEmail } from "@/data/verification";
import { Mail, RefreshCw, CheckCircle, AlertCircle } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const EmailVerificationPending: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [resendSuccess, setResendSuccess] = useState(false);

  const resendMutation = useMutation({
    mutationFn: () => {
      if (!user?.email) {
        throw new Error("No user email found");
      }
      return resendVerificationEmail({ email: user.email });
    },
    onSuccess: () => {
      setResendSuccess(true);
      setTimeout(() => setResendSuccess(false), 5000); // Hide success message after 5 seconds
    },
  });

  return (
    <div className="flex justify-center items-center min-h-screen">
      <div className="bg-secondary-bg shadow-lg p-8 rounded-lg w-full max-w-md text-center">
        <div className="mb-6">
          <div className="flex justify-center items-center bg-blue-100 mx-auto mb-4 rounded-full w-16 h-16">
            <Mail className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="mb-2 font-bold text-gray-900 text-2xl">
            {t("emailVerification.title")}
          </h1>
          <p className="mb-4 text-gray-600">
            {t("emailVerification.description")}
          </p>
          {user?.email && (
            <p className="mb-6 text-gray-500 text-sm">
              {t("emailVerification.sentTo")}{" "}
              <span className="font-semibold">{user.email}</span>
            </p>
          )}
        </div>

        {resendSuccess && (
          <div className="flex justify-center items-center bg-green-100 mb-4 p-3 rounded-lg">
            <CheckCircle className="mr-2 w-5 h-5 text-green-600" />
            <p className="text-green-700 text-sm">
              {t("emailVerification.resendSuccess")}
            </p>
          </div>
        )}

        {resendMutation.isError && (
          <div className="flex justify-center items-center bg-red-100 mb-4 p-3 rounded-lg">
            <AlertCircle className="mr-2 w-5 h-5 text-red-600" />
            <p className="text-red-700 text-sm">
              {resendMutation.error?.message ||
                t("emailVerification.resendError")}
            </p>
          </div>
        )}

        <div className="space-y-3">
          <Button
            onClick={() => resendMutation.mutate()}
            disabled={resendMutation.isPending || resendSuccess}
            variant="outline"
            className="w-full"
          >
            {resendMutation.isPending ? (
              <>
                <RefreshCw className="mr-2 w-4 h-4 animate-spin" />
                {t("emailVerification.resending")}
              </>
            ) : (
              <>
                <Mail className="mr-2 w-4 h-4" />
                {t("emailVerification.resendButton")}
              </>
            )}
          </Button>

          <div className="text-gray-500 text-xs">
            <p>{t("emailVerification.checkSpam")}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationPending;
