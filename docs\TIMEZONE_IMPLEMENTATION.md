# Timezone and Date Formatting Implementation

This documentation covers the comprehensive timezone and date formatting system implemented across the Y-Verify frontend application.

## Overview

The application now supports user-configurable timezone and date formatting preferences that apply to all date and time displays throughout the interface. This ensures a consistent and localized experience for users across different timezones.

## Key Components

### 1. Timezone Utility Library (`/src/lib/timezone.ts`)

The core timezone functionality includes:

#### Timezone Options

- **19 timezone options** covering major regions including Middle East, Europe, Americas, and Asia-Pacific
- Proper IANA timezone identifiers (e.g., `Asia/Riyadh`, `America/New_York`)
- Migration support for legacy timezone values

#### Core Functions

- `formatDateWithTimezone()` - Format dates with timezone conversion
- `formatDateTimeWithTimezone()` - Format dates with time and timezone conversion
- `convertToUTC()` - Convert local timezone dates to UTC for storage
- `convertFromUTC()` - Convert UTC dates to user's timezone for display
- `normalizeTimezone()` - Handle legacy timezone values and migrations
- `getUserBrowserTimezone()` - Detect user's browser timezone as fallback

### 2. Localization Settings Hook (`/src/hooks/useLocalizationSettings.ts`)

Enhanced hook providing:

- User's timezone preference from settings
- Date format preference from settings
- Timezone-aware formatting functions
- Backward compatibility with existing date formatting

#### New API Methods

```typescript
const { formatDate, formatDateTime, formatTime, timezone } = useDateFormatter();

// Format date with user's timezone and date format
formatDate(date); // Returns: "15/01/2024" (based on user preferences)

// Format date and time with user's timezone
formatDateTime(date, includeTime); // Returns: "15/01/2024 14:30"

// Format time only with user's timezone
formatTime(date); // Returns: "14:30"
```

### 3. Settings Interface (`/src/components/settings/LocalizationTab.tsx`)

Updated to provide:

- **19 timezone options** in dropdown
- Proper IANA timezone identifiers
- Seamless migration from legacy timezone values

## Implementation Details

### Timezone Support

- All dates from Supabase (assumed UTC) are converted to user's selected timezone
- Support for daylight saving time transitions
- Fallback to browser timezone if user hasn't selected one
- Migration path for existing legacy timezone values

### Date Format Support

- 8 different date format patterns
- User-configurable through settings
- Applied consistently across all components

### Updated Components

The following components have been updated to use timezone-aware formatting:

1. **Calendar Components**

   - `Calendar.tsx` - Calendar grid and date picker
   - `EventModal.tsx` - Event details display

2. **Forms and Assignment Components**

   - `AssignChecklistForm.tsx` - Assignment creation dates
   - `EditChecklistForm.tsx` - Edit form dates
   - `EditAssignment.tsx` - Assignment modification dates
   - `AssignmentResponseHistory.tsx` - Response timestamps

3. **Notification and Reporting**

   - `NotificationsPage.tsx` - Notification timestamps
   - `LineChart.tsx` - Chart data points

4. **Branch Management**
   - `NewBranchForm.tsx` - Branch creation dates
   - `MapLocationPicker.tsx` - Location review dates

## Usage Examples

### Basic Date Formatting

```typescript
import { useDateFormatter } from "@/hooks/useLocalizationSettings";

const MyComponent = () => {
  const { formatDate, formatDateTime, formatTime } = useDateFormatter();

  // Format a date from the database (UTC)
  const displayDate = formatDate(submission.createdAt);

  // Format with time
  const displayDateTime = formatDateTime(notification.sentAt);

  // Format time only
  const displayTime = formatTime(assignment.dueTime);

  return <div>{displayDate}</div>;
};
```

### Direct Timezone Functions

```typescript
import { formatDateWithTimezone } from "@/lib/timezone";

// Convert UTC date to specific timezone
const saudiTime = formatDateWithTimezone(
  utcDate,
  "dd/MM/yyyy HH:mm",
  "Asia/Riyadh"
);
```

### Settings Integration

```typescript
import { useLocalizationSettings } from "@/hooks/useLocalizationSettings";

const SettingsComponent = () => {
  const { timezone, timezoneOptions } = useLocalizationSettings();

  return (
    <select value={timezone}>
      {timezoneOptions.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};
```

## Data Flow

1. **User Selection**: User selects timezone in Settings > Localization
2. **Storage**: Timezone preference stored in backend/Supabase
3. **Retrieval**: Frontend fetches user preferences via `useLocalizationSettings()`
4. **Application**: All date/time displays use timezone-aware formatting functions
5. **Conversion**: UTC timestamps from database converted to user's timezone before display

## Migration Strategy

### Legacy Timezone Values

The system handles migration from old timezone values:

- `"saudi-arabia"` → `"Asia/Riyadh"`
- `"uae"` → `"Asia/Dubai"`
- `"egypt"` → `"Africa/Cairo"`

### Backward Compatibility

- Existing code using `formatDate()` continues to work
- Gradual migration path from old API to new timezone-aware API
- Fallback mechanisms for invalid or missing timezone data

## Error Handling

- Invalid timezone identifiers fall back to UTC
- Date parsing errors fall back to basic formatting
- Missing user settings fall back to browser timezone
- Console logging for debugging timezone conversion issues

## Testing

A test component (`TimezoneTest.tsx`) is available to verify:

- User's selected timezone vs browser timezone
- Date format preferences
- UTC to local timezone conversion
- Different timezone formatting examples

## Performance Considerations

- Timezone data cached by browser's Intl API
- Settings cached via React Query
- Minimal overhead for timezone conversions
- Library size impact: `date-fns-tz` adds ~15KB gzipped

## Future Enhancements

1. **Time Format Support**: 12-hour vs 24-hour time formatting
2. **Calendar Integration**: First day of week preferences
3. **Relative Time**: Timezone-aware "2 hours ago" formatting
4. **Multi-timezone Display**: Show times in multiple zones simultaneously

## Dependencies

- `date-fns-tz`: Timezone conversion and formatting
- `date-fns`: Base date manipulation and formatting
- React Query: Settings caching and state management
- Existing localization infrastructure
