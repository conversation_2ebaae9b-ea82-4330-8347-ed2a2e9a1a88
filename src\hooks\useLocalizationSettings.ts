import { useQuery } from "@tanstack/react-query";
import {
  fetchLocalizationSettingsByUserId,
  fetchDefaultLocalizationSettings,
} from "@/data/settings";
import { format } from "date-fns";
import {
  formatDateWithTimezone,
  formatDateTimeWithTimezone,
  TIMEZONE_OPTIONS,
  getUserBrowserTimezone,
} from "@/lib/timezone";

// Hook to get user's localization settings
export const useLocalizationSettings = () => {
  // Fetch user's localization settings
  const { data: userSettings, isLoading: userSettingsLoading } = useQuery({
    queryKey: ["localizationSettings"],
    queryFn: fetchLocalizationSettingsByUserId,
    retry: false,
  });

  // Fetch default localization settings as fallback
  const { data: defaultSettings, isLoading: defaultsLoading } = useQuery({
    queryKey: ["defaultLocalizationSettings"],
    queryFn: fetchDefaultLocalizationSettings,
  });

  // Get current settings (user settings or defaults)
  const currentSettings = userSettings || defaultSettings;
  const isLoading = defaultsLoading || userSettingsLoading;

  // Get timezone from settings or fallback to browser timezone
  const timezone = currentSettings?.timezone || getUserBrowserTimezone();

  return {
    settings: currentSettings,
    isLoading,
    timezone,
    timezoneOptions: TIMEZONE_OPTIONS,
  };
};

// Date format mapping from backend format to date-fns format
const DATE_FORMAT_MAP = {
  "dd-mm-yyyy": "dd/MM/yyyy",
  "mm-dd-yyyy": "MM/dd/yyyy",
  "yyyy-mm-dd": "yyyy/MM/dd",
  "DD-MM-YYYY": "dd/MM/yyyy",
  "MM-DD-YYYY": "MM/dd/yyyy",
  "YYYY-MM-DD": "yyyy/MM/dd",
  "MM/DD/YYYY": "MM/dd/yyyy",
  "DD/MM/YYYY": "dd/MM/yyyy",
  "YYYY/MM/DD": "yyyy/MM/dd",
} as const;

// Function to convert backend date format to date-fns format
export const getDateFnsFormat = (backendFormat: string): string => {
  return (
    DATE_FORMAT_MAP[backendFormat as keyof typeof DATE_FORMAT_MAP] ||
    "MM/dd/yyyy"
  );
};

// Utility function to format date based on user's localization settings with timezone support
export const formatDateWithUserSettings = (
  date: Date | string,
  userDateFormat?: string,
  userTimezone?: string,
  fallbackFormat?: string
): string => {
  if (!date) return "";

  // Use user's date format, fallback format, or default
  const formatToUse = userDateFormat || fallbackFormat || "MM/dd/yyyy";
  const dateFnsFormat = getDateFnsFormat(formatToUse);
  const timezone = userTimezone || getUserBrowserTimezone();

  try {
    return formatDateWithTimezone(date, dateFnsFormat, timezone);
  } catch (error) {
    console.error("Error formatting date with timezone:", error);
    // Fallback to basic formatting without timezone
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return format(dateObj, dateFnsFormat);
  }
};

// Utility function to format datetime based on user's localization settings with timezone support
export const formatDateTimeWithUserSettings = (
  date: Date | string,
  userDateFormat?: string,
  userTimezone?: string,
  includeTime: boolean = true,
  fallbackFormat?: string
): string => {
  if (!date) return "";

  // Use user's date format, fallback format, or default
  const formatToUse = userDateFormat || fallbackFormat || "MM/dd/yyyy";
  const dateFnsFormat = getDateFnsFormat(formatToUse);
  const timezone = userTimezone || getUserBrowserTimezone();
  const finalFormat = includeTime ? `${dateFnsFormat} HH:mm` : dateFnsFormat;

  try {
    return includeTime
      ? formatDateTimeWithTimezone(date, finalFormat, timezone)
      : formatDateWithTimezone(date, dateFnsFormat, timezone);
  } catch (error) {
    console.error("Error formatting datetime with timezone:", error);
    // Fallback to basic formatting without timezone
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return format(dateObj, finalFormat);
  }
};

// Hook to get a format function with user's settings (timezone-aware)
export const useDateFormatter = () => {
  const { settings, timezone } = useLocalizationSettings();

  const formatDate = (date: Date | string, fallbackFormat?: string) => {
    return formatDateWithUserSettings(
      date,
      settings?.dateFormat,
      timezone,
      fallbackFormat
    );
  };

  const formatDateTime = (
    date: Date | string,
    includeTime: boolean = true,
    fallbackFormat?: string
  ) => {
    return formatDateTimeWithUserSettings(
      date,
      settings?.dateFormat,
      timezone,
      includeTime,
      fallbackFormat
    );
  };

  const formatTime = (date: Date | string) => {
    if (!date) return "";
    try {
      return formatDateTimeWithTimezone(
        date,
        "HH:mm",
        timezone || getUserBrowserTimezone()
      );
    } catch (error) {
      console.error("Error formatting time:", error);
      const dateObj = typeof date === "string" ? new Date(date) : date;
      return format(dateObj, "HH:mm");
    }
  };

  return {
    formatDate,
    formatDateTime,
    formatTime,
    timezone,
  };
};

// Function for local date string formatting with user settings (kept for backward compatibility)
export const formatDateWithUserLocale = (
  date: Date | string,
  userDateFormat?: string,
  options?: Intl.DateTimeFormatOptions
): string => {
  if (!date) return "";

  const dateObj = typeof date === "string" ? new Date(date) : date;

  if (userDateFormat && options) {
    // If we have specific options, use toLocaleDateString
    return dateObj.toLocaleDateString("en-US", options);
  }

  // Otherwise, use our standard formatting
  return formatDateWithUserSettings(dateObj, userDateFormat);
};

// Hook for getting a date formatter with specific fallback format (timezone-aware)
export const useDateFormatterWithFallback = (fallbackFormat?: string) => {
  const { settings, timezone } = useLocalizationSettings();

  return (date: Date | string) => {
    return formatDateWithUserSettings(
      date,
      settings?.dateFormat,
      timezone,
      fallbackFormat
    );
  };
};

// Hook for getting current user's date format string
export const useUserDateFormat = () => {
  const { settings } = useLocalizationSettings();
  return settings?.dateFormat || "MM/dd/yyyy";
};

// Hook for getting current user's timezone
export const useUserTimezone = () => {
  const { timezone } = useLocalizationSettings();
  return timezone;
};
