import { useAuth } from "@/contexts/AuthContext";
import {
  BarChart3,
  Building2,
  FileText,
  Home,
  Layers,
  LogOut,
  Files,
  Calendar,
  Users,
  Settings,
  PencilLineIcon,
  Wrench,
} from "lucide-react";
import { NavLink, useLocation } from "react-router-dom";
import { But<PERSON> } from "./ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "./ui/tooltip";
import { useTranslation } from "react-i18next";
import Logo from "./Logo";
import { canUserAccess, type Feature } from "@/data/permissions";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import MaintainerNotificationBadge from "./MaintainerNotificationBadge";

const navigationItems = [
  {
    titleKey: "sidebar.dashboard",
    url: "/",
    icon: Home,
    permission: "access_dashboard" as Feature,
  },
  {
    titleKey: "sidebar.companies",
    url: "/companies",
    icon: Building2,
    permission: "view_stores" as Feature,
  },
  {
    titleKey: "sidebar.templates",
    url: "/templates",
    icon: FileText,
    permission: "view_templates" as Feature,
  },
  {
    titleKey: "sidebar.assignments",
    url: "/assignments",
    icon: Layers,
    permission: "view_checklist_assignments" as Feature,
  },
  {
    titleKey: "sidebar.responses",
    icon: FileText,
    url: "/assignment-response/history",
    permission: "view_assignment_responses" as Feature,
  },
  {
    titleKey: "sidebar.calendar",
    icon: Calendar,
    url: "/calendar",
    permission: "access_calendar_view" as Feature,
  },
  {
    titleKey: "sidebar.userManagement",
    icon: Users,
    url: "/user-management",
    permission: "user_invite" as Feature,
  },
  {
    titleKey: "sidebar.auditLog",
    icon: BarChart3,
    url: "/audit-log",
    permission: "access_dashboard" as Feature,
  },
  {
    titleKey: "sidebar.branchReportSystem",
    icon: PencilLineIcon,
    url: "/report",
    permission: "view_reports" as Feature,
  },
  {
    titleKey: "sidebar.myAssignments",
    icon: Wrench,
    url: "/my-assignments",
    permission: "view_reports" as Feature,
    roleRequired: "MAINTAINER",
  },
  {
    titleKey: "sidebar.settings",
    icon: Settings,
    url: "/settings",
    permission: "view_modify_settings" as Feature,
  },
];

export function Sidebar() {
  const location = useLocation();
  const currentPath = location.pathname;
  const { t } = useTranslation();
  const { signOut, user } = useAuth();

  // Get user role using useQuery
  const { data: userRole } = useQuery({
    queryKey: ["userRole"],
    queryFn: async () => {
      const { data } = await supabase.auth.getSession();
      return data.session?.user?.user_metadata?.role || null;
    },
    enabled: !!user,
    staleTime: Infinity, // Cache until logout
    gcTime: Infinity,
  });

  // Check permissions using useQuery
  const { data: visibleItems = [], isLoading: isCheckingPermissions } =
    useQuery<typeof navigationItems>({
      queryKey: ["sidebarPermissions", userRole],
      queryFn: async () => {
        if (!user) return [];

        const permissionPromises = navigationItems.map(async (item) => {
          const hasPermission = await canUserAccess(item.permission);

          // Check role requirement if specified
          let roleMatches = true;
          if ("roleRequired" in item && item.roleRequired) {
            roleMatches = userRole === item.roleRequired;
          }

          return {
            ...item,
            hasPermission: hasPermission && roleMatches,
          };
        });

        const itemsWithPermissions = await Promise.all(permissionPromises);
        return itemsWithPermissions.filter((item) => item.hasPermission);
      },
      enabled: !!user && userRole !== undefined, // Wait for user and userRole
      staleTime: Infinity, // Cache until logout
      gcTime: Infinity,
      retry: false,
    });

  const isActive = (path: string) => {
    if (path === "/" && currentPath === "/") return true;
    if (path !== "/" && currentPath.startsWith(path)) return true;
    return false;
  };

  async function handleLogout() {
    await signOut();
  }

  // Don't render sidebar if user is not logged in
  if (!user) {
    return null;
  }

  return (
    <div className="flex flex-col border-slate-200 border-r w-16">
      {/* Navigation */}
      <nav className="flex-1 py-4">
        <TooltipProvider>
          <div className="space-y-2 px-2">
            {isCheckingPermissions ? (
              // Show loading state while checking permissions
              <div className="flex justify-center items-center w-12 h-12">
                <div className="border-2 border-slate-300 border-t-slate-600 rounded-full w-4 h-4 animate-spin"></div>
              </div>
            ) : (
              visibleItems.map((item) => (
                <Tooltip key={item.titleKey}>
                  <TooltipTrigger asChild>
                    <NavLink
                      to={item.url}
                      className={`w-12 h-12 flex items-center justify-center rounded-lg transition-colors relative ${
                        isActive(item.url)
                          ? "bg-slate-200 text-slate-900"
                          : "text-slate-500 hover:bg-slate-100 hover:text-slate-700"
                      }`}
                    >
                      <item.icon className="w-5 h-5" />
                      {item.url === "/my-assignments" &&
                        userRole === "MAINTAINER" && (
                          <div className="-top-1 -right-1 absolute">
                            <MaintainerNotificationBadge />
                          </div>
                        )}
                    </NavLink>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>{t(item.titleKey)}</p>
                  </TooltipContent>
                </Tooltip>
              ))
            )}
          </div>
        </TooltipProvider>
      </nav>

      {/* Logout button */}
      <div className="flex justify-center items-center border-slate-200 border-t w-full">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={handleLogout}
                variant="ghost"
                size="sm"
                className="p-0"
              >
                <LogOut />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>{t("sidebar.logout")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}
