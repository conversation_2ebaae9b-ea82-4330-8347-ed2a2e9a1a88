import { supabase } from "@/integrations/supabase/client";
import { MEDIA_BUCKET } from "@/lib/constants";
import { fetchWithToken } from "@/lib/fetchWithToken";
import { UploadResult } from "@/lib/types";

export const createResponseMediaEntry = async (
  fileName: string,
  url: string,
  checklistResponseFieldsId: string
) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/responses/media`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        fileName,
        url,
        checklistResponseFieldsId,
      }),
    }
  );

  if (!response.ok) {
    throw new Error(
      (await response.json()).message || "Failed to create media entry"
    );
  }

  const responseData = await response.json();
  return responseData.data;
};

// Create Media Entry related to Issue Report
interface ReportMediaEntryInput {
  fileName: string;
  url: string;
  issueReportId: string;
}
interface Media {
  id: string;
  name: string;
  url: string;
  createdAt: string;
}
export async function createIssueReportMediaEntry(
  data: ReportMediaEntryInput
): Promise<Media> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/report-issue/media`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to create media entry");
  }

  const json = await response.json();
  return json.data.media;
}

/**
 * Upload a single image to Supabase storage
 * @param file - The image file to upload
 * @param bucket - The Supabase storage bucket name (default: 'media-bucket')
 * @param folder - Optional folder path within the bucket
 * @returns Promise with upload result
 */
interface UploadProps {
  file: File;
  checklistResponseFieldsId?: string;
  issueReportId?: string;
  folder?: string;
  isLogo?: boolean;
}

export const uploadImageToSupabase = async ({
  file,
  checklistResponseFieldsId,
  issueReportId,
  folder,
  isLogo = false,
}: UploadProps): Promise<UploadResult> => {
  const bucket = MEDIA_BUCKET;
  try {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      return {
        success: false,
        error: "File must be an image",
      };
    }

    // Different size limits for logo vs regular media
    const maxSize = isLogo ? 2 * 1024 * 1024 : 10 * 1024 * 1024; // 2MB for logos, 10MB for others
    const maxSizeText = isLogo ? "2MB" : "10MB";

    if (file.size > maxSize) {
      return {
        success: false,
        error: `File size must be less than ${maxSizeText}`,
      };
    }

    // Additional validation for logos
    if (isLogo) {
      const allowedLogoTypes = [
        "image/png",
        "image/jpeg",
        "image/jpg",
        "image/svg+xml",
      ];
      if (!allowedLogoTypes.includes(file.type)) {
        return {
          success: false,
          error: "Logo must be PNG, JPG, JPEG, or SVG format",
        };
      }
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const fileExtension = file.name.split(".").pop()?.toLowerCase() || "jpg";
    const fileName = `${timestamp}-${randomId}.${fileExtension}`;

    // Create full path
    const filePath = folder ? `${folder}/${fileName}` : fileName;

    // Upload file to Supabase storage
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: false,
      });

    if (error) {
      console.error("Supabase upload error:", error);
      return {
        success: false,
        error: error.message,
      };
    }

    // Get public URL
    const { data: publicUrlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath);

    // For logos, we don't need to create a media entry, just return the URL
    if (isLogo) {
      return {
        success: true,
        url: publicUrlData.publicUrl,
        path: filePath,
        id: null, // No media entry for logos
      };
    }

    //   Create media entry for non-logo uploads
    const mediaEntry = checklistResponseFieldsId
      ? await createResponseMediaEntry(
          fileName,
          publicUrlData.publicUrl,
          checklistResponseFieldsId
        )
      : issueReportId
      ? await createIssueReportMediaEntry({
          fileName,
          url: publicUrlData.publicUrl,
          issueReportId,
        })
      : null;

    if (!mediaEntry && !isLogo) {
      return {
        success: false,
        error: "Failed to create media entry",
      };
    }
    return {
      success: true,
      url: publicUrlData.publicUrl,
      path: filePath,
      id: mediaEntry?.id || mediaEntry?.response.id || null,
    };
  } catch (error) {
    console.error("Upload error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

/**
 * Delete an image from Supabase storage
 * @param filePath - The path of the file to delete
 * @param bucket - The Supabase storage bucket name
 * @returns Promise with deletion result
 */
export const deleteImageFromSupabase = async (
  filePath: string,
  bucket: string = "media-bucket"
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error } = await supabase.storage.from(bucket).remove([filePath]);

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

/**
 * Get signed URL for private images (if your bucket is private)
 * @param filePath - The path of the file
 * @param bucket - The Supabase storage bucket name
 * @param expiresIn - Expiration time in seconds (default: 1 hour)
 * @returns Promise with signed URL
 */
export const getSignedUrl = async (
  filePath: string,
  bucket: string = "media-bucket",
  expiresIn: number = 3600
): Promise<{ success: boolean; url?: string; error?: string }> => {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(filePath, expiresIn);

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
      url: data.signedUrl,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};
