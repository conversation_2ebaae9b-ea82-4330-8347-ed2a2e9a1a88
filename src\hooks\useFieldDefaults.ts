import { useQuery } from "@tanstack/react-query";
import { fetchDefaultsSettingsByUserId } from "@/data/settings";

/**
 * Hook to provide default field values based on user's defaults settings
 * This ensures that when creating new template fields or assignment fields,
 * they respect the user's preferences (e.g., requireEvidence setting)
 */
export const useFieldDefaults = () => {
  const { data: defaultsSettings } = useQuery({
    queryKey: ["defaultsSettings"],
    queryFn: fetchDefaultsSettingsByUserId,
    retry: false,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  /**
   * Get default values for a new field based on user settings
   */
  const getNewFieldDefaults = () => {
    return {
      questionText: "New question",
      questionType: "Text" as const,
      required: false,
      addHint: "",
      requireEvidence: defaultsSettings?.requireEvidenceByDefault ?? false,
      scoreWeight: 3,
      placeholder: "",
      preferredAnswer: "",
      hasPreferredAnswer: false,
    };
  };

  /**
   * Get default values for assignment fields specifically
   */
  const getNewAssignmentFieldDefaults = () => {
    return {
      question: "New question",
      questionType: "Text" as const,
      required: false,
      hint: "",
      requiresEvidence: defaultsSettings?.requireEvidenceByDefault ?? false,
      score: "1",
      order: 1,
      preferredAnswer: "",
      hasPreferredAnswer: false,
      templateFieldId: undefined,
    };
  };

  /**
   * Get default values when creating a new section with initial field
   */
  const getNewSectionWithFieldDefaults = (sectionName?: string) => {
    return {
      name: sectionName || "New Section",
      order: 1,
      fields: [
        {
          ...getNewFieldDefaults(),
          questionText: "Lorem ipsum dummy text",
          addHint: "Tooltip appear in checklist",
          order: 1,
        },
      ],
    };
  };

  return {
    defaultsSettings,
    getNewFieldDefaults,
    getNewAssignmentFieldDefaults,
    getNewSectionWithFieldDefaults,
    isRequireEvidenceByDefault:
      defaultsSettings?.requireEvidenceByDefault ?? false,
  };
};
