import { Header } from "@/components/CompanyHeader";
import { But<PERSON> } from "@/components/ui/button";
import { Building2, Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Companies = () => {
  const navigate = useNavigate();

  return (
    <div className="flex min-h-screen">
      <div className="flex-1">
        <div className="min-h-screen">
          <Header title="Dashboard" />

          <main className="p-6">
            <div className="mx-auto max-w-4xl">
              <div className="mb-12 text-center">
                <h1 className="mb-4 font-bold text-slate-900 text-3xl">
                  Welcome to Company Management
                </h1>
                <p className="mb-8 text-slate-600 text-lg">
                  Manage your companies efficiently with our comprehensive
                  dashboard
                </p>
              </div>

              <div className="gap-6 grid md:grid-cols-2 mx-auto max-w-2xl">
                <div className="shadow-sm hover:shadow-md p-6 border border-slate-200 rounded-lg transition-shadow">
                  <div className="flex justify-center items-center bg-slate-100 mb-4 rounded-lg w-12 h-12">
                    <Building2 className="w-6 h-6 text-slate-700" />
                  </div>
                  <h3 className="mb-2 font-semibold text-slate-900 text-lg">
                    View Companies
                  </h3>
                  <p className="mb-4 text-slate-600">
                    Browse and manage all your registered companies
                  </p>
                  <Button
                    onClick={() => navigate("/companies")}
                    className="bg-slate-800 hover:bg-slate-900 w-full"
                  >
                    Go to Companies
                  </Button>
                </div>

                <div className="shadow-sm hover:shadow-md p-6 border border-slate-200 rounded-lg transition-shadow">
                  <div className="flex justify-center items-center bg-slate-100 mb-4 rounded-lg w-12 h-12">
                    <Plus className="w-6 h-6 text-slate-700" />
                  </div>
                  <h3 className="mb-2 font-semibold text-slate-900 text-lg">
                    Add New Company
                  </h3>
                  <p className="mb-4 text-slate-600">
                    Register a new company to your management system
                  </p>
                  <Button
                    onClick={() => navigate("/companies/new")}
                    variant="outline"
                    className="border-slate-200 w-full text-slate-700 hover:"
                  >
                    Create Company
                  </Button>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default Companies;
