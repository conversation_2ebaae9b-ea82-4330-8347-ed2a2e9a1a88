import { CalendarReturn } from "@/data/assignments";
import { cn, getEventType, getEventTypeColor } from "@/lib/utils";
import { number } from "zod";
import { useTranslation } from "react-i18next";
import { useLocalizationSettings } from "@/hooks/useLocalizationSettings";

interface CalendarGridProps {
  events: CalendarReturn[];
  onEventClick: (event: CalendarReturn) => void;
  selectedDate: Date;
}

// Generate calendar days with configurable first day of the week
function generateCalendarDays(
  month: number,
  year: number,
  firstDayOfWeek: string = "sunday"
) {
  const firstDay = new Date(year, month, 1, 0);
  const lastDay = new Date(year, month + 1, 0);

  // Calculate the first day of the week offset
  let firstDayOfWeekOffset = 0; // Sunday = 0
  switch (firstDayOfWeek) {
    case "monday":
      firstDayOfWeekOffset = 1;
      break;
    case "tuesday":
      firstDayOfWeekOffset = 2;
      break;
    case "wednesday":
      firstDayOfWeekOffset = 3;
      break;
    case "thursday":
      firstDayOfWeekOffset = 4;
      break;
    case "friday":
      firstDayOfWeekOffset = 5;
      break;
    case "saturday":
      firstDayOfWeekOffset = 6;
      break;
    case "sunday":
    default:
      firstDayOfWeekOffset = 0;
      break;
  }

  // Calculate how many days to show from previous month
  let firstDayOfWeekIndex = firstDay.getDay();
  // Adjust for different first day of week
  firstDayOfWeekIndex = (firstDayOfWeekIndex - firstDayOfWeekOffset + 7) % 7;

  const days: Array<{ date: number; isCurrentMonth: boolean; fullDate: Date }> =
    [];

  // Add previous month days
  const prevMonth = new Date(year, month, 0, 0);
  for (let i = firstDayOfWeekIndex - 1; i >= 0; i--) {
    days.push({
      date: prevMonth.getDate() - i,
      isCurrentMonth: false,
      fullDate: new Date(year, month - 1, prevMonth.getDate() - i, 0),
    });
  }

  // Add current month days
  for (let day = 1; day <= lastDay.getDate(); day++) {
    days.push({
      date: day,
      isCurrentMonth: true,
      fullDate: new Date(year, month, day, 0),
    });
  }

  // Add next month days to complete the grid
  const remaining = 42 - days.length; // 6 rows * 7 days
  for (let day = 1; day <= remaining; day++) {
    days.push({
      date: day,
      isCurrentMonth: false,
      fullDate: new Date(year, month + 1, day, 0),
    });
  }

  return days;
}

export function CalendarGrid({
  events,
  onEventClick,
  selectedDate,
}: CalendarGridProps) {
  const { t } = useTranslation();
  const { settings } = useLocalizationSettings();
  const month = selectedDate.getMonth();
  const year = selectedDate.getFullYear();

  // Get first day of week setting (default to sunday)
  const firstDayOfWeek = settings?.firstDayOfWeek || "sunday";

  const calendarDays = generateCalendarDays(month, year, firstDayOfWeek);

  // Base day names in Sunday-first order
  const baseDayNames = [
    t("calendarComponent.dayNames.sunday"),
    t("calendarComponent.dayNames.monday"),
    t("calendarComponent.dayNames.tuesday"),
    t("calendarComponent.dayNames.wednesday"),
    t("calendarComponent.dayNames.thursday"),
    t("calendarComponent.dayNames.friday"),
    t("calendarComponent.dayNames.saturday"),
  ];

  // Reorder day names based on first day of week setting
  let dayNames = [...baseDayNames];

  // Calculate the shift amount based on first day of week
  let shiftAmount = 0;
  switch (firstDayOfWeek) {
    case "monday":
      shiftAmount = 1;
      break;
    case "tuesday":
      shiftAmount = 2;
      break;
    case "wednesday":
      shiftAmount = 3;
      break;
    case "thursday":
      shiftAmount = 4;
      break;
    case "friday":
      shiftAmount = 5;
      break;
    case "saturday":
      shiftAmount = 6;
      break;
    case "sunday":
    default:
      shiftAmount = 0;
      break;
  }

  // Reorder the day names array based on the shift amount
  if (shiftAmount > 0) {
    dayNames = [
      ...baseDayNames.slice(shiftAmount),
      ...baseDayNames.slice(0, shiftAmount),
    ];
  }

  // Group events by date
  const eventsByDate = events.reduce((acc, event) => {
    const dateKey = new Date(event.startDate).toDateString();
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(event);
    return acc;
  }, {} as Record<string, CalendarReturn[]>);

  return (
    <div className="p-2 sm:p-5">
      {/* Day Headers */}
      <div className="gap-0.5 sm:gap-1.5 grid grid-cols-7 mb-1.5">
        {dayNames.map((day) => (
          <div key={day} className="py-2 text-center">
            <span className="hidden sm:inline font-bold text-gray text-sm sm:text-base">
              {day}
            </span>
            <span className="sm:hidden font-bold text-gray text-xs sm:text-sm">
              {day.slice(0, 3)}
            </span>
          </div>
        ))}
      </div>

      {/* Calendar Days */}
      <div className="gap-0.5 sm:gap-1.5 grid grid-cols-7">
        {calendarDays.map((day, index) => {
          const dayEvents = eventsByDate[day.fullDate.toDateString()] || [];

          return (
            <div
              key={index}
              className={cn(
                "flex flex-col bg-main-bg p-1 sm:p-2.5 rounded-lg max-w-[10.75rem] min-h-[80px] sm:min-h-[130px] max-h-[8.125rem]",
                dayEvents.length > 0 ? "justify-between" : "justify-start"
              )}
            >
              <div
                className={cn(
                  "font-medium text-sm sm:text-base",
                  day.isCurrentMonth ? "text-foreground" : "text-gray"
                )}
              >
                {day.date}
              </div>

              {/* Events */}
              {dayEvents.length > 0 && (
                <div className="space-y-0.5 sm:space-y-1">
                  {dayEvents.length === 1 ? (
                    <div
                      className={cn(
                        "hover:opacity-90 px-1 sm:px-2 py-1 sm:py-1.5 rounded-md min-h-[5rem] font-bold text-[10px] text-white sm:text-xs transition-opacity cursor-pointer",
                        getEventTypeColor(getEventType(dayEvents[0]))
                      )}
                      onClick={() => onEventClick(dayEvents[0])}
                    >
                      <div className="leading-tight">
                        <div className="hidden sm:block">
                          {dayEvents[0].calendarString}
                          <br />
                        </div>
                        <div className="sm:hidden">
                          {dayEvents[0].templateName} - {dayEvents[0].storeName}
                        </div>
                        <div className="mt-1 capitalize">
                          {getEventType(dayEvents[0])}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="group relative">
                      {/* Arrow buttons */}
                      <button
                        className="top-1/2 left-0 z-10 absolute opacity-0 group-hover:opacity-100 shadow p-0.5 rounded-full transition -translate-y-1/2"
                        onClick={(e) => {
                          const container = e.currentTarget
                            .nextSibling as HTMLDivElement;
                          container.scrollBy({ left: -80, behavior: "smooth" });
                        }}
                      >
                        ◀
                      </button>

                      <div className="flex gap-1 px-5 overflow-x-auto hide-scrollbar">
                        {dayEvents.map((event, index) => (
                          <div
                            key={index}
                            className={cn(
                              "px-2 py-1.5 rounded-md min-w-[6rem] max-w-[10rem] min-h-[5rem] font-bold text-[10px] text-white sm:text-xs cursor-pointer shrink-0",
                              getEventTypeColor(getEventType(event))
                            )}
                            onClick={() => onEventClick(event)}
                          >
                            <div className="leading-tight">
                              <div className="hidden sm:block">
                                {event.calendarString}
                                <br />
                              </div>
                              <div className="sm:hidden">
                                {event.templateName} - {event.storeName}
                              </div>
                              <div className="mt-1 capitalize">
                                {getEventType(event)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      <button
                        className="top-1/2 right-0 z-10 absolute opacity-0 group-hover:opacity-100 shadow p-0.5 rounded-full transition -translate-y-1/2"
                        onClick={(e) => {
                          const container = e.currentTarget
                            .previousSibling as HTMLDivElement;
                          container.scrollBy({ left: 80, behavior: "smooth" });
                        }}
                      >
                        ▶
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
