import React, { useRef } from "react";
import { RotateCcw } from "lucide-react";
import { Toggle } from "../ui/toggle";
import { Select } from "../ui/select-settings";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  fetchDefaultDefaultsSettings,
  fetchDefaultsSettingsByUserId,
  createDefaultsSettings,
  updateDefaultsSettings,
  applyDefaultDefaultsSettings,
} from "@/data/settings";
import { DefaultsSettings } from "@/lib/types";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

export const DefaultsTab: React.FC = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const debounceRef = useRef<NodeJS.Timeout>();

  // Fetch default defaults settings
  const { data: defaultSettings, isLoading: defaultsLoading } = useQuery({
    queryKey: ["defaultDefaultsSettings"],
    queryFn: fetchDefaultDefaultsSettings,
  });

  // Fetch user's current defaults settings
  const { data: userSettings, isLoading: userSettingsLoading } = useQuery({
    queryKey: ["defaultsSettings"],
    queryFn: fetchDefaultsSettingsByUserId,
    retry: false,
  });

  // Get current values (user settings or defaults)
  const currentSettings = userSettings || defaultSettings;
  const isLoading = defaultsLoading || userSettingsLoading;

  // Mutation to create/update defaults settings with optimistic updates
  const createMutation = useMutation({
    mutationFn: createDefaultsSettings,
    onMutate: async (newSettings) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["defaultsSettings"] });

      // Snapshot the previous value
      const previousSettings = queryClient.getQueryData(["defaultsSettings"]);

      // Optimistically update to the new value
      queryClient.setQueryData(["defaultsSettings"], newSettings);

      // Return a context object with the snapshotted value
      return { previousSettings };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["defaultsSettings"] });
      toast.success(t("settings.defaults.settingsCreated"));
    },
    onError: (error, newSettings, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(["defaultsSettings"], context?.previousSettings);
      toast.error(t("settings.defaults.createFailed") + ": " + error);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({ queryKey: ["defaultsSettings"] });
    },
  });

  const updateMutation = useMutation({
    mutationFn: updateDefaultsSettings,
    onMutate: async (newSettings) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["defaultsSettings"] });

      // Snapshot the previous value
      const previousSettings = queryClient.getQueryData(["defaultsSettings"]);

      // Optimistically update to the new value
      queryClient.setQueryData(["defaultsSettings"], newSettings);

      // Return a context object with the snapshotted value
      return { previousSettings };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["defaultsSettings"] });
      toast.success(t("settings.defaults.settingsUpdated"));
    },
    onError: (error, newSettings, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(["defaultsSettings"], context?.previousSettings);
      toast.error(t("settings.defaults.updateFailed") + ": " + error);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({ queryKey: ["defaultsSettings"] });
    },
  });

  // Mutation to apply default settings with optimistic updates
  const applyDefaultsMutation = useMutation({
    mutationFn: applyDefaultDefaultsSettings,
    onMutate: async () => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["defaultsSettings"] });

      // Snapshot the previous value
      const previousSettings = queryClient.getQueryData(["defaultsSettings"]);

      // Optimistically update to default settings
      if (defaultSettings) {
        queryClient.setQueryData(["defaultsSettings"], defaultSettings);
      }

      // Return a context object with the snapshotted value
      return { previousSettings };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["defaultsSettings"] });
      toast.success(t("settings.defaults.defaultsApplied"));
    },
    onError: (error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(["defaultsSettings"], context?.previousSettings);
      toast.error(t("settings.defaults.defaultsFailed") + ": " + error);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({ queryKey: ["defaultsSettings"] });
    },
  });

  // Handle save settings
  const handleSaveSettings = (settings: DefaultsSettings) => {
    if (userSettings) {
      updateMutation.mutate(settings);
    } else {
      createMutation.mutate(settings);
    }
  };

  // Handle reset to defaults
  const handleResetToDefaults = () => {
    applyDefaultsMutation.mutate();
  };

  // Handle field changes with optimistic updates
  const handleFieldChange = (
    field: keyof DefaultsSettings,
    value: string | boolean | number
  ) => {
    // Don't update if settings aren't loaded yet
    if (!currentSettings) return;

    const newSettings = { ...currentSettings, [field]: value };

    // Immediately update the UI optimistically
    queryClient.setQueryData(["defaultsSettings"], newSettings);

    // Clear any existing debounce timeout
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Debounce the actual API call
    debounceRef.current = setTimeout(() => {
      if (userSettings) {
        updateMutation.mutate(newSettings);
      } else {
        createMutation.mutate(newSettings);
      }
    }, 1000);
  };

  const frequencyOptions = [
    { value: "daily", label: t("assignment.daily") },
    { value: "weekly", label: t("assignment.weekly") },
    { value: "monthly", label: t("assignment.monthly") },
    { value: "yearly", label: t("assignment.yearly") },
  ];

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
            {t("settings.defaults.title")}
          </h2>
        </div>
        <div className="flex justify-center items-center py-8">
          <div className="border-primary border-b-2 rounded-full w-8 h-8 animate-spin"></div>
          <span className="ml-3 text-gray-600">
            {t("settings.defaults.loadingSettings")}
          </span>
        </div>
      </div>
    );
  }

  if (!currentSettings) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
            {t("settings.defaults.title")}
          </h2>
        </div>
        <div className="flex justify-center items-center py-8">
          <span className="text-gray-600">
            {t("settings.defaults.noSettingsAvailable")}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header with Reset Button */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
            {t("settings.defaults.title")}
          </h2>
          {/* Show saving indicator when mutations are in progress */}
          {(createMutation.isPending || updateMutation.isPending) && (
            <div className="flex items-center gap-2 text-gray-600 text-sm">
              <div className="border-primary border-b-2 rounded-full w-3 h-3 animate-spin"></div>
              <span>{t("settings.defaults.saving")}</span>
            </div>
          )}
        </div>
        <button
          onClick={handleResetToDefaults}
          disabled={applyDefaultsMutation.isPending}
          className="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 px-4 py-2 border border-gray-300 rounded-lg transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          <span className="font-medium text-sm">
            {applyDefaultsMutation.isPending
              ? t("settings.defaults.resetting")
              : t("settings.defaults.resetToDefaults")}
          </span>
        </button>
      </div>

      {/* Settings Grid */}
      <div className="space-y-6">
        {/* Default Checklist Frequency */}
        <div className="flex lg:flex-row flex-col lg:items-center gap-3 lg:gap-7">
          <label className="w-full lg:w-52 font-medium text-black-text">
            {t("settings.defaults.defaultChecklistFrequency")}
          </label>
          <div className="flex-1 max-w-md">
            <Select
              value={currentSettings.defaultChecklistFrequency}
              onValueChange={(value) =>
                handleFieldChange(
                  "defaultChecklistFrequency",
                  value as "daily" | "weekly" | "monthly" | "yearly"
                )
              }
              options={frequencyOptions}
            />
          </div>
        </div>

        {/* Require Evidence */}
        <div className="flex lg:flex-row flex-col lg:items-center gap-3 lg:gap-7">
          <label className="w-full lg:w-52 font-medium text-black-text">
            {t("settings.defaults.requireEvidenceByDefault")}
          </label>
          <div className="flex sm:flex-row flex-col sm:items-center gap-3">
            <Toggle
              checked={currentSettings.requireEvidenceByDefault}
              onCheckedChange={(checked) =>
                handleFieldChange("requireEvidenceByDefault", checked)
              }
            />
            <span className="text-gray-text text-sm">
              {t("settings.defaults.requireEvidenceNote")}
            </span>
          </div>
        </div>

        {/* Allow Edits After Submission */}
        <div className="flex lg:flex-row flex-col lg:items-center gap-3 lg:gap-7">
          <label className="w-full lg:w-52 font-medium text-black-text">
            {t("settings.defaults.allowEditAfterSubmission")}
          </label>
          <div className="flex sm:flex-row flex-col sm:items-center gap-3">
            <Toggle
              checked={currentSettings.allowEditAfterSubmission}
              onCheckedChange={(checked) =>
                handleFieldChange("allowEditAfterSubmission", checked)
              }
            />
            <span className="text-gray-text text-sm">
              {t("settings.defaults.allowEditNote")}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
