import {
  formatDateWithUserSettings,
  getDateFnsFormat,
} from "@/hooks/useLocalizationSettings";

// Example usage demonstration for different date format preferences
const examples = {
  // User has dd-mm-yyyy format preference
  userWithDDMMYYYY: {
    format: "dd-mm-yyyy",
    examples: [
      { date: new Date("2023-01-01"), expected: "01/01/2023" },
      { date: new Date("2023-12-31"), expected: "31/12/2023" },
      { date: "2023-06-15", expected: "15/06/2023" },
    ],
  },

  // User has mm-dd-yyyy format preference
  userWithMMDDYYYY: {
    format: "mm-dd-yyyy",
    examples: [
      { date: new Date("2023-01-01"), expected: "01/01/2023" },
      { date: new Date("2023-12-31"), expected: "12/31/2023" },
      { date: "2023-06-15", expected: "06/15/2023" },
    ],
  },

  // User has yyyy-mm-dd format preference
  userWithYYYYMMDD: {
    format: "yyyy-mm-dd",
    examples: [
      { date: new Date("2023-01-01"), expected: "2023/01/01" },
      { date: new Date("2023-12-31"), expected: "2023/12/31" },
      { date: "2023-06-15", expected: "2023/06/15" },
    ],
  },
};

// Manual testing functions (for development/debugging)
export const manualTests = {
  testDDMMYYYY: () => {
    const testDate = new Date("2023-12-25");
    const result = formatDateWithUserSettings(testDate, "dd-mm-yyyy");
    console.log(
      "dd-mm-yyyy format test:",
      result === "25/12/2023" ? "PASS" : "FAIL",
      `(${result})`
    );
  },

  testMMDDYYYY: () => {
    const testDate = new Date("2023-12-25");
    const result = formatDateWithUserSettings(testDate, "mm-dd-yyyy");
    console.log(
      "mm-dd-yyyy format test:",
      result === "12/25/2023" ? "PASS" : "FAIL",
      `(${result})`
    );
  },

  testYYYYMMDD: () => {
    const testDate = new Date("2023-12-25");
    const result = formatDateWithUserSettings(testDate, "yyyy-mm-dd");
    console.log(
      "yyyy-mm-dd format test:",
      result === "2023/12/25" ? "PASS" : "FAIL",
      `(${result})`
    );
  },

  testStringDate: () => {
    const result = formatDateWithUserSettings("2023-12-25", "dd-mm-yyyy");
    console.log(
      "String date test:",
      result === "25/12/2023" ? "PASS" : "FAIL",
      `(${result})`
    );
  },

  testFormatMapping: () => {
    const tests = [
      { input: "dd-mm-yyyy", expected: "dd/MM/yyyy" },
      { input: "mm-dd-yyyy", expected: "MM/dd/yyyy" },
      { input: "yyyy-mm-dd", expected: "yyyy/MM/dd" },
      { input: "unknown-format", expected: "MM/dd/yyyy" },
    ];

    tests.forEach((test) => {
      const result = getDateFnsFormat(test.input);
      console.log(
        `Format mapping ${test.input}:`,
        result === test.expected ? "PASS" : "FAIL",
        `(${result})`
      );
    });
  },

  runAllTests: () => {
    console.log("Running manual date format tests...");
    manualTests.testDDMMYYYY();
    manualTests.testMMDDYYYY();
    manualTests.testYYYYMMDD();
    manualTests.testStringDate();
    manualTests.testFormatMapping();
    console.log("Manual tests completed.");
  },
};

export { examples };
