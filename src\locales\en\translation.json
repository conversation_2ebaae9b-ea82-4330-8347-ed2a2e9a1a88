{"locale": "en", "auth": {"emailPlaceholder": "<EMAIL>", "signIn": "Sign In", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "signUp": "Sign Up", "welcome": "Welcome back", "signInSubtext": "Sign in to your account to continue"}, "error": {"title": "Error", "description": "An unexpected error occurred.", "code": "Error code: {{0}}", "data": "Error data: {{0}}"}, "dashboard": {"tabs": {"today": "Today", "week": "This week", "month": "This month", "customRange": "Custom date range"}, "stats": {"clients": "Clients", "users": "Users", "activeTemplates": "Active templates", "churnRate": "Churn rate"}, "clientsOverTime": {"title": "Clients over time", "timePeriods": {"6M": "6 months", "1Y": "1 year", "2Y": "2 years"}}, "activityLog": "Activity log", "companyName": "Company name", "activityType": "Activity type", "date": "Date", "performedBy": "Performed by", "clientSnapshot": "Client snapshot", "showingToOfResults": "Showing {{0}} to {{1}} of {{2}} results", "branches": "Branches", "devices": "Devices", "title": "Owner Dashboard"}, "companyManagement": {"title": "Company management", "filters": "Filters", "addFilter": "Add Filter", "table": {"companyName": "Company name", "businessType": "Business type", "branchesRooms": "Branches/Rooms", "storeAdmin": "Store Admin", "status": "Status", "createdDate": "Created Date", "action": "Action", "active": "Active", "inactive": "Inactive"}, "newCompany": "New Company"}, "templates": {"title": "Templates", "search": "Search", "searchPlaceholder": "Search...", "industryType": "Industry type", "status": "Status", "templateName": "Template name", "sectionCount": "Section count", "published": "Published", "draft": "Draft", "action": "Action", "newTemplate": "New Template", "all": "All", "loadingTemplates": "Loading templates...", "errorLoading": "Error loading templates", "unknownError": "Unknown error occurred", "retry": "Retry", "error": "Error", "noBusinessTypesFound": "No business types found", "noMatchingTemplates": "No templates match your search criteria", "noTemplatesFound": "No templates found", "templateLabel": "template", "tooltips": {"createNew": "Create New Template", "viewTemplate": "View Template", "editTemplate": "Edit Template", "deleteTemplate": "Delete Template"}}, "templateView": {"templateView": "Template View", "errorLoading": "Error loading template", "unknownError": "Unknown error occurred", "templateNotFound": "Temp<PERSON> not found", "templateName": "Template name", "industryType": "Industry type", "hasPreferredAnswer": "* has preferred answer"}, "assignment": {"respond": "Respond to Assignment", "title": "Checklist assignment", "search": "Search", "branch": "Branch", "status": "Status", "templateName": "Template name", "assignTo": "Assign To", "frequency": "Frequency", "startDue": "Start - Due", "pending": "Pending", "completed": "Completed", "in_progress": "In Progress", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "action": "Action", "assignmentChecklist": "Assignment Checklist"}, "checklistAssignments": {"loadingAssignments": "Loading assignments...", "errorLoading": "Error loading assignments", "searchByName": "Search by name", "selectStatus": "Select status", "inProgress": "In Progress", "checklistAssignment": "checklist assignment"}, "templateSelection": {"assignChecklistForm": "Assign checklist form", "usePrebuiltTemplate": "Use Prebuilt Template", "createCustomTemplate": "Create custom template", "failedToLoadTemplates": "Failed to load templates. Please try again later."}, "assignChecklistForm": {"targetBranch": "Target Branch *", "selectBranch": "Select branch", "loadingBranches": "Loading branches...", "errorLoadingBranches": "Error loading branches", "startDate": "Start Date", "dueDate": "Due Date", "notesInstructions": "Notes / Instructions (Optional)", "title": "Assign checklist form", "section": "Section {{number}}", "question": "Question", "answer": "Answer", "notes": "Notes/Instructions", "templateRequired": "Template is required", "branchRequired": "Branch is required", "validEmailRequired": "Please enter a valid email", "dueDateAfterStartDate": "Due date must be after start date", "assignmentUpdated": "Assignment updated successfully!", "failedToUpdateAssignment": "Failed to update assignment. Please try again.", "loadingForm": "Loading form...", "failedToLoadFormData": "Failed to load form data", "templateError": "Template error", "branchesError": "Branches error", "assignToEmail": "Assign to <PERSON><PERSON>", "enterUserEmail": "Enter user email", "selectDate": "Select date", "frequency": "Frequency (Optional)", "selectFrequency": "Select frequency", "passRate": "Pass Rate", "notesPlaceholder": "Focus on freezer units"}, "assignmentView": {"assignmentView": "Assignment View", "errorLoading": "Error loading assignment", "assignmentNotFound": "Assignment not found"}, "editAssignment": {"loadingAssignment": "Loading assignment...", "error": "Error", "editAssignment": "Edit Assignment", "assignmentDetails": "Assignment Details", "sectionsFields": "Sections & Fields", "moveSectionUp": "Move section up", "moveSectionDown": "Move section down", "moveFieldUp": "Move field up", "moveFieldDown": "Move field down"}, "branchManagement": {"title": "Branch Management", "addNewBranch": "Add New Branch", "newBranch": "New Branch", "showingToOfResults": "Showing {{0}} to {{1}} of {{2}} results", "addFilter": "Add Filter", "filtersTitle": "Filters", "noFiltersApplied": "No filters applied. Click \"Add Filter\" to start filtering your results.", "showOnMap": "Show on Map", "filters": {"status": "Status", "location": "Location", "branchManager": "Branch Manager"}, "table": {"storeName": "Store name", "location": "Location", "map": "Map", "status": "Status", "branchManager": "Branch Manager", "action": "Action", "errorLoading": "Error loading branches", "noBranches": "No branches found"}, "mapDialog": {"title": "Branch Location"}, "labels": {"branch": "Branch"}}, "global": {"showAll": "Show all", "previous": "Previous", "next": "Next", "pageOf": "Page {{0}} of {{1}}", "showingToOfResults": "Showing {{0}} to {{1}} of {{2}} results", "search": "Search", "remove": "Remove", "cancel": "Cancel", "delete": "Delete", "update": "Update", "overview": "Overview", "all": "All", "yes": "Yes", "no": "No", "retry": "Retry", "edit": "Edit", "save": "Save", "saving": "Saving...", "continue": "Continue", "template": "Template", "templates": "Templates", "assignments": "Assignments", "unknownError": "Unknown error occurred", "loading": "Loading...", "error": "Error", "optional": "Optional"}, "pagination": {"back": "← Back", "next": "Next →"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"delete": "Delete", "edit": "Edit"}, "submissionChecklist": {"title": "Checklist of submission", "searchPlaceholder": "Search by template name", "dateRange": "Date range", "startDate": "Start date", "endDate": "End date", "status": "Status", "table": {"userName": "User name", "templateName": "Template name", "submissionDate": "Submission date", "branch": "Branch", "status": "Status", "score": "Score", "action": "Action", "overdue": "Overdue", "onTime": "On Time", "passed": "Passed", "failed": "Failed"}, "showingToOfResults": "Showing {{0}} to {{1}} of {{2}} results"}, "userManagement": {"title": "User management", "searchPlaceholder": "Search by name", "searchByName": "Search by name", "rolePermissions": "Role & Permissions", "inviteUser": "Invite User", "role": "Role", "allRoles": "All Roles", "loadingUsers": "Loading users...", "failedToLoadUsers": "Failed to load users.", "deleteFailedAlert": "Failed to delete user invite", "active": "Active", "inactive": "Inactive", "roleTypes": {"admin": "admin", "employee": "employee", "store_manager": "store manager", "area_manager": "area manager", "auditor": "auditor", "checker": "checker", "viewer": "viewer"}, "table": {"userName": "User name", "role": "Role", "email": "Email", "branch": "Branch", "status": "Status", "action": "Action", "active": "Active"}, "pagination": {"back": "Back", "next": "Next"}, "deleteModal": {"userInvite": "User invite", "disclaimer": "This will delete all invites to this user to all branches"}}, "activityLog": {"title": "Activity log", "companyName": "Company name", "activityType": "Activity type", "date": "Date", "performedBy": "Performer by"}, "branchReport": {"title": "Branch Report System", "subtitle": "Submit and manage branch reports", "issueReports": "Issue Reports", "createReport": "Create Report", "table": {"title": "Title", "branchName": "Branch name", "date": "Date", "performedBy": "Performed by", "action": "Action", "status": "Status", "assignedTo": "Assigned To", "errorLoading": "Error loading reports", "noReports": "No reports found", "viewDetails": "View report details", "noDescription": "No description provided.", "noMedia": "No media found.", "reportMedia": "Report media", "report": "Report", "deleteFailed": "Failed to delete report: "}, "assignReport": "Assign Report", "assignReportDescription": "Assign report '{{reportTitle}}' to a maintainer for resolution", "maintainerEmail": "Maintainer <PERSON><PERSON>", "assignToMaintainer": "Assign to Maintainer", "assignSuccess": "Report assigned successfully", "assignFailed": "Failed to assign report", "markAsCompleted": "<PERSON> as Completed", "markAsCompletedDescription": "Mark report '{{reportTitle}}' as resolved and add completion notes", "addComments": "Add Comments", "addCommentsDescription": "Add comments to report '{{reportTitle}}'", "completionComments": "Completion Comments", "completionCommentsPlaceholder": "Describe how the issue was resolved...", "comments": "Comments", "commentsPlaceholder": "Add your comments or progress notes...", "markCompleted": "Mark Completed", "updateComments": "Update Comments", "markCompletedSuccess": "Report marked as completed successfully", "markCompletedFailed": "Failed to mark report as completed", "commentsUpdatedSuccess": "Comments updated successfully", "commentsUpdateFailed": "Failed to update comments", "myAssignments": "My Assignments", "myAssignmentsDescription": "View and manage your assigned maintenance tasks", "noAssignments": "No assignments found", "noAssignmentsDescription": "You don't have any assigned reports at the moment.", "assignmentDetails": "Assignment Details", "description": "Description", "assignedDate": "Assigned Date", "assignmentStatus": "Assignment Status", "assignedTo": "Assigned To", "resolutionComments": "Resolution Comments"}, "settings": {"title": "Setting", "tabs": {"branding": "Branding", "localization": "Localization", "defaults": "De<PERSON>ults", "users": "Users"}, "branding": {"title": "Branding", "loadingSettings": "Loading branding settings...", "noSettingsAvailable": "No branding settings available", "resetting": "Resetting...", "resetToDefaults": "Reset to defaults", "logo": "Logo :", "uploadLogo": "Upload logo", "primaryColor": "Primary colour :", "primaryColorDescription": "(Used in UI buttons and highlights)", "secondaryColor": "Secondary colour :", "secondaryColorDescription": "(Used in charts, badges, etc.)", "loginScreenMessage": "Login screen message", "loginMessagePlaceholder": "Write here...", "settingsCreated": "Branding settings created successfully", "settingsUpdated": "Branding settings updated successfully", "defaultsApplied": "Default branding settings applied successfully", "createFailed": "Failed to create branding settings", "updateFailed": "Failed to update branding settings", "defaultsFailed": "Failed to apply default branding settings"}, "localization": {"title": "Localization", "loadingSettings": "Loading localization settings...", "noSettingsAvailable": "No settings available", "resetting": "Resetting...", "resetToDefaults": "Reset to defaults", "language": "Language", "dateFormat": "Date format", "timeZone": "Time zone", "firstDayOfWeek": "First day of week", "settingsCreated": "Localization settings created successfully", "settingsUpdated": "Localization settings updated successfully", "defaultsApplied": "Default localization settings applied successfully", "createFailed": "Failed to create localization settings", "updateFailed": "Failed to update localization settings", "defaultsFailed": "Failed to apply default localization settings"}, "users": {"title": "Users", "loadingSettings": "Loading user settings...", "noSettingsAvailable": "No settings available", "resetting": "Resetting...", "resetToDefaults": "Reset to defaults", "enable2FA": "Enable 2FA for admins :", "sessionTimeout": "Session timeout :", "auditTrailVisibility": "Audit trail visibility", "settingsCreated": "User settings created successfully", "settingsUpdated": "User settings updated successfully", "defaultsApplied": "Default user settings applied successfully", "createFailed": "Failed to create user settings", "updateFailed": "Failed to update user settings", "defaultsFailed": "Failed to apply default user settings"}, "defaults": {"title": "De<PERSON>ults", "loadingSettings": "Loading defaults settings...", "noSettingsAvailable": "No settings available", "resetting": "Resetting...", "resetToDefaults": "Reset to defaults", "defaultChecklistFrequency": "Default checklist frequency :", "requireEvidenceByDefault": "Require evidence by default :", "requireEvidenceNote": "(If enable, photo upload by default on all new questions)", "allowEditAfterSubmission": "Allow edits after submission :", "allowEditNote": "(If enabled, users can edit checklists after submission)", "autoArchiveOldChecklists": "Auto-archive old checklists :", "autoArchiveNote": "(Automatically archive checklists older than X days)", "settingsCreated": "Defaults settings created successfully", "settingsUpdated": "Defaults settings updated successfully", "defaultsApplied": "Default defaults settings applied successfully", "createFailed": "Failed to create defaults settings", "updateFailed": "Failed to update defaults settings", "defaultsFailed": "Failed to apply default defaults settings"}}, "branding": {"title": "Branding", "logo": "Logo", "uploadLogo": "Upload logo", "primaryColor": "Primary colour", "primaryColorNote": "Used in UI buttons and highlights", "secondaryColor": "Secondary colour", "secondaryColorNote": "Used in charts, badges, etc.", "custom": "Custom", "loginScreenMessage": "Login screen message", "loginScreenPlaceholder": "Write here..."}, "localization": {"title": "Localization", "language": "Language", "dateFormat": "Date format", "timeZone": "Time zone", "firstDayOfWeek": "First day of week"}, "defaults": {"title": "De<PERSON>ults", "defaultChecklistFrequency": "Default checklist frequency", "requireEvidence": "Require evidence by default", "requireEvidenceNote": "If enable, photo upload by default on all new questions", "allowEdits": "Allow edits after submission", "allowEditsNote": "If enabled, users can edit checklists after submission", "autoArchiveChecklists": "Auto-archive old checklists", "autoArchiveChecklistsNote": "Automatically archive checklists older than X days"}, "users": {"title": "Users", "enable2FA": "Enable 2FA for admins", "sessionTimeout": "Session timeout", "auditTrailVisibility": "Audit trail visibility"}, "calendar": {"title": "Assignment calendar of July", "events": "Events", "test": "Test", "cleaningRoom": "Cleaning room", "pharmacyDaily": "Pharmacy Daily Check", "sound": "Sound", "assigned": "Assigned", "new": "New", "time": "Time", "prev": "Previous", "next": "Next", "assignmentCalendar": "Assignment Calendar", "pickMonth": "Pick a month"}, "login": {"title": "Welcome Back!", "subtitle": "Welcome Back! Please enter your details.", "email": "Email", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "logIn": "Log in", "loggingIn": "Logging in...", "orContinueWith": "OR CONTINUE WITH", "continueWithGoogle": "Continue with Google", "noAccount": "Don’t have an account? ", "signUp": "Sign Up", "language": "Language"}, "signup": {"title": "Create Account", "subtitle": "Please fill in your details to create an account.", "fullName": "Full Name", "namePlaceholder": "Enter your full name", "email": "Email", "emailPlaceholder": "<EMAIL>", "phone": "Phone Number", "phonePlaceholder": "Enter your phone number", "role": "Role", "selectRole": "Select your role", "password": "Password", "passwordPlaceholder": "••••••", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "••••••", "createAccount": "Create Account", "creatingAccount": "Creating Account...", "orContinueWith": "OR CONTINUE WITH", "continueWithGoogle": "Continue with Google", "alreadyHaveAccount": "Already have an account? ", "signIn": "Sign In", "language": "Language"}, "validation": {"nameMinLength": "Name must be at least 2 characters", "invalidEmail": "Please enter a valid email address", "passwordMinLength": "Password must be at least 6 characters", "confirmPassword": "Please confirm your password", "phoneRequired": "Phone number is required", "invalidPhone": "Please enter a valid phone number", "roleRequired": "Please select a role", "passwordMismatch": "Passwords don't match"}, "company": {"newCompanyTitle": "New company model", "editCompanyTitle": "Edit company model", "companyName": "Company name", "businessType": "Business type", "assignAdminEmail": "Assign admin email", "numberOfBranches": "Number of branches", "status": "Status", "active": "Active", "inactive": "Inactive", "create": "Create", "cancel": "Cancel", "creating": "Creating...", "updating": "Updating...", "update": "Update"}, "branch": {"newBranchTitle": "New branch", "editBranchTitle": "Edit branch", "selectStore": "Select store", "branchManagerEmail": "Branch manager email", "location": "Location", "status": "Status", "active": "Active", "inactive": "Inactive", "longitude": "Longitude", "latitude": "Latitude", "create": "Create", "cancel": "Cancel", "creating": "Creating...", "updating": "Updating...", "update": "Update", "createSuccess": "Branch created successfully!", "updateSuccess": "Branch updated successfully!", "createError": "Failed to create branch", "updateError": "Failed to update branch", "errorLoading": "Error loading branch", "noDataFound": "No data found"}, "templateBuilder": {"title": "New template builder", "templateName": "Template name", "industryType": "Industry type", "addSection": "Add section", "section": "Section {{number}}", "addField": "Add field", "field": "Field {{number}}", "questionText": "Question text", "questionType": "Question Type", "placeholder": "Placeholder", "addHint": "Add hint (Optional)", "scoreWeight": "Score weight", "scoreWeightNote": "Enter a value between 1 and 10", "required": "Required", "requireEvidence": "Require evidence", "passRate": "Pass rate", "passRatePlaceholder": "Enter pass rate percentage (0-100)", "create": "Create", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "creating": "Creating...", "updating": "Updating...", "update": "Update", "dummyText": "Lorem ipsum dummy text", "tooltipHint": "Tooltip appear in checklist", "newQuestion": "New question", "loadingTemplate": "Loading template...", "failedToLoadTemplate": "Failed to load template", "editTemplate": "Edit template"}, "reportingDashboard": {"title": "Reporting dashboard", "completionRate": "Completion rate", "onTime": "On time", "averageScore": "Average score", "failedItems": "Failed items", "refresh": "Refresh", "bestStoreByCompletion": "Best store by completion", "worstStoreByCompletion": "Worst store by completion", "topEmployeeByCompletion": "Top employee by completion", "onTimeDetail": "{{0}}% on-time", "completionDetail": "{{0}}% completion", "failedQuestionsByFrequency": "Failed questions by frequency", "submissionDistribution": "Submission distribution", "completionPassRateByBranch": "Completion & pass rate by branch", "selectBranch": "Select a branch", "monthly": "Monthly", "branchCompletionList": "Branch completion list", "searchByName": "Search by name", "loading": "Loading...", "error": "Error", "noDataFound": "No data found"}, "table": {"branchName": "Branch name", "completionPercent": "Completion %", "passingRate": "Passing rate", "onTimePercent": "On-time %", "failedChecklists": "Failed checklists", "action": "Action"}, "templateAnalytics": {"title": "Template analytics", "totalSubmissions": "Total submissions", "avgCompletionRate": "Avg. completion rate", "avgScore": "Avg. score", "onTimeRate": "On-time rate", "completionPassRateByBranch": "Completion & pass rate by branch", "templateSubmissionTrend": "Template submission trend", "submissionDistribution": "Submission distribution", "selectBranch": "Select branch", "monthly": "Monthly", "daily": "Daily", "allBranches": "All branches", "showingToOfResults": "Showing {{0}} to {{1}} of {{2}} results"}, "template": {"templateName": "Template name", "industryType": "Industry type", "test": "Test", "edit": "Edit", "addField": "Add field", "addSection": "Add section", "questionText": "Question text", "questionType": "Question Type", "placeholder": "Placeholder", "addHint": "Add hint (Optional)", "required": "Required", "requireEvidence": "Require evidence", "scoreWeight": "Score weight", "scoreWeightNote": "Enter a value between 1 and 10", "passRate": "Pass rate", "passRatePlaceholder": "Enter pass rate percentage (0-100)", "create": "Create", "cancel": "Cancel", "update": "Update", "delete": "Delete"}, "branch-table": {"branch": "Branch", "submissionDate": "Submission date", "status": "Status", "score": "Score", "action": "Action"}, "deleteDialog": {"deleteTemplateTitle": "Are you sure you want to delete this template?", "deleteAssignmentTitle": "Are you sure you want to delete this checklist assignment?", "no": "No", "yes": "Yes"}, "assignChecklist": {"title": "Assign checklist form", "usePrebuiltTemplate": "Use Prebuilt Template", "createCustomTemplate": "Create custom template", "template": "Template", "targetBranch": "Target Branch", "startDate": "Start Date", "dueDate": "Due Date", "assignTo": "Assign to (Optional)", "frequency": "Frequency", "passRate": "Pass Rate", "notes": "Notes / Instructions (Optional)", "save": "Save", "cancel": "Cancel"}, "templateGrid": {"test": "Test", "yesno": "yes/no", "scored": "Scored", "car": "car", "tech": "Tech", "templateWithPassRate": "Template with pass rate", "testWithPreferred2": "Test with preferred 2", "templateWithPreferredAnswer": "Template with Preferred answer", "testTemplate1": "Test Template 1", "page": "Page", "of": "of"}, "assignments": {"title": "Assignments", "templateName": "Template name", "industryType": "Industry type", "edit": "Edit", "section": "Section {{number}}", "dummyText": "Lorem ipsum dummy text"}, "checklistSubmission": {"title": "Checklist of submission", "searchByTemplateName": "Search by template name", "dateRange": "Date range", "startDate": "Start date", "endDate": "End date", "status": "Status", "userName": "User name", "submissionDate": "Submission date", "branch": "Branch", "score": "Score", "action": "Action", "onTime": "On Time", "overdue": "Overdue", "passed": "Passed", "failed": "Failed"}, "roles": {"title": "Role & Permission Matrix", "featureRole": "Feature / Role", "viewReports": "View Reports", "createEditChecklists": "Create/Edit Checklists", "assignTemplates": "Assign Templates", "submitChecklists": "Submit Checklists", "accessCalendarView": "Access Calendar View", "admin": "Admin", "checker": "Checker", "auditor": "Auditor", "viewer": "Viewer", "cancel": "Cancel", "save": "Save"}, "inviteUser": {"title": "Invite User", "form": {"email": {"label": "Email"}, "storeId": {"label": "Select Store", "placeholder": "Choose a store...", "validation": "Please select a store"}, "branchIds": {"label": "Branch Assignment", "placeholder": "Select branches...", "selectStoreFirst": "Please select a store first", "validation": "Select at least one branch"}, "status": {"label": "Status", "active": "Active", "inactive": "Inactive"}}, "buttons": {"cancel": "Cancel", "save": "Save", "saving": "Saving..."}}, "editUser": {"title": "Edit User", "email": "Email", "branchAssignment": "Branch Assignment", "status": "Status", "inactive": "Inactive", "active": "Active", "cancel": "Cancel", "save": "Save"}, "submitReport": {"title": "Submit Report", "createNewReport": "Create new report", "targetBranch": "Target Branch", "reportTitle": "Report Title", "email": "Email", "description": "Description", "attachment": "Attachment (Optional)", "uploadFileHint": "Click to upload or drag and drop", "fileTypesHint": "PNG, JPG, PDF up to 10MB", "cancel": "Cancel", "submitReport": "Submit Report"}, "filters": {"title": "Filters", "addFilter": "Add Filter", "storeAdminEmail": "Store Admin Email", "is": "is", "enterStoreAdminEmail": "Enter store admin email", "showingResults": "Showing {0} to {1} of {2} results"}, "common": {"loading": "Loading...", "installApp": "Install App", "notAvailable": "N/A", "addNewFilter": "Add new filter", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "yes": "Yes", "no": "No", "search": "Search", "searchPlaceholder": "Search...", "noOptionsFound": "No options found", "select": "Select", "selectAll": "Select All", "clear": "Clear", "add": "Add", "create": "Create", "update": "Update", "submit": "Submit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "view": "View", "actions": "Actions", "priority": "Priority"}, "permissions": {"accessForbidden": "Access Forbidden", "noPermission": "You don't have permission to access this page.", "requiredPermission": "Required permission:", "goBack": "Go Back", "goToAssignments": "Go to Assignments", "checkingPermissions": "Checking permissions..."}, "companyTable": {"noFiltersApplied": "No filters applied. Click \"Add Filter\" to start filtering your results.", "noCompaniesFound": "No companies found", "rooms": "Rooms", "deleteLabel": "Company"}, "filterRow": {"selectProperty": "Select property", "selectValue": "Select value", "enterValue": "Enter value", "is": "is"}, "deleteModal": {"message": "Are you sure you want to delete this item?"}, "clientSnapshot": {"show": "Show", "failedToLoad": "Failed to load client data. Please try again later.", "branch": "Branch", "branchPlural": "es"}, "assignmentResponse": {"respond": "Respond to Assignment", "assignmentCreatedSuccess": "Assignment created successfully!", "assignmentUpdatedSuccess": "Assignment updated successfully!", "failedToCreateAssignment": "Failed to create assignment. Please try again.", "failedToUpdateAssignment": "Failed to update assignment. Please try again.", "maximumImagesAllowed": "Maximum {{count}} images allowed per field", "fieldRequired": "This field is required", "pleaseSelectOption": "Please select an option", "assignmentSubmittedSuccess": "Assignment submitted successfully!", "errorSubmittingAssignment": "Error submitting assignment. Please try again.", "assignmentView": "Assignment View", "errorLoadingAssignment": "Error loading assignment", "unknownError": "Unknown error occurred", "assignmentNotFound": "Assignment not found", "assignChecklistForm": "Assign checklist form", "editChecklistForm": "Edit checklist form", "evidenceRequired": "Evidence required", "enterResponse": "Enter your response...", "no": "No", "uploadEvidenceImages": "Upload Evidence Images", "clickToUpload": "Click to upload images or drag and drop", "fileFormats": "PNG, JPG, GIF up to 10MB each", "evidenceAlt": "Evidence {{index}}", "imagesUploaded": "{{count}} image(s) uploaded", "submitting": "Submitting...", "submitAssignment": "Submit Assignment", "editingDisabled": "Editing Disabled", "editingDisabledMessage": "You have disabled editing responses after submission in your settings."}, "assignmentResponseHistory": {"unknownTemplate": "Unknown Template", "passed": "Passed", "failed": "Failed", "noScore": "No Score", "unknownUser": "Unknown User", "loadingSubmissions": "Loading submissions...", "failedToLoad": "Failed to load submissions.", "tryAgainLater": "Please try again later.", "checklistSubmission": "Checklist of submission", "searchPlaceholder": "Search by template name", "dateRange": "Date range", "startDate": "Start date", "to": "to", "endDate": "End date", "status": "Status", "all": "All", "onTime": "On time", "overdue": "Overdue", "userName": "User name", "templateName": "Template name", "submissionDate": "Submission date", "branch": "Branch", "score": "Score", "action": "Action", "noSubmissionsFound": "No submissions found matching your criteria", "showingResults": "Showing {{start}} to {{end}} of {{total}} results"}, "responseSuccessful": {"altText": "Successful response", "successMessage": "Checklist submitted successfully!", "historyButton": "History"}, "assignmentResponseDetail": {"loading": "Loading...", "error": "Error", "branch": "Branch", "question": "Q", "answer": "ANS", "auditResponse": "Audit response", "responseId": "Response ID"}, "advancedReporting": {"loading": "Loading...", "failedToLoad": "Failed to load data. Please try again.", "templateAnalytics": "Template analytics", "template": "Template", "selectTemplate": "Select template", "branch": "Branch", "selectBranch": "Select branch", "selectBothTemplateAndBranch": "Select both template and branch to view analytics.", "templateSubmissionTrend": "Template submission trend"}, "summaryCard": {"totalSubmissions": "Total submissions", "avgCompletionRate": "Avg. completion Rate", "avgScore": "Avg. score", "onTimeRate": "On-time rate"}, "lineChart": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly"}, "sidebar": {"dashboard": "Dashboard", "companies": "Companies", "templates": "Templates", "assignments": "Assignments", "responses": "Responses", "calendar": "Calendar", "userManagement": "User Management", "auditLog": "<PERSON>t Log", "branchReportSystem": "Branch Report System", "myAssignments": "My Assignments", "settings": "Settings", "logout": "Logout"}, "notifications": {"title": "Notifications", "settings": "Notification Settings", "all": "All", "reminder": "Reminder", "alert": "<PERSON><PERSON>", "escalated": "Escalated", "submitted": "Submitted", "cancelled": "Cancelled", "markAsRead": "<PERSON> as read", "markAsUnread": "<PERSON> as unread", "delete": "Delete", "escalation": "Escalation", "messagePlaceholder": "Type your message...", "escalationSettings": "Escalation Settings", "escalateAfter": "Escalate after", "escalateTo": "Escalate to", "customMessage": "Custom Message", "repeatEscalation": "Repeat Escalation", "repeatAfter": "Repeat after", "showAll": "Show All", "unreadOnly": "Unread Only", "loading": "Loading notifications...", "error": "Failed to load notifications. Please try again.", "retry": "Retry", "cardTitle": "Notifications", "total": "Total: {{total}}", "noUnread": "No unread notifications", "noNotifications": "No notifications found", "markRead": "<PERSON>", "view": "View"}, "reports": {"title": "Issue Reports", "targetBranch": "Target Branch", "reportTitle": "Report Title", "email": "Email", "description": "Description", "status": "Status", "createdAt": "Created At", "actions": "Actions"}, "header": {"usFlag": "US Flag", "notifications": "Notifications", "changeLanguage": "Change Language"}, "profile": {"profileSettings": "Profile Settings", "logout": "Log out"}, "inviteUserView": {"title": "View User Invite", "userInvite": "User Invite", "loading": "Loading...", "error": "Error loading invite data", "email": "Email", "selectedBranches": "Selected Branches", "status": "Status", "active": "Active", "inactive": "Inactive"}, "notificationsPage": {"title": "Notification centre", "filters": "Filters", "listTitle": "List of notification", "cardTitle": "Notification card", "escalationSettings": "Escalation settings", "escalationTime": "Escalation time", "escalationRole": "Escalation role", "escalationMessage": "Escalation message", "messagePlaceholder": "Type...", "repeatEscalation": "Repeat escalation", "repeatTime": "Repeat time", "hours": "hours", "auditor": "Auditor"}, "rolePermissionMatrix": {"title": "Role & Permission Matrix", "loading": "Loading permissions...", "error": "Error loading permissions", "save": "Save", "cancel": "Cancel", "features": {"view_reports": "View Reports", "create_edit_checklist": "Create/Edit Checklists", "assign_template": "Assign Templates", "submit_checklist": "Submit Checklists", "access_calendar_view": "Access Calendar View", "access_dashboard": "Access Dashboard", "view_checklist_assignments": "View Checklist Assignments", "view_assignment_responses": "View Assignment Responses", "view_templates": "View Templates", "view_stores": "View Stores", "view_branches": "View Branches", "crud_checklist_assignments": "CRUD Checklist Assignments", "crud_assignment_responses": "CRUD Assignment Responses", "crud_templates": "CRUD Templates", "crud_stores": "CRUD Stores", "crud_branches": "CRUD Branches", "view_modify_settings": "View/Modify Settings", "role_permission_matrix": "Role & Permission Matrix", "user_invite": "User Invite"}, "roles": {"ADMIN": "Admin", "STORE_MANAGER": "Store Manager", "BRANCH_MANAGER": "Branch Manager", "AUDITOR": "Auditor", "CHECKER": "Checker", "MAINTAINER": "Maintainer", "VIEWER": "Viewer"}}, "calendarComponent": {"assignmentCalendar": "Assignment calendar of", "pickMonth": "Pick a month", "dayNames": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "eventTypes": {"test": "Test", "cleaningRoom": "Cleaning room", "pharmacyDaily": "Pharmacy Daily Check", "sound": "Sound"}}, "emailVerification": {"title": "Email Verification", "checkYourEmail": "Check your email", "description": "We've sent a verification link to your email address. Please click the link to verify your account.", "didntReceive": "Didn't receive the email?", "resendLink": "Resend verification link", "resending": "Resending...", "linkSent": "Verification link sent!", "linkSentDescription": "A new verification link has been sent to your email address.", "verifying": "Verifying your email...", "verifyingDescription": "Please wait while we verify your email address.", "verificationSuccess": "Email verified successfully!", "verificationSuccessDescription": "Your email has been verified. You can now access your account.", "verificationFailed": "Verification failed", "verificationFailedDescription": "The verification link is invalid or has expired. Please request a new one.", "invalidLink": "Invalid verification link", "noTokenProvided": "The verification link appears to be incomplete or corrupted.", "backToLogin": "Back to login", "requestNewLink": "Request new verification link", "goToDashboard": "Go to home page", "redirectingToDashboard": "Redirecting to home page in 3 seconds...", "sentTo": "Sent to", "resendSuccess": "Verification email resent successfully!", "resendError": "Failed to resend verification email", "resendButton": "Resend verification email", "checkSpam": "Please also check your spam folder"}, "pwa": {"online": "Online", "offline": "Offline", "onlineDescription": "All features are available", "offlineDescription": "Limited features available - data will sync when reconnected", "appInstalled": "App Installed", "installedDescription": "Y-Verify is installed on your device", "installAvailable": "Install Available", "installDescription": "Install Y-Verify as an app for better experience", "installNotAvailable": "Install Not Available", "manualInstallHint": "Check your browser menu for install options", "install": "Install App", "installSuccess": "App installed successfully!", "installFailed": "Installation was cancelled or failed", "share": "Share App", "shareText": "Check out Y-Verify - Branch Report & Audit Management System", "shareSuccess": "App shared successfully!", "shareFailed": "Sharing failed", "urlCopied": "App URL copied to clipboard!", "updateAvailable": "Update Available", "updateDescription": "A new version of Y-Verify is ready to install", "update": "Update Now", "updatingApp": "Updating app...", "instructions": "How to Install", "howToInstall": "How to Install Y-Verify", "features": "App Features", "feature1": "Work offline with cached data", "feature2": "Fast loading and native feel", "feature3": "Push notifications for assignments", "feature4": "Home screen shortcuts", "appFeatures": "Progressive Web App Features", "offlineSupport": "Works offline with cached data", "nativeExperience": "Native app-like experience", "responsive": "Responsive Design", "allDevices": "Works on all devices and screen sizes", "installed": "Installed", "browser": "Browser"}}