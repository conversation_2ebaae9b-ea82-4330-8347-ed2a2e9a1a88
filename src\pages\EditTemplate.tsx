import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import TemplateForm from "@/components/TemplateForm";
import { toast } from "sonner";
import { fetchTemplateById } from "@/data/templates";
import { transformTemplateData } from "@/lib/utils";
import { useTranslation } from "react-i18next";

const EditTemplate = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { templateId } = useParams<{ templateId: string }>();

  const {
    data: template,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["template", templateId],
    queryFn: () => fetchTemplateById(templateId),
    enabled: !!templateId,
  });

  const handleSuccess = () => {
    navigate("/templates");
  };

  const handleCancel = () => {
    navigate("/templates");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen">
        <div className="p-6 w-full">
          <div className="flex justify-center items-center h-64">
            <div className="text-gray-500">
              {t("templateBuilder.loadingTemplate")}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    toast.error(t("templateBuilder.failedToLoadTemplate") + ": " + error);
    return (
      <div className="min-h-screen">
        <div className="p-6 w-full">
          <div className="flex items-center space-x-4 mb-6">
            <Link to="/templates">
              <Button variant="ghost" size="sm">
                <ChevronLeft className="w-4 h-4" />
              </Button>
            </Link>
          </div>
          <div className="flex justify-center items-center h-64">
            <div className="text-red-500">Failed to load template</div>
          </div>
        </div>
      </div>
    );
  }

  const defaultValues = template ? transformTemplateData(template) : undefined;

  return (
    <div className="min-h-screen">
      <div className="p-6 w-full">
        <div className="flex items-center space-x-4 mb-6">
          <Link to="/templates">
            <Button variant="ghost" size="sm">
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </Link>
        </div>

        <h1 className="mb-6 font-bold text-gray-900 text-2xl">
          {t("templateBuilder.editTemplate")}
        </h1>

        <TemplateForm
          defaultValues={defaultValues}
          isEdit={true}
          templateId={templateId}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};

export default EditTemplate;
