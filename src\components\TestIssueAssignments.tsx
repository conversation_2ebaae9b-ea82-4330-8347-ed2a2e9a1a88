import React from "react";
import { useQuery } from "@tanstack/react-query";
import { fetchIssueAssignments } from "@/data/issueAssignments";

const TestIssueAssignments: React.FC = () => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["test-issue-assignments"],
    queryFn: () => fetchIssueAssignments({ limit: 10 }),
  });

  if (isLoading) return <div>Loading assignments...</div>;
  if (error) return <div>Error: {String(error)}</div>;

  return (
    <div className="p-4">
      <h2 className="mb-4 font-bold text-xl">Issue Assignments Test</h2>
      {data?.assignments.length === 0 ? (
        <p>No assignments found</p>
      ) : (
        <div>
          <p>Found {data?.total} assignments</p>
          <ul className="space-y-2">
            {data?.assignments.map((assignment) => (
              <li key={assignment.id} className="p-2 border rounded">
                <div>Issue ID: {assignment.issueId}</div>
                <div>Status: {assignment.status}</div>
                <div>
                  Assigned to: {assignment.maintenanceEmployeeName || "N/A"}
                </div>
                <div>Comments: {assignment.comments || "N/A"}</div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default TestIssueAssignments;
