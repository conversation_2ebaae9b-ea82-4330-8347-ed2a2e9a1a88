import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate, useParams } from "react-router-dom";
import { z } from "zod";
import { useTranslation } from "react-i18next";

import { fetchAllBranches } from "@/data/branches";
import { fetchInviteByIdForEdit } from "@/data/users";

import { Switch } from "@/components/ui/switch";
import { ChevronRight } from "lucide-react";

export default function InviteUserView() {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();

  const {
    data: inviteData,
    isLoading: isInviteLoading,
    error: inviteError,
  } = useQuery({
    queryKey: ["invite", id],
    queryFn: () => fetchInviteByIdForEdit(id!),
    enabled: !!id,
  });

  const { data: branchesResult, isLoading: branchesLoading } = useQuery({
    queryKey: ["branches"],
    queryFn: fetchAllBranches,
    staleTime: 5 * 60 * 1000,
  });

  const branchesOptions =
    branchesResult?.map((branch) => {
      if (!inviteData?.branchIds.includes(branch.id)) return undefined;
      return {
        label: `${branch.storeName} - ${branch.location}`,
        value: branch.id,
      };
    }) ?? [];

  if (isInviteLoading || branchesLoading) {
    return <div className="p-8 text-center">{t("inviteUserView.loading")}</div>;
  }

  if (inviteError) {
    return <div className="p-8 text-red-500">{t("inviteUserView.error")}</div>;
  }
  return (
    <div className="space-y-6 p-8">
      <div className="flex items-center gap-2 text-lg">
        <span className="font-medium text-muted-foreground">
          {t("userManagement.title")}
        </span>
        <ChevronRight className="w-6 h-6 text-muted-foreground" />
        <span className="font-medium text-primary">
          {t("inviteUserView.userInvite")}
        </span>
      </div>

      <h2 className="font-bold text-primary text-2xl">
        {t("inviteUserView.title")}
      </h2>

      <div className="gap-6 grid grid-cols-1 lg:grid-cols-2">
        <div className="flex flex-col gap-2">
          <span className="font-bold text-primary">
            {t("inviteUserView.email")}
          </span>
          <p className="">{inviteData?.email}</p>
        </div>
        <div className="flex flex-col gap-2">
          <span className="font-bold text-primary">
            {t("inviteUserView.selectedBranches")}
          </span>
          <ul className="flex flex-col gap-1">
            {branchesOptions.map(
              (branch) => branch && <li key={branch.value}>{branch.label}</li>
            )}
          </ul>
        </div>

        <div className="flex flex-col gap-2">
          <span className="text-primary">{t("inviteUserView.status")}</span>

          <div className="flex items-center gap-4 pt-2">
            <span className="text-muted-foreground text-sm">
              {t("inviteUserView.inactive")}
            </span>
            <Switch
              checked={inviteData.status === "active"}
              disabled
              className="data-[state=checked]:bg-primary"
            />
            <span className="text-primary text-sm">
              {t("inviteUserView.active")}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
