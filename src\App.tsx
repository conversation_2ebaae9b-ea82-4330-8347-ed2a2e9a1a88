import { Toaster as SonnerToaster } from "@/components/ui/sonner";
import { Toaster as AppToaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Suspense, lazy, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import { NotificationContextProvider } from "./components/NotificationContext";
import { RoutePermissionWrapper } from "./components/PermissionWrapper";
import { DashboardWrapper } from "./components/DashboardWrapper";
import { AuthProvider } from "./contexts/AuthProvider";
import { ThemeProvider } from "./contexts/ThemeProvider";
import NotificationProvider from "./contexts/NotificationProvider";
import MainLayout from "./layouts/MainLayout";
import Auth from "./pages/Auth";
import EditBranch from "./pages/EditBranch";
import EditCompany from "./pages/EditCompany";
import InviteUserView from "./pages/InviteUserView";
// Lazy-loaded pages
const Index = lazy(() => import("./pages/Index"));
const NotFound = lazy(() => import("./pages/NotFound"));
const SignIn = lazy(() => import("./pages/SignIn"));
const SignUp = lazy(() => import("./pages/SignUp"));
const Companies = lazy(() => import("./pages/Companies"));
const NewCompany = lazy(() => import("./pages/NewCompany"));
const CompanyManagement = lazy(() => import("./pages/CompanyManagement"));
const BranchManagement = lazy(() => import("./pages/Branches"));
const NewBranch = lazy(() => import("./pages/NewBranch"));
const Templates = lazy(() => import("./pages/Templates"));
const TemplateViewType = lazy(() => import("./pages/TemplateView"));
const NewTemplate = lazy(() => import("./pages/NewTemplate"));
const EditTemplate = lazy(() => import("./pages/EditTemplate"));
const Assignments = lazy(() => import("./pages/ChecklistAssignments"));
const NewAssignment = lazy(() => import("./pages/NewAssignment"));
const EditAssignment = lazy(() => import("./pages/EditAssignment"));
const AssignmentView = lazy(() => import("./pages/AssignmentView"));
const AssignmentResponse = lazy(() => import("./pages/AssignmentResponse"));
const EditAssignmentResponse = lazy(
  () => import("./pages/EditAssignmentResponse")
);
const ResponseSuccessful = lazy(() => import("./pages/ResponseSuccessful"));
const AssignmentResponseHistory = lazy(
  () => import("./pages/AssignmentResponseHistory")
);
const AssignmentResponseDetail = lazy(
  () => import("./pages/AssignmentResponseDetail")
);
const ReportingDashboard = lazy(() => import("./pages/ReportingDashboard"));
const AdvancedReporting = lazy(() => import("./pages/AdvancedReporting"));
const Sandbox = lazy(() => import("./pages/Sandbox"));
const AssignmentCalendar = lazy(() => import("./pages/AssignmentCalendar"));
const NotificationsPage = lazy(() => import("./pages/NotificationsPage"));
const SettingsPage = lazy(() => import("./pages/Settings"));
const UserManagement = lazy(() => import("./pages/UserManagement"));
const InviteUser = lazy(() => import("./pages/InviteUser"));
const RolePermissionMatrix = lazy(() => import("./pages/RolePermissionMatrix"));
const EmailVerificationPending = lazy(
  () => import("./pages/EmailVerificationPending")
);
const EmailVerification = lazy(() => import("./pages/EmailVerification"));
const InviteUserEdit = lazy(() => import("./pages/EditInvite"));
const ReportSubmission = lazy(() => import("./pages/ReportSubmission"));
const ReportMain = lazy(() => import("./pages/ReportMain"));
const AuditLogPage = lazy(() => import("./pages/AuditLogPage"));
const ReportEdit = lazy(() => import("./pages/ReportEdit"));
const MyAssignments = lazy(() => import("./pages/MyAssignments"));
const PWATest = lazy(() => import("./pages/PWATest"));

const routes = [
  // Home page with special dashboard wrapper
  {
    path: "/",
    element: (
      <DashboardWrapper>
        <Index />
      </DashboardWrapper>
    ),
  },

  // Auth pages - no permissions needed
  { path: "/auth", element: <Auth /> },
  { path: "/signin", element: <SignIn /> },
  { path: "/signup", element: <SignUp /> },

  // Email verification pages - no permissions needed
  {
    path: "/email-verification-pending",
    element: <EmailVerificationPending />,
  },
  { path: "/verify", element: <EmailVerification /> },

  // Company management routes
  { path: "/companies/new", element: <NewCompany /> },
  { path: "/companies/:storeId/branches", element: <BranchManagement /> },
  { path: "/companies/branches/new", element: <NewBranch /> },
  { path: "/companies/branches/edit/:branchId", element: <EditBranch /> },
  { path: "/companies/edit/:storeId", element: <EditCompany /> },
  { path: "/companies", element: <CompanyManagement /> },

  // Template routes
  { path: "/templates", element: <Templates /> },
  { path: "/templates/new", element: <NewTemplate /> },
  { path: "/templates/:templateId", element: <TemplateViewType /> },
  { path: "/templates/:templateId/edit", element: <EditTemplate /> },

  // Assignment routes
  { path: "/assignments", element: <Assignments /> },
  { path: "/assignments/new", element: <NewAssignment /> },
  { path: "/assignments/:assignmentId", element: <AssignmentView /> },
  { path: "/assignments/:assignmentId/edit", element: <EditAssignment /> },

  // Assignment response routes
  {
    path: "/assignment-response/:assignmentId",
    element: <AssignmentResponse />,
  },
  {
    path: "/assignment-response/edit/:responseId",
    element: <EditAssignmentResponse />,
  },
  { path: "/response-successful", element: <ResponseSuccessful /> },
  {
    path: "/assignment-response/history",
    element: <AssignmentResponseHistory />,
  },
  {
    path: "/assignment-response/response/:responseId",
    element: <AssignmentResponseDetail />,
  },

  // Reporting routes
  { path: "/reporting-dashboard/:storeId", element: <ReportingDashboard /> },
  { path: "/advanced-reporting/:storeId", element: <AdvancedReporting /> },

  // User management routes
  { path: "/user-management", element: <UserManagement /> },
  { path: "/invite-user", element: <InviteUser /> },
  { path: "/invite-user/:id", element: <InviteUserView /> },
  { path: "/edit-invite/:id", element: <InviteUserEdit /> },
  { path: "/roles", element: <RolePermissionMatrix /> },

  // Calendar route
  { path: "/calendar", element: <AssignmentCalendar /> },

  // Notification routes
  { path: "/notifications", element: <NotificationsPage /> },
  { path: "/audit-log", element: <AuditLogPage /> },

  // Settings route
  { path: "/settings", element: <SettingsPage /> },

  // Report routes
  { path: "/report-submission", element: <ReportSubmission /> },
  { path: "/report", element: <ReportMain /> },
  { path: "/report/edit/:reportId", element: <ReportEdit /> },

  // Maintainer routes
  { path: "/my-assignments", element: <MyAssignments /> },

  // Testing and development routes - no permissions needed
  { path: "/pwa-test", element: <PWATest /> },
  { path: "/sandbox", element: <Sandbox /> },

  // 404 - no permissions needed
  { path: "*", element: <NotFound /> },
];

const App = () => {
  const { i18n } = useTranslation();
  useEffect(() => {
    document.documentElement.lang = i18n.language;
    document.documentElement.dir = i18n.language === "ar" ? "rtl" : "ltr";
  }, [i18n.language]);

  return (
    <ThemeProvider>
      <NotificationContextProvider position="top-right" maxNotifications={5}>
        <NotificationProvider>
          <TooltipProvider>
            <AppToaster />
            <SonnerToaster />
            <div className="font-satoshi">
              <BrowserRouter>
                <Suspense fallback={<div className="p-4">Loading...</div>}>
                  <Routes>
                    <Route element={<MainLayout />}>
                      {routes.map(({ path, element }) => (
                        <Route
                          key={path}
                          path={path}
                          element={
                            // Skip permission check for auth, signin, signup, email verification, sandbox, and 404 pages
                            path === "/auth" ||
                            path === "/signin" ||
                            path === "/signup" ||
                            path === "/email-verification-pending" ||
                            path === "/verify" ||
                            path === "/sandbox" ||
                            path === "*" ? (
                              element
                            ) : (
                              <RoutePermissionWrapper>
                                {element}
                              </RoutePermissionWrapper>
                            )
                          }
                        />
                      ))}
                    </Route>
                  </Routes>
                </Suspense>
              </BrowserRouter>
            </div>
          </TooltipProvider>
        </NotificationProvider>
      </NotificationContextProvider>
    </ThemeProvider>
  );
};
export default App;
