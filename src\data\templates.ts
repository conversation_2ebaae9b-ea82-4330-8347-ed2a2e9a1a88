import { TemplateFormData } from "@/components/TemplateForm";
import { supabase } from "@/integrations/supabase/client";
import { fetchWithToken } from "@/lib/fetchWithToken";
import { Template, type TemplateViewType } from "@/lib/types";

// API functions

// Fetch All Templates
export const fetchTemplates = async ({
  page,
  limit,
  search,
  businessType,
}: {
  page: number;
  limit: number;
  search?: string;
  businessType?: string;
}): Promise<{ data: Template[]; totalPages: number }> => {
  const params = new URLSearchParams();
  params.set("page", page.toString());
  params.set("limit", limit.toString());
  if (search) params.set("search", search);
  if (businessType && businessType !== "all")
    params.set("businessType", businessType);
  const res = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/templates?${params}`
  );

  if (!res.ok) throw new Error("Failed to fetch templates");

  const response = await res.json();

  const enrichedTemplates = await Promise.all(
    response.data.map(async (template: Template) => {
      const countRes = await fetchWithToken(
        `${process.env.API_ENDPOINT}/api/templates/sections/count?templateId=${template.id}`
      );
      const countData = await countRes.json();
      return { ...template, sectionsCount: countData.data.total };
    })
  );

  return {
    data: enrichedTemplates,
    totalPages: response.pagination.totalPages ?? 1,
  };
};
// Fetch business types
export const fetchBusinessTypes = async (): Promise<{ data: string[] }> => {
  const res = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/templates/business-types/all`
  );

  if (!res.ok) throw new Error("Failed to fetch templates");

  const response = await res.json();

  return {
    data: response.data,
  };
};
// Delete Template
export const deleteTemplate = async (id: string): Promise<void> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/templates/${id}`,
    {
      method: "DELETE",
    }
  );
  if (!response.ok) {
    throw new Error("Failed to delete template");
  }
};
// Fetch Template By Id
export const fetchTemplateById = async (
  id: string
): Promise<TemplateViewType> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/templates/${id}`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch template");
  }
  const data = await response.json();
  return data.data;
};
// Create Template
export const createTemplate = async (templateData: TemplateFormData) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/templates`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(templateData),
    }
  );

  if (!response.ok) {
    throw new Error((await response.json()).message);
  }

  return response.json();
};
// Update Template
export const updateTemplate = async (
  templateId: string,
  templateData: TemplateFormData
) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/templates/hierarchy/${templateId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(templateData),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to update template");
  }

  return response.json();
};
