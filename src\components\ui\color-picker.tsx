import React, { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";

interface ColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  presetColors?: string[];
  className?: string;
}

const defaultPresetColors = [
  "#FF9B22", // Orange
  "#665CEF", // Purple
  "#6685FA", // Light Blue
  "#1191E5", // Blue
  "#EE609C", // Pink
  "#119EA1", // Teal
];

export const ColorPicker: React.FC<ColorPickerProps> = ({
  value,
  onChange,
  presetColors = defaultPresetColors,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [customColor, setCustomColor] = useState(value);
  const pickerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handlePresetClick = (color: string) => {
    onChange(color);
    setCustomColor(color);
    setIsOpen(false);
  };

  const handleCustomColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value;
    setCustomColor(newColor);
    onChange(newColor);
  };

  return (
    <div ref={pickerRef} className="relative">
      <div className="flex items-center gap-3">
        {/* Preset Colors */}
        <div className="flex gap-3">
          {presetColors.map((color) => (
            <button
              key={color}
              type="button"
              onClick={() => handlePresetClick(color)}
              className={cn(
                "border-2 rounded-full w-[30px] h-[30px] hover:scale-110 transition-all",
                value === color ? "border-gray-400" : "border-gray-200"
              )}
              style={{ backgroundColor: color }}
            >
              <span className="sr-only">preset colors</span>
            </button>
          ))}
        </div>

        {/* Custom Color Section */}
        <div className="flex items-center gap-2 ml-3">
          <span className="font-medium text-black-text text-sm">Custom</span>
          <div className="flex items-center gap-1 shadow-sm px-3 py-2 border border-field-stroke rounded-full">
            <span className="text-gray-text">#</span>
            <input
              type="text"
              value={customColor.replace("#", "")}
              onChange={(e) => {
                const newValue = `#${e.target.value}`;
                setCustomColor(newValue);
                onChange(newValue);
              }}
              className="bg-transparent border-none outline-none w-16 text-black-text text-sm"
              maxLength={6}
            />
          </div>
          <span className="font-mono text-gray-500 text-xs">
            Current: {value}
          </span>
          <div className="relative">
            <input
              type="color"
              value={value}
              onChange={handleCustomColorChange}
              className={cn(
                "border-2 rounded-full w-[30px] h-[30px] hover:scale-110 transition-all cursor-pointer",
                "border-gray-400"
              )}
              style={{ backgroundColor: value }}
              title="Choose color"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
