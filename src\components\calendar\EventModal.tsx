import {
  <PERSON>,
  Map<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  BarChart3,
  Calendar as CalendarIcon,
} from "lucide-react";
import { cn, getEventType, getEventTypeColor } from "@/lib/utils";
import { Link } from "react-router-dom";
import { CalendarReturn } from "@/data/assignments";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useDateFormatter } from "@/hooks/useLocalizationSettings";

interface EventModalProps {
  event: CalendarReturn;
  onClose: () => void;
}

export function EventModal({ event, onClose }: EventModalProps) {
  const { formatDate } = useDateFormatter();

  return (
    <div className="z-50 fixed inset-0 flex justify-center items-center bg-black/50 p-4">
      <div className="bg-primary-bg shadow-xl rounded-xl w-full max-w-[446px] max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-gray-100 border-b">
          <div className="flex items-center gap-2.5">
            <div
              className={cn(
                "shadow-md border-4 border-white rounded-full w-5 h-5",
                getEventTypeColor(getEventType(event))
              )}
            />
            <h2 className="font-bold text-foreground text-lg">
              {event.calendarString}
            </h2>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <button
                  onClick={onClose}
                  className="flex justify-center items-center hover:bg-gray-100 rounded w-[18px] h-[18px] text-foreground"
                >
                  <X className="w-4 h-4" />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Close modal</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Content */}
        <div className="space-y-6 p-4">
          {/* Location */}
          <div className="flex items-center gap-2.5">
            <MapPin className="w-[22px] h-[22px] text-gray" />
            <span className="font-medium text-gray text-base">
              {event.branchLocation}
            </span>
          </div>

          {/* Time */}
          <div className="flex items-center gap-2.5">
            <Clock className="w-[22px] h-[22px] text-gray" />
            <span className="font-medium text-gray text-base">
              {formatDate(new Date(event.startDate))} -{" "}
              {formatDate(new Date(event.dueDate))}
            </span>
          </div>

          {/* Assignee */}
          <div className="flex items-center gap-2.5">
            <Users className="w-[22px] h-[22px] text-gray" />
            <span className="font-medium text-gray text-base">
              {event.assignee}
            </span>
          </div>

          {/* Date Fields */}
          <div className="space-y-4">
            <div className="flex gap-2.5">
              <div className="flex-1">
                <label className="block mb-2 font-medium text-foreground text-base">
                  Start Date
                </label>
                <div className="flex justify-between items-center shadow-sm px-4 py-2.5 border border-border rounded-full">
                  <span className="font-medium text-foreground text-base">
                    {formatDate(new Date(event.startDate))}
                  </span>
                  <CalendarIcon className="w-[22px] h-[22px] text-foreground" />
                </div>
              </div>
              <div className="flex-1">
                <label className="block mb-2 font-medium text-foreground text-base">
                  Due Date
                </label>
                <div className="flex justify-between items-center shadow-sm px-4 py-2.5 border border-border rounded-full">
                  <span className="font-medium text-foreground text-base">
                    {formatDate(new Date(event.dueDate))}
                  </span>
                  <CalendarIcon className="w-[22px] h-[22px] text-foreground" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex sm:flex-row flex-col items-stretch sm:items-center gap-2.5 p-4 border-gray-100 border-t">
          <Link to="/assignments">
            <button className="bg-primary hover:bg-primary/90 px-4 sm:px-6 py-2.5 rounded-full font-bold text-white text-sm sm:text-base transition-colors">
              Reschedule
            </button>
          </Link>
          <Link to={`/assignments/${event.id}/edit`}>
            <button className="bg-primary hover:bg-primary/90 px-4 sm:px-6 py-2.5 rounded-full font-bold text-white text-sm sm:text-base transition-colors">
              Edit
            </button>
          </Link>
          <Link to={`/assignment-response/${event.id}`}>
            <button className="flex-1 bg-primary hover:bg-primary/90 px-4 sm:px-6 py-2.5 rounded-full font-bold text-white text-sm sm:text-base transition-colors">
              View submissions
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
}
