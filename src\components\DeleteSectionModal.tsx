import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useTranslation } from "react-i18next";

interface DeleteSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  labelToDelete?: string;
  disclaimer?: string;
  isPending?: boolean;
}

export const DeleteSectionModal = ({
  isOpen,
  onClose,
  onConfirm,
  labelToDelete = "Section",
  disclaimer,
  isPending,
}: DeleteSectionModalProps) => {
  const { t } = useTranslation();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <div className="flex flex-col items-center p-6 text-center">
          {/* Trash Icon */}
          <svg
            width="166"
            height="166"
            viewBox="0 0 166 166"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="83" cy="83" r="83" fill="#F4F4F4" />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M114.315 123.121C114.632 122.794 114.843 122.361 114.886 121.875L120.516 58.9269C120.623 57.727 119.678 56.6934 118.473 56.6934H56.1805C54.9757 56.6934 54.0305 57.727 54.1377 58.9269L59.7674 121.875C59.8107 122.361 60.0211 122.794 60.3388 123.121C62.5739 126.261 73.8063 128.649 87.3266 128.649C100.847 128.649 112.08 126.261 114.315 123.121Z"
              fill="#FF4B54"
            />
            <mask
              id="mask0_169_343"
              style={{ maskType: "luminance" }}
              maskUnits="userSpaceOnUse"
              x="54"
              y="56"
              width="67"
              height="73"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M114.315 123.121C114.632 122.794 114.843 122.361 114.886 121.875L120.516 58.9269C120.623 57.727 119.678 56.6934 118.473 56.6934H56.1805C54.9757 56.6934 54.0305 57.727 54.1377 58.9269L59.7674 121.875C59.8107 122.361 60.0211 122.794 60.3388 123.121C62.5739 126.261 73.8063 128.649 87.3266 128.649C100.847 128.649 112.08 126.261 114.315 123.121Z"
                fill="white"
              />
            </mask>
            <g mask="url(#mask0_169_343)">
              <path
                d="M114.315 123.121L114.039 122.853L114.019 122.874L114.002 122.898L114.315 123.121ZM60.339 123.121L60.6523 122.898L60.6354 122.874L60.6149 122.853L60.339 123.121ZM114.591 123.389C114.968 123.001 115.218 122.486 115.269 121.909L114.503 121.841C114.468 122.236 114.297 122.588 114.039 122.853L114.591 123.389ZM115.269 121.909L120.899 58.9613L120.133 58.8928L114.503 121.841L115.269 121.909ZM120.899 58.9613C121.026 57.5365 119.904 56.3091 118.473 56.3091V57.0781C119.452 57.0781 120.22 57.9179 120.133 58.8928L120.899 58.9613ZM118.473 56.3091H56.1808V57.0781H118.473V56.3091ZM56.1808 56.3091C54.7501 56.3091 53.6276 57.5365 53.755 58.9613L54.521 58.8928C54.4338 57.9179 55.2018 57.0781 56.1808 57.0781V56.3091ZM53.755 58.9613L59.3847 121.909L60.1506 121.841L54.521 58.8928L53.755 58.9613ZM59.3847 121.909C59.4362 122.486 59.6861 123.001 60.0632 123.389L60.6149 122.853C60.3565 122.588 60.1857 122.236 60.1506 121.841L59.3847 121.909ZM87.3269 128.265C80.587 128.265 74.4257 127.67 69.6791 126.687C67.3051 126.195 65.2948 125.608 63.7469 124.956C62.1853 124.297 61.1471 123.593 60.6523 122.898L60.0258 123.344C60.6485 124.219 61.8528 124.992 63.4483 125.664C65.0574 126.342 67.1195 126.942 69.523 127.439C74.3316 128.435 80.5465 129.034 87.3269 129.034V128.265ZM114.002 122.898C113.507 123.593 112.469 124.297 110.907 124.955C109.359 125.608 107.349 126.195 104.975 126.687C100.228 127.67 94.0668 128.265 87.3269 128.265V129.034C94.1073 129.034 100.323 128.435 105.131 127.439C107.534 126.942 109.597 126.342 111.206 125.664C112.801 124.992 114.005 124.219 114.628 123.344L114.002 122.898Z"
                fill="black"
              />
            </g>
            <path
              d="M120.319 58.7606C120.319 59.7248 119.483 60.704 117.809 61.6361C116.151 62.5592 113.74 63.3965 110.744 64.1022C104.755 65.5129 96.4707 66.387 87.314 66.387C78.1572 66.387 69.8733 65.5129 63.8842 64.1022C60.888 63.3965 58.4768 62.5592 56.8192 61.6361C55.1453 60.704 54.3091 59.7248 54.3091 58.7606C54.3091 57.7968 55.1453 56.8175 56.8192 55.8854C58.4768 54.9623 60.888 54.1248 63.8842 53.4194C69.8733 52.0087 78.1572 51.1343 87.314 51.1343C96.4707 51.1343 104.755 52.0087 110.744 53.4194C113.74 54.1248 116.151 54.9623 117.809 55.8854C119.483 56.8175 120.319 57.7968 120.319 58.7606Z"
              fill="#FAF3F3"
              stroke="black"
              strokeWidth={1.05469}
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M111.478 64.0527C117.037 62.6631 120.443 60.7938 120.443 58.7379C120.443 54.4476 105.616 50.9697 87.3266 50.9697C69.0371 50.9697 54.2104 54.4476 54.2104 58.7379C54.2104 60.7938 57.6158 62.6631 63.1755 64.0527C69.2168 62.5428 77.8022 61.5997 87.3266 61.5997C96.851 61.5997 105.436 62.5428 111.478 64.0527Z"
              fill="black"
            />
            <mask
              id="mask1_169_343"
              style={{ maskType: "luminance" }}
              maskUnits="userSpaceOnUse"
              x="54"
              y="50"
              width="67"
              height="15"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M111.478 64.0527C117.037 62.6631 120.443 60.7938 120.443 58.7379C120.443 54.4476 105.616 50.9697 87.3266 50.9697C69.0371 50.9697 54.2104 54.4476 54.2104 58.7379C54.2104 60.7938 57.6158 62.6631 63.1755 64.0527C69.2168 62.5428 77.8022 61.5997 87.3266 61.5997C96.851 61.5997 105.436 62.5428 111.478 64.0527Z"
                fill="white"
              />
            </mask>
            <g mask="url(#mask1_169_343)">
              <path
                d="M111.478 64.0525L111.385 64.4255L111.478 64.4488L111.571 64.4255L111.478 64.0525ZM63.1757 64.0525L63.0824 64.4255L63.1757 64.4488L63.2688 64.4255L63.1757 64.0525ZM120.058 58.7376C120.058 59.5418 119.382 60.4241 117.852 61.3124C116.351 62.1845 114.149 62.9884 111.385 63.6795L111.571 64.4255C114.366 63.7269 116.647 62.9015 118.238 61.9774C119.801 61.0696 120.828 59.9894 120.828 58.7376H120.058ZM87.3268 51.354C96.4511 51.354 104.699 52.222 110.656 53.6191C113.637 54.3184 116.023 55.1454 117.654 56.0501C118.47 56.5025 119.079 56.9647 119.48 57.4249C119.88 57.8824 120.058 58.3198 120.058 58.7376H120.828C120.828 58.0829 120.543 57.4731 120.06 56.9193C119.579 56.3679 118.887 55.8545 118.027 55.3777C116.307 54.4235 113.843 53.5768 110.831 52.8703C104.802 51.456 96.4921 50.585 87.3268 50.585V51.354ZM54.5952 58.7376C54.5952 58.3198 54.7742 57.8824 55.1733 57.4249C55.5747 56.9647 56.1841 56.5025 56.9998 56.0501C58.6309 55.1454 61.0168 54.3184 63.9981 53.6191C69.9546 52.222 78.2026 51.354 87.3268 51.354V50.585C78.1616 50.585 69.8516 51.456 63.8225 52.8703C60.8109 53.5768 58.3471 54.4235 56.6268 55.3777C55.7667 55.8545 55.0746 56.3679 54.5939 56.9193C54.1107 57.4731 53.8262 58.0829 53.8262 58.7376H54.5952ZM63.2688 63.6795C60.5043 62.9884 58.3028 62.1845 56.8014 61.3124C55.272 60.4241 54.5952 59.5418 54.5952 58.7376H53.8262C53.8262 59.9894 54.8523 61.0696 56.4153 61.9774C58.0065 62.9015 60.2872 63.7269 63.0824 64.4255L63.2688 63.6795ZM63.2688 64.4255C69.2709 62.9253 77.8232 61.984 87.3268 61.984V61.215C77.7814 61.215 69.163 62.1596 63.0824 63.6795L63.2688 64.4255ZM87.3268 61.984C96.8305 61.984 105.383 62.9253 111.385 64.4255L111.571 63.6795C105.49 62.1596 96.872 61.215 87.3268 61.215V61.984Z"
                fill="black"
              />
            </g>
            <path
              d="M83.7893 77.0895C83.7893 75.072 85.4248 73.4365 87.4422 73.4365C89.4597 73.4365 91.0952 75.072 91.0952 77.0895V113.235C91.0952 115.252 89.4597 116.888 87.4422 116.888C85.4248 116.888 83.7893 115.252 83.7893 113.235V77.0895Z"
              fill="#FAF3F3"
              stroke="black"
              strokeWidth={1.05469}
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M83.647 111.478V113.386C83.647 115.493 85.3555 117.202 87.463 117.202C89.5704 117.202 91.2787 115.493 91.2787 113.386V111.479C91.2782 113.586 89.5699 115.294 87.463 115.294C85.3555 115.294 83.647 113.585 83.647 111.478Z"
              fill="black"
            />
            <mask
              id="mask2_169_343"
              style={{ maskType: "luminance" }}
              maskUnits="userSpaceOnUse"
              x="83"
              y="111"
              width="9"
              height="7"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M83.647 111.478V113.386C83.647 115.493 85.3555 117.202 87.463 117.202C89.5704 117.202 91.2787 115.493 91.2787 113.386V111.479C91.2782 113.586 89.5699 115.294 87.463 115.294C85.3555 115.294 83.647 113.585 83.647 111.478Z"
                fill="white"
              />
            </mask>
            <g mask="url(#mask2_169_343)">
              <path
                d="M91.279 111.479H91.6635H90.8944H91.279ZM83.2627 111.478V113.386H84.0317V111.478H83.2627ZM83.2627 113.386C83.2627 115.706 85.1433 117.586 87.4632 117.586V116.817C85.568 116.817 84.0317 115.281 84.0317 113.386H83.2627ZM87.4632 117.586C89.7829 117.586 91.6635 115.706 91.6635 113.386H90.8944C90.8944 115.281 89.3582 116.817 87.4632 116.817V117.586ZM91.6635 113.386V111.479H90.8944V113.386H91.6635ZM87.4632 115.679C89.7827 115.679 91.663 113.798 91.6635 111.479L90.8944 111.479C90.8939 113.374 89.3579 114.91 87.4632 114.91V115.679ZM83.2627 111.478C83.2627 113.798 85.1433 115.679 87.4632 115.679V114.91C85.568 114.91 84.0317 113.373 84.0317 111.478H83.2627Z"
                fill="black"
              />
            </g>
            <path
              d="M71.8239 74.9416C71.6678 73.0764 70.1085 71.6421 68.2371 71.6421C66.1307 71.6421 64.4742 73.4429 64.65 75.5419L67.6844 111.794C67.8405 113.659 69.3999 115.093 71.2715 115.093C73.3779 115.093 75.0342 113.293 74.8583 111.193L71.8239 74.9416Z"
              fill="#FAF3F3"
              stroke="black"
              strokeWidth={1.05469}
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M66.5965 71.7456C66.3402 72.3142 66.2197 72.9548 66.2758 73.6244L69.3177 109.902C69.483 111.872 71.13 113.386 73.107 113.386C73.6646 113.386 74.1924 113.267 74.6674 113.053C74.0742 114.367 72.7525 115.294 71.199 115.294C69.222 115.294 67.575 113.78 67.4099 111.81L64.3681 75.5324C64.2286 73.8705 65.1763 72.3857 66.5965 71.7456Z"
              fill="black"
            />
            <mask
              id="mask3_169_343"
              style={{ maskType: "luminance" }}
              maskUnits="userSpaceOnUse"
              x="64"
              y="71"
              width="11"
              height="45"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M66.5965 71.7456C66.3402 72.3142 66.2197 72.9548 66.2758 73.6244L69.3177 109.902C69.483 111.872 71.13 113.386 73.107 113.386C73.6646 113.386 74.1924 113.267 74.6674 113.053C74.0742 114.367 72.7525 115.294 71.199 115.294C69.222 115.294 67.575 113.78 67.4099 111.81L64.3681 75.5324C64.2286 73.8705 65.1763 72.3857 66.5965 71.7456Z"
                fill="white"
              />
            </mask>
            <g mask="url(#mask3_169_343)">
              <path
                d="M66.5968 71.7456L66.9475 71.9037L67.3656 70.9775L66.4389 71.3951L66.5968 71.7456ZM74.6677 113.053L75.0184 113.211L75.4363 112.285L74.5098 112.702L74.6677 113.053ZM66.2464 71.5874C65.9637 72.2139 65.8314 72.9201 65.8929 73.6566L66.6594 73.5923C66.6089 72.9896 66.7171 72.4141 66.9475 71.9037L66.2464 71.5874ZM65.8929 73.6566L68.935 109.934L69.7012 109.869L66.6594 73.5923L65.8929 73.6566ZM68.935 109.934C69.1167 112.103 70.9304 113.771 73.1073 113.771V113.002C71.3303 113.002 69.8496 111.64 69.7012 109.869L68.935 109.934ZM73.1073 113.771C73.7205 113.771 74.3022 113.639 74.8259 113.404L74.5098 112.702C74.0835 112.894 73.6095 113.002 73.1073 113.002V113.771ZM71.1993 115.679C72.9107 115.679 74.3657 114.657 75.0184 113.211L74.3173 112.895C73.7836 114.077 72.5951 114.91 71.1993 114.91V115.679ZM67.027 111.842C67.209 114.011 69.0227 115.679 71.1993 115.679V114.91C69.4223 114.91 67.9419 113.548 67.7932 111.777L67.027 111.842ZM63.9852 75.5644L67.027 111.842L67.7932 111.777L64.7514 75.5003L63.9852 75.5644ZM66.4389 71.3951C64.876 72.0993 63.8316 73.7338 63.9852 75.5644L64.7514 75.5003C64.6263 74.0073 65.4771 72.6717 66.755 72.0962L66.4389 71.3951Z"
                fill="black"
              />
            </g>
            <path
              d="M103.317 74.9416C103.473 73.0764 105.032 71.6421 106.904 71.6421C109.01 71.6421 110.667 73.4429 110.491 75.5419L107.456 111.794C107.3 113.659 105.741 115.093 103.869 115.093C101.763 115.093 100.106 113.293 100.282 111.193L103.317 74.9416Z"
              fill="#FAF3F3"
              stroke="black"
              strokeWidth={1.05469}
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M100.394 113.053C100.987 114.367 102.309 115.294 103.862 115.294C105.839 115.294 107.486 113.78 107.652 111.81L110.693 75.5324C110.833 73.8705 109.885 72.3857 108.465 71.7456C108.721 72.3142 108.842 72.9548 108.786 73.6244L105.744 109.902C105.578 111.872 103.931 113.386 101.954 113.386C101.397 113.386 100.869 113.267 100.394 113.053Z"
              fill="black"
            />
            <mask
              id="mask4_169_343"
              style={{ maskType: "luminance" }}
              maskUnits="userSpaceOnUse"
              x="100"
              y="71"
              width="11"
              height="45"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M100.394 113.053C100.987 114.367 102.309 115.294 103.862 115.294C105.839 115.294 107.486 113.78 107.652 111.81L110.693 75.5324C110.833 73.8705 109.885 72.3857 108.465 71.7456C108.721 72.3142 108.842 72.9548 108.786 73.6244L105.744 109.902C105.578 111.872 103.931 113.386 101.954 113.386C101.397 113.386 100.869 113.267 100.394 113.053Z"
                fill="white"
              />
            </mask>
            <g mask="url(#mask4_169_343)">
              <path
                d="M100.394 113.053L100.552 112.702L99.6255 112.285L100.044 113.211L100.394 113.053ZM108.465 71.7456L108.623 71.3951L107.696 70.9775L108.114 71.9037L108.465 71.7456ZM103.863 114.91C102.467 114.91 101.278 114.077 100.745 112.895L100.044 113.211C100.696 114.657 102.151 115.679 103.863 115.679V114.91ZM107.269 111.777C107.12 113.548 105.64 114.91 103.863 114.91V115.679C106.039 115.679 107.853 114.011 108.035 111.842L107.269 111.777ZM110.311 75.5003L107.269 111.777L108.035 111.842L111.077 75.5644L110.311 75.5003ZM108.307 72.0962C109.585 72.6717 110.436 74.0073 110.311 75.5003L111.077 75.5644C111.23 73.7338 110.186 72.0993 108.623 71.3951L108.307 72.0962ZM108.114 71.9037C108.345 72.4141 108.453 72.9896 108.403 73.5923L109.169 73.6566C109.231 72.9201 109.098 72.2139 108.816 71.5874L108.114 71.9037ZM108.403 73.5923L105.361 109.869L106.127 109.934L109.169 73.6566L108.403 73.5923ZM105.361 109.869C105.212 111.64 103.732 113.002 101.955 113.002V113.771C104.132 113.771 105.945 112.103 106.127 109.934L105.361 109.869ZM101.955 113.002C101.452 113.002 100.978 112.894 100.552 112.702L100.236 113.404C100.76 113.639 101.341 113.771 101.955 113.771V113.002Z"
                fill="black"
              />
            </g>
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M85.9006 46.9113L108.448 40.8696L110.565 48.7677L110.55 48.7718C111.193 53.3264 96.7354 60.9907 77.8105 66.0615C58.8853 71.1324 42.5328 71.7238 40.812 67.4581L40.7975 67.462L40.7369 67.2356C40.7357 67.2315 40.7345 67.2271 40.7334 67.2228C40.7322 67.2187 40.7311 67.2143 40.73 67.21L38.6812 59.5636L61.2288 53.522C64.9728 52.1541 69.0805 50.8539 73.4236 49.6901C77.7666 48.5265 81.9743 47.5985 85.9006 46.9113Z"
              fill="#FF4B54"
            />
            <mask
              id="mask5_169_343"
              style={{ maskType: "luminance" }}
              maskUnits="userSpaceOnUse"
              x="38"
              y="40"
              width="73"
              height="31"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M85.9006 46.9113L108.448 40.8696L110.565 48.7677L110.55 48.7718C111.193 53.3264 96.7354 60.9907 77.8105 66.0615C58.8853 71.1324 42.5328 71.7238 40.812 67.4581L40.7975 67.462L40.7369 67.2356C40.7357 67.2315 40.7345 67.2271 40.7334 67.2228C40.7322 67.2187 40.7311 67.2143 40.73 67.21L38.6812 59.5636L61.2288 53.522C64.9728 52.1541 69.0805 50.8539 73.4236 49.6901C77.7666 48.5265 81.9743 47.5985 85.9006 46.9113Z"
                fill="white"
              />
            </mask>
            <g mask="url(#mask5_169_343)">
              <path
                d="M85.9003 46.911L85.9667 47.2899L85.9834 47.2868L85.9998 47.2824L85.9003 46.911ZM108.448 40.8694L108.82 40.7699L108.72 40.3984L108.349 40.498L108.448 40.8694ZM110.564 48.7675L110.664 49.1389L111.036 49.0395L110.936 48.668L110.564 48.7675ZM110.55 48.7716L110.45 48.4001L110.122 48.4881L110.169 48.8252L110.55 48.7716ZM40.8117 67.4579L41.1684 67.3138L41.041 66.9982L40.7122 67.0864L40.8117 67.4579ZM40.7972 67.4617L40.4258 67.5612L40.5253 67.9326L40.8967 67.8332L40.7972 67.4617ZM40.7366 67.2353L41.108 67.1359L41.1074 67.1336L40.7366 67.2353ZM40.7297 67.2097L41.1018 67.1126L41.1011 67.1103L40.7297 67.2097ZM38.6809 59.5633L38.5814 59.1922L38.21 59.2916L38.3095 59.6631L38.6809 59.5633ZM61.2286 53.5217L61.3283 53.8932L61.3447 53.8888L61.3606 53.8829L61.2286 53.5217ZM85.9998 47.2824L108.548 41.2408L108.349 40.498L85.8009 46.5396L85.9998 47.2824ZM108.077 40.9688L110.193 48.8672L110.936 48.668L108.82 40.7699L108.077 40.9688ZM110.465 48.396L110.45 48.4001L110.65 49.143L110.664 49.1389L110.465 48.396ZM110.169 48.8252C110.234 49.2848 110.113 49.8165 109.769 50.4248C109.425 51.0333 108.869 51.6955 108.106 52.3987C106.58 53.805 104.274 55.3305 101.338 56.883C95.4706 59.9853 87.1534 63.1597 77.7105 65.6898L77.9097 66.4327C87.3918 63.892 95.7659 60.699 101.698 57.5628C104.661 55.9957 107.032 54.4343 108.627 52.9642C109.425 52.2292 110.041 51.5055 110.439 50.8034C110.836 50.101 111.027 49.3968 110.931 48.7177L110.169 48.8252ZM77.7105 65.6898C68.2679 68.22 59.4777 69.6294 52.8449 69.8765C49.526 70.0003 46.7664 69.8322 44.7418 69.3771C43.7292 69.1498 42.9171 68.8542 42.3144 68.4991C41.7125 68.1444 41.342 67.7442 41.1684 67.3138L40.4552 67.6017C40.7117 68.2377 41.2287 68.7522 41.9242 69.1618C42.6192 69.5712 43.5149 69.8898 44.5731 70.1275C46.6897 70.603 49.5237 70.7699 52.8736 70.645C59.5787 70.3954 68.4273 68.9734 77.9097 66.4327L77.7105 65.6898ZM40.7122 67.0864L40.6977 67.0903L40.8967 67.8332L40.9113 67.8291L40.7122 67.0864ZM41.1686 67.3622L41.108 67.1359L40.3652 67.3351L40.4258 67.5612L41.1686 67.3622ZM41.1074 67.1336C41.1064 67.13 41.1055 67.1267 41.1045 67.1231L40.3617 67.3223C40.3631 67.3271 40.3644 67.3323 40.3659 67.3374L41.1074 67.1336ZM41.1045 67.1231C41.1036 67.1197 41.1027 67.1159 41.1018 67.1126L40.3576 67.3069C40.359 67.312 40.3603 67.3171 40.3617 67.3223L41.1045 67.1231ZM41.1011 67.1103L39.0523 59.4639L38.3095 59.6631L40.3583 67.3092L41.1011 67.1103ZM38.7804 59.9348L61.3283 53.8932L61.1291 53.1505L38.5814 59.1922L38.7804 59.9348ZM61.3606 53.8829C65.0935 52.5191 69.1902 51.2223 73.523 50.0613L73.3239 49.3184C68.9705 50.485 64.8518 51.7885 61.0968 53.1605L61.3606 53.8829ZM73.523 50.0613C77.8556 48.9005 82.052 47.9751 85.9667 47.2899L85.8342 46.5324C81.8959 47.2217 77.6774 48.152 73.3239 49.3184L73.523 50.0613Z"
                fill="black"
              />
            </g>
            <path
              d="M108.262 40.9193C108.398 41.4253 108.316 41.9924 108.007 42.6194C107.698 43.2482 107.167 43.9265 106.424 44.6425C104.939 46.0742 102.638 47.6282 99.6862 49.2088C93.7859 52.3683 85.3243 55.614 75.7012 58.1923C66.0784 60.7709 57.1277 62.1908 50.4381 62.4049C47.0914 62.5118 44.3216 62.3167 42.3195 61.8194C41.3183 61.5707 40.519 61.2488 39.9371 60.8588C39.3565 60.47 39.0019 60.0201 38.8663 59.514C38.7307 59.0077 38.8129 58.441 39.1211 57.8137C39.4301 57.1851 39.9614 56.5066 40.7041 55.7908C42.1893 54.3589 44.4905 52.8051 47.4421 51.2245C53.3428 48.0647 61.8041 44.8194 71.4271 42.241C81.0502 39.6624 90.0006 38.2424 96.6906 38.0284C100.037 37.9214 102.807 38.1166 104.809 38.6139C105.81 38.8626 106.609 39.1844 107.191 39.5744C107.772 39.9634 108.127 40.4133 108.262 40.9193Z"
              fill="#FF4B54"
              stroke="black"
              strokeWidth={1.05469}
            />
            <path
              d="M102.734 42.4007C102.823 42.7322 102.746 43.1229 102.471 43.5789C102.197 44.0352 101.735 44.5376 101.093 45.0762C99.8107 46.1526 97.8442 47.3447 95.3343 48.5777C90.3173 51.0422 83.1647 53.6531 75.0669 55.8229C66.9691 57.9926 59.4694 59.3079 53.892 59.6822C51.1019 59.8693 48.8029 59.8201 47.1541 59.5292C46.3292 59.3836 45.6781 59.1793 45.212 58.9214C44.7462 58.6637 44.4842 58.3638 44.3955 58.0323C44.3066 57.7011 44.3835 57.3105 44.658 56.8542C44.9328 56.3979 45.3943 55.8954 46.0362 55.3568C47.3184 54.2804 49.2849 53.0884 51.795 51.8554C56.812 49.3908 63.9647 46.7802 72.0625 44.6102C80.1603 42.4405 87.66 41.1251 93.2371 40.7511C96.0272 40.5638 98.3262 40.6129 99.9753 40.9039C100.8 41.0495 101.451 41.2538 101.917 41.5117C102.383 41.7693 102.645 42.0693 102.734 42.4007Z"
              fill="#FF4B54"
              stroke="black"
              strokeWidth={1.05469}
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M101.467 45.0099C102.596 44.0045 103.12 43.0983 102.92 42.3508C102.062 39.1483 88.2233 40.0738 72.0111 44.418C55.7988 48.7619 43.3518 54.8796 44.2098 58.0819C44.41 58.8294 45.3177 59.3521 46.7974 59.6587C46.702 59.5336 46.6343 59.399 46.5959 59.2552C45.8094 56.3198 57.4489 50.6504 72.5937 46.5924C87.7386 42.5344 100.653 41.6243 101.44 44.5598C101.479 44.7036 101.487 44.8538 101.467 45.0099Z"
              fill="black"
            />
            <mask
              id="mask6_169_343"
              style={{ maskType: "luminance" }}
              maskUnits="userSpaceOnUse"
              x="44"
              y="40"
              width="59"
              height="20"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M101.467 45.0099C102.596 44.0045 103.12 43.0983 102.92 42.3508C102.062 39.1483 88.2233 40.0738 72.0111 44.418C55.7988 48.7619 43.3518 54.8796 44.2098 58.0819C44.41 58.8294 45.3177 59.3521 46.7974 59.6587C46.702 59.5336 46.6343 59.399 46.5959 59.2552C45.8094 56.3198 57.4489 50.6504 72.5937 46.5924C87.7386 42.5344 100.653 41.6243 101.44 44.5598C101.479 44.7036 101.487 44.8538 101.467 45.0099Z"
                fill="white"
              />
            </mask>
            <g mask="url(#mask6_169_343)">
              <path
                d="M101.467 45.0097L101.086 44.961L100.956 45.981L101.723 45.2968L101.467 45.0097ZM46.7973 59.6585L46.7194 60.0351L47.7263 60.2437L47.1034 59.4257L46.7973 59.6585ZM102.548 42.4501C102.614 42.6964 102.572 43.0053 102.355 43.397C102.137 43.7903 101.758 44.235 101.211 44.7226L101.723 45.2968C102.304 44.779 102.752 44.2678 103.028 43.7698C103.305 43.2701 103.425 42.7523 103.291 42.2511L102.548 42.4501ZM72.1104 44.789C80.2 42.6216 87.687 41.3098 93.2487 40.9379C96.0326 40.7518 98.3142 40.8025 99.9409 41.0904C100.755 41.2345 101.383 41.4342 101.824 41.6782C102.263 41.9217 102.478 42.1865 102.548 42.4501L103.291 42.2511C103.147 41.7138 102.741 41.3073 102.196 41.0053C101.652 40.7038 100.932 40.4846 100.075 40.333C98.359 40.0294 96.0068 39.9826 93.1974 40.1705C87.5724 40.5467 80.0342 41.8697 71.9115 44.0464L72.1104 44.789ZM44.5812 57.9822C44.5104 57.7187 44.5635 57.3824 44.8226 56.9515C45.0818 56.5203 45.5258 56.0332 46.1592 55.501C47.4241 54.4385 49.3744 53.2536 51.8786 52.0229C56.881 49.5643 64.0208 46.9567 72.1104 44.789L71.9115 44.0464C63.7886 46.2228 56.5988 48.846 51.5392 51.3328C49.0124 52.5748 46.9985 53.7914 45.6645 54.9122C44.9982 55.4718 44.484 56.0222 44.1633 56.5554C43.8426 57.0889 43.6945 57.6441 43.8383 58.1814L44.5812 57.9822ZM46.8755 59.2822C46.1577 59.1332 45.6073 58.9374 45.2218 58.7059C44.8378 58.4755 44.647 58.2286 44.5812 57.9822L43.8383 58.1814C43.9726 58.6826 44.3358 59.071 44.8257 59.3652C45.3138 59.6585 45.9572 59.8772 46.7194 60.0351L46.8755 59.2822ZM47.1034 59.4257C47.0357 59.337 46.9919 59.2473 46.9673 59.1555L46.2244 59.3547C46.2769 59.5503 46.3682 59.7295 46.4915 59.8915L47.1034 59.4257ZM46.9673 59.1555C46.9062 58.9282 46.9521 58.6293 47.1936 58.2358C47.4353 57.8423 47.8493 57.3955 48.4418 56.9051C49.6246 55.9256 51.4477 54.8307 53.7889 53.6902C58.4655 51.4123 65.1372 48.9882 72.6931 46.9636L72.4942 46.2207C64.9055 48.2541 58.1848 50.6937 53.4521 52.9988C51.0886 54.1501 49.2029 55.2765 47.9511 56.3126C47.3264 56.8299 46.8419 57.3391 46.5384 57.8336C46.2346 58.3278 46.0887 58.8482 46.2244 59.3547L46.9673 59.1555ZM72.6931 46.9636C80.2493 44.939 87.2391 43.7026 92.4281 43.3368C95.026 43.1537 97.1524 43.1907 98.6664 43.4473C99.4247 43.5757 100.007 43.7559 100.413 43.9759C100.819 44.1958 101.008 44.4316 101.069 44.6593L101.812 44.4601C101.676 43.9538 101.289 43.576 100.779 43.2996C100.269 43.0233 99.5946 42.8246 98.7951 42.689C97.1929 42.4175 94.9965 42.385 92.3743 42.5698C87.123 42.9397 80.0829 44.1873 72.4942 46.2207L72.6931 46.9636ZM101.069 44.6593C101.093 44.7508 101.1 44.8505 101.086 44.961L101.849 45.0584C101.875 44.8564 101.864 44.6557 101.812 44.4601L101.069 44.6593Z"
                fill="black"
              />
            </g>
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M78.2048 46.998L67.937 49.7494L68.9247 53.4352L69.0001 53.415C69.5397 53.7582 71.7381 53.5401 74.2732 52.8607C76.8082 52.1817 78.8213 51.2711 79.1171 50.7041L79.1923 50.6838L79.1482 50.5195C79.1482 50.519 79.1479 50.5185 79.1479 50.518C79.1477 50.5175 79.1476 50.5169 79.1474 50.5164L78.2048 46.998Z"
              fill="#FF4B54"
            />
            <mask
              id="mask7_169_343"
              style={{ maskType: "luminance" }}
              maskUnits="userSpaceOnUse"
              x="67"
              y="46"
              width="13"
              height="8"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M78.2048 46.998L67.937 49.7494L68.9247 53.4352L69.0001 53.415C69.5397 53.7582 71.7381 53.5401 74.2732 52.8607C76.8082 52.1817 78.8213 51.2711 79.1171 50.7041L79.1923 50.6838L79.1482 50.5195C79.1482 50.519 79.1479 50.5185 79.1479 50.518C79.1477 50.5175 79.1476 50.5169 79.1474 50.5164L78.2048 46.998Z"
                fill="white"
              />
            </mask>
            <g mask="url(#mask7_169_343)">
              <path
                d="M67.9367 49.7492L67.8373 49.3777L67.4658 49.4772L67.5653 49.8486L67.9367 49.7492ZM78.2045 46.9978L78.576 46.8983L78.4763 46.5269L78.1048 46.6263L78.2045 46.9978ZM68.9244 53.4349L68.553 53.5344L68.6525 53.9059L69.0239 53.8064L68.9244 53.4349ZM68.9998 53.4147L69.2059 53.0901L69.0634 52.9994L68.9001 53.0432L68.9998 53.4147ZM79.1169 50.7038L79.0171 50.3324L78.8541 50.3762L78.7759 50.5259L79.1169 50.7038ZM79.192 50.6835L79.2917 51.055L79.6629 50.9555L79.5634 50.5841L79.192 50.6835ZM79.1479 50.5192L78.7757 50.6154L78.7767 50.6187L79.1479 50.5192ZM79.1471 50.5162L78.7757 50.6154L78.7767 50.6187L79.1471 50.5162ZM68.0362 50.1204L78.304 47.3692L78.1048 46.6263L67.8373 49.3777L68.0362 50.1204ZM69.2959 53.3352L68.3082 49.6494L67.5653 49.8486L68.553 53.5344L69.2959 53.3352ZM68.9001 53.0432L68.825 53.0635L69.0239 53.8064L69.0993 53.7861L68.9001 53.0432ZM74.1734 52.489C72.9209 52.8246 71.7579 53.0445 70.8486 53.1386C70.3931 53.1855 70.0101 53.2001 69.7153 53.1845C69.401 53.1676 69.2505 53.1186 69.2059 53.0901L68.7935 53.7392C69.0188 53.8825 69.3456 53.9348 69.6743 53.9523C70.0229 53.971 70.4492 53.953 70.9276 53.9035C71.8863 53.8043 73.0901 53.5757 74.3724 53.2319L74.1734 52.489ZM78.7759 50.5259C78.7513 50.5728 78.6457 50.6905 78.3819 50.8622C78.1345 51.0232 77.7954 51.2021 77.3775 51.389C76.5429 51.7625 75.426 52.1535 74.1734 52.489L74.3724 53.2319C75.6549 52.8884 76.8118 52.4847 77.6916 52.0912C78.1307 51.8948 78.5088 51.6972 78.8013 51.5067C79.0774 51.3272 79.3343 51.1183 79.4576 50.8817L78.7759 50.5259ZM79.0925 50.3124L79.0171 50.3324L79.2163 51.0752L79.2917 51.055L79.0925 50.3124ZM78.7767 50.6187L78.8205 50.7833L79.5634 50.5841L79.5193 50.4198L78.7767 50.6187ZM78.7762 50.6172C78.7762 50.6169 78.7759 50.6161 78.7757 50.6154L79.5204 50.4231C79.5198 50.4213 79.5193 50.4195 79.5191 50.4182L78.7762 50.6172ZM78.7767 50.6187C78.7764 50.6179 78.7762 50.6177 78.7762 50.6172L79.5191 50.4182C79.5186 50.4167 79.5181 50.4152 79.5175 50.4134L78.7767 50.6187ZM77.8331 47.0975L78.7757 50.6154L79.5186 50.4167L78.576 46.8983L77.8331 47.0975Z"
                fill="black"
              />
            </g>
            <path
              d="M78.001 47.1442C78.0349 47.1608 78.0485 47.1736 78.0533 47.1795C78.052 47.187 78.0467 47.2049 78.0256 47.2364C77.9895 47.2905 77.9218 47.3608 77.8144 47.4448C77.6006 47.6117 77.2663 47.8045 76.8293 48.0098C75.9579 48.4195 74.7136 48.863 73.3034 49.2408C71.8933 49.6187 70.5938 49.8568 69.6346 49.9378C69.1534 49.9783 68.7674 49.9786 68.4987 49.9409C68.3636 49.9219 68.27 49.8948 68.2116 49.8661C68.1778 49.8494 68.1642 49.8366 68.1593 49.8307C68.1606 49.8233 68.166 49.8053 68.187 49.7738C68.2231 49.7197 68.2905 49.6494 68.3982 49.5654C68.6118 49.3985 68.9463 49.2057 69.3834 49.0001C70.2547 48.5907 71.499 48.1472 72.9092 47.7694C74.3194 47.3915 75.6188 47.1534 76.578 47.0724C77.0592 47.0319 77.4453 47.0316 77.7139 47.0693C77.849 47.0883 77.9426 47.1154 78.001 47.1442Z"
              fill="#FF4B54"
            />
            <path
              d="M78.0538 47.1754C78.0538 47.1754 78.0538 47.1757 78.0538 47.1762M68.1567 49.8271C68.1567 49.8271 68.157 49.8274 68.1573 49.8279M68.1588 49.8348C68.1588 49.8348 68.1588 49.8345 68.1588 49.834M78.0554 47.1823C78.0556 47.1826 78.0559 47.1829 78.0559 47.1829M78.001 47.1442C78.0349 47.1608 78.0485 47.1736 78.0533 47.1795C78.052 47.187 78.0467 47.2049 78.0256 47.2364C77.9895 47.2905 77.9218 47.3608 77.8144 47.4448C77.6006 47.6117 77.2663 47.8045 76.8293 48.0098C75.9579 48.4195 74.7136 48.863 73.3034 49.2408C71.8933 49.6187 70.5938 49.8568 69.6346 49.9378C69.1534 49.9783 68.7674 49.9786 68.4987 49.9409C68.3636 49.9219 68.27 49.8948 68.2116 49.8661C68.1778 49.8494 68.1642 49.8366 68.1593 49.8307C68.1606 49.8233 68.166 49.8053 68.187 49.7738C68.2231 49.7197 68.2905 49.6494 68.3982 49.5654C68.6118 49.3985 68.9463 49.2057 69.3834 49.0001C70.2547 48.5907 71.499 48.1472 72.9092 47.7694C74.3194 47.3915 75.6188 47.1534 76.578 47.0724C77.0592 47.0319 77.4453 47.0316 77.7139 47.0693C77.849 47.0883 77.9426 47.1154 78.001 47.1442Z"
              stroke="black"
              strokeWidth={1.05469}
            />
            <path
              d="M76.4472 60.4487L77.3291 63.7397M67.2328 62.9179L68.1146 66.2089M85.6619 57.9798L86.5438 61.2708M59.0527 63.9812L59.9345 67.2719M93.2783 54.8103L94.1602 58.1013M51.3281 64.6397L52.21 67.9308M100.297 51.5188L101.178 54.8098M43.8481 64.1045L44.7297 67.3955M106.507 47.3149L107.389 50.6059"
              stroke="black"
              strokeWidth={1.05469}
              strokeLinecap="round"
            />
            <path
              d="M50.9095 76.2483C50.891 76.159 50.8123 76.0952 50.7213 76.0952C50.6303 76.0952 50.5516 76.159 50.5332 76.2483L49.1874 82.7208L42.6084 84.0459C42.5187 84.0638 42.4541 84.1428 42.4541 84.2343C42.4541 84.3258 42.5187 84.4048 42.6084 84.4227L49.1874 85.7478L50.5332 92.2203C50.5516 92.3095 50.6303 92.3734 50.7213 92.3734C50.8123 92.3734 50.891 92.3095 50.9095 92.2203L52.2553 85.7478L58.8343 84.4227C58.924 84.4048 58.9886 84.3258 58.9886 84.2343C58.9886 84.1428 58.924 84.0638 58.8343 84.0459L52.2553 82.7208L50.9095 76.2483ZM118.842 107.3C118.823 107.211 118.745 107.147 118.654 107.147C118.563 107.147 118.484 107.211 118.466 107.3L117.12 113.773L110.541 115.097C110.451 115.116 110.386 115.194 110.386 115.286C110.386 115.378 110.451 115.456 110.541 115.474L117.12 116.799L118.466 123.272C118.484 123.361 118.563 123.425 118.654 123.425C118.745 123.425 118.823 123.361 118.842 123.272L120.188 116.799L126.767 115.474C126.856 115.456 126.921 115.378 126.921 115.286C126.921 115.194 126.856 115.116 126.767 115.097L120.188 113.773L118.842 107.3Z"
              fill="#FFCC00"
              stroke="black"
              strokeWidth={1.05469}
              strokeLinejoin="round"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M115.353 62.4923L107.638 61.6651L112.12 46.731L101.287 57.2215L100.463 49.1388L95.4004 56.8139L89.7169 49.0145L90.942 61.0334L80.1494 58.3684L88.6246 65.406L88.6604 66.1868C99.94 66.0658 109.752 64.619 115.353 62.4923Z"
              fill="#FAF3F3"
            />
            <path
              d="M115.353 62.4923L115.421 62.672L115.374 62.3011L115.353 62.4923ZM107.637 61.6651L107.453 61.61L107.387 61.8317L107.617 61.8563L107.637 61.6651ZM112.12 46.731L112.304 46.7863L112.516 46.0796L111.986 46.5928L112.12 46.731ZM101.286 57.2215L101.095 57.241L101.135 57.6355L101.42 57.3597L101.286 57.2215ZM100.463 49.1389L100.654 49.1194L100.599 48.5826L100.302 49.033L100.463 49.1389ZM95.4001 56.8139L95.2445 56.9272L95.4078 57.1513L95.5606 56.9198L95.4001 56.8139ZM89.7166 49.0145L89.872 48.9012L89.4531 48.3265L89.5254 49.034L89.7166 49.0145ZM90.9417 61.0334L90.8955 61.2203L91.1606 61.2857L91.1329 61.0139L90.9417 61.0334ZM80.1492 58.3684L80.1953 58.1818L80.0264 58.5163L80.1492 58.3684ZM88.6243 65.406L88.8163 65.3973L88.8125 65.3124L88.7471 65.2581L88.6243 65.406ZM88.6602 66.1868L88.4682 66.1958L88.4766 66.3811L88.6622 66.3791L88.6602 66.1868ZM115.374 62.3011L107.658 61.4741L107.617 61.8563L115.333 62.6835L115.374 62.3011ZM107.821 61.7204L112.304 46.7863L111.935 46.6759L107.453 61.61L107.821 61.7204ZM111.986 46.5928L101.153 57.0834L101.42 57.3597L112.253 46.8691L111.986 46.5928ZM101.478 57.202L100.654 49.1194L100.271 49.1583L101.095 57.241L101.478 57.202ZM100.302 49.033L95.2394 56.7081L95.5606 56.9198L100.623 49.2447L100.302 49.033ZM95.5555 56.7006L89.872 48.9012L89.5613 49.1276L95.2445 56.9272L95.5555 56.7006ZM89.5254 49.034L90.7505 61.0529L91.1329 61.0139L89.9078 48.995L89.5254 49.034ZM90.9876 60.8468L80.1953 58.1818L80.103 58.5551L90.8955 61.2203L90.9876 60.8468ZM80.0264 58.5163L88.5015 65.5539L88.7471 65.2581L80.272 58.2205L80.0264 58.5163ZM88.4323 65.4149L88.4682 66.1958L88.8522 66.1781L88.8163 65.3973L88.4323 65.4149ZM88.6622 66.3791C99.9508 66.2578 109.789 64.8105 115.421 62.672L115.285 62.3126C109.715 64.4272 99.929 65.8736 88.6581 65.9945L88.6622 66.3791Z"
              fill="black"
            />
          </svg>

          <h2 className="mb-2 font-semibold text-gray-900 text-lg">
            {t("deleteModal.message")}
          </h2>
          {disclaimer && <p className="text-gray-500 text-sm">{disclaimer}</p>}

          <div className="flex items-center space-x-3 mt-6">
            <Button
              variant="outline"
              onClick={onClose}
              className="px-8"
              disabled={isPending}
            >
              {t("deleteDialog.no")}
            </Button>
            <Button
              onClick={() => {
                onConfirm();
                onClose();
              }}
              className="bg-slate-700 hover:bg-slate-800 px-8"
              disabled={isPending}
            >
              {t("deleteDialog.yes")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
