import React from "react";
import { useLoadScript } from "@react-google-maps/api";

const libraries: ("places" | "geometry")[] = ["places", "geometry"];

export function GoogleMapsLoader({ children }: { children: React.ReactNode }) {
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
    libraries,
  });

  if (loadError) {
    console.error("Google Maps loading error:", loadError);
    return <div>Error loading maps: {loadError.message}</div>;
  }

  if (!isLoaded) {
    console.log("Google Maps is still loading...");
    return <div>Loading Maps...</div>;
  }

  console.log("Google Maps loaded successfully");
  return <>{children}</>;
}
