import { fetchAssignmentById, updateAssignment } from "@/data/assignments";
import { fetchAllBranches } from "@/data/branches";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate, use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import { useForm, useFieldArray, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Calendar } from "@/components/ui/calendar";
import {
  CalendarIcon,
  ChevronLeft,
  ChevronUp,
  ChevronDown,
  Edit,
  Plus,
  Trash2,
  Star,
  Loader2,
} from "lucide-react";
import { cn, isUUID } from "@/lib/utils";
import { format } from "date-fns";
import { toast } from "sonner";
import {
  ChecklistAssignmentFrequency,
  UpdateChecklistHierarchyInput,
} from "@/lib/types";
import { supabase } from "@/integrations/supabase/client";
import { useDateFormatter } from "@/hooks/useLocalizationSettings";
import { useFieldDefaults } from "@/hooks/useFieldDefaults";
import { Checkbox } from "@/components/ui/checkbox";
import { DeleteSectionModal } from "@/components/DeleteSectionModal";
import { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";

// Enhanced Zod schemas
const assignmentFieldSchema = z.object({
  id: z.string(),
  question: z.string().min(1, "Question text is required"),
  questionType: z.enum(["Text", "YES/NO"]),
  required: z.boolean(),
  hint: z.string().optional(),
  requiresEvidence: z.boolean(),
  score: z.string().min(1, "Score is required"),
  order: z.number().min(1, "Order must be a positive integer"),
  preferredAnswer: z.string().optional(),
  hasPreferredAnswer: z.boolean().default(false),
  templateFieldId: z.string().optional(),
});

const assignmentSectionSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Section name is required"),
  order: z.number().min(1, "Order must be a positive integer"),
  description: z.string().optional(),
  fields: z
    .array(assignmentFieldSchema)
    .min(1, "At least one field is required"),
});

const editAssignmentSchema = z
  .object({
    // Assignment basic data
    branchId: z.string().min(1, "Branch is required"),
    assignedTo: z
      .string()
      .email("Please enter a valid email")
      .optional()
      .or(z.literal("")),
    startDate: z.date().optional(),
    dueDate: z.date().optional(),
    frequency: z.enum(["daily", "weekly", "monthly", "yearly"]).optional(),
    notes: z.string().optional(),
    passRate: z.string().regex(/^\d+$/, "Pass rate must be a number"),
    // Sections and fields
    sections: z
      .array(assignmentSectionSchema)
      .min(1, "At least one section is required"),
  })
  .refine(
    (data) => {
      if (data.startDate && data.dueDate) {
        return data.dueDate >= data.startDate;
      }
      return true;
    },
    {
      message: "Due date must be after start date",
      path: ["dueDate"],
    }
  );

type EditAssignmentFormData = z.infer<typeof editAssignmentSchema>;

const EditAssignment = () => {
  const { t } = useTranslation();
  const { assignmentId } = useParams<{ assignmentId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { formatDate } = useDateFormatter();
  const { getNewAssignmentFieldDefaults } = useFieldDefaults();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState<{
    type: "section" | "field";
    sectionIndex: number;
    fieldIndex?: number;
  } | null>(null);

  // Fetch assignment data
  const {
    data: assignment,
    isLoading: isAssignmentLoading,
    error: assignmentError,
  } = useQuery({
    queryKey: ["assignment", assignmentId],
    queryFn: () => fetchAssignmentById(assignmentId!),
    enabled: !!assignmentId,
  });

  // Fetch branches data
  const {
    data: branches,
    isLoading: isBranchesLoading,
    error: branchesError,
  } = useQuery({
    queryKey: ["all-branches"],
    queryFn: () => fetchAllBranches(),
  });

  // Transform assignment data to form format
  const getDefaultValues = useCallback((): Partial<EditAssignmentFormData> => {
    if (!assignment) return {};

    return {
      branchId: assignment.branchId || "",
      assignedTo: assignment.assignedTo || "",
      startDate: assignment.startDate
        ? new Date(assignment.startDate)
        : undefined,
      dueDate: assignment.dueDate ? new Date(assignment.dueDate) : undefined,
      frequency: assignment.frequency as EditAssignmentFormData["frequency"],
      notes: assignment.notes || "",
      passRate: "100", // Default pass rate since it's not in assignment data
      sections:
        assignment.sections?.map((section, sectionIndex) => ({
          id: section.id,
          name: section.name || "",
          order: section.order || sectionIndex + 1,
          description: section.description || "",
          fields:
            section.fields?.map((field, fieldIndex) => ({
              id: field.id || (Date.now() + fieldIndex).toString(),
              question: field.question || "",
              questionType: field.questionType || "Text",
              required: field.required || false,
              hint: field.hint || "",
              requiresEvidence: field.requiresEvidence || false,
              score: field.score || "1",
              order: field.order || fieldIndex + 1,
              preferredAnswer: field.preferredAnswer || "",
              hasPreferredAnswer: !!field.preferredAnswer,
              templateFieldId: field.id,
            })) || [],
        })) || [],
    };
  }, [assignment]);

  // React Hook Form setup
  const form = useForm<EditAssignmentFormData>({
    resolver: zodResolver(editAssignmentSchema),
    defaultValues: getDefaultValues(),
  });

  // Reset form when assignment data is loaded
  useEffect(() => {
    if (assignment) {
      const defaultValues = getDefaultValues();
      form.reset(defaultValues);
    }
  }, [assignment, form, getDefaultValues]);

  // Field arrays for sections
  const {
    fields: sections,
    append: appendSection,
    remove: removeSection,
  } = useFieldArray({
    control: form.control,
    name: "sections",
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: async (formData: EditAssignmentFormData) => {
      // Sort sections and fields by their current order in the form to ensure proper sequencing
      const sortedSections = [...formData.sections].sort(
        (a, b) => a.order - b.order
      );

      const updateData: UpdateChecklistHierarchyInput = {
        assignment: {
          branchId: formData.branchId,
          assignedTo: formData.assignedTo || undefined,
          dueDate: formData.dueDate?.toISOString(),
          startDate: formData.startDate?.toISOString(),
          frequency: formData.frequency,
          notes: formData.notes,
          passRate: formData.passRate,
        },
        sections: sortedSections.map((section, sectionIndex) => {
          // Sort fields within each section
          const sortedFields = [...section.fields].sort(
            (a, b) => a.order - b.order
          );

          return {
            id: isUUID(section.id) ? section.id : undefined,
            name: section.name,
            description: section.description,
            order: sectionIndex + 1, // Ensure sequential ordering starting from 1
            fields: sortedFields.map((field, fieldIndex) => ({
              id: isUUID(field.id) ? field.id : undefined,
              question: field.question,
              questionType: field.questionType,
              required: field.required,
              requiresEvidence: field.requiresEvidence,
              score: field.score,
              hint: field.hint,
              order: fieldIndex + 1, // Ensure sequential ordering starting from 1
              preferredAnswer: field.hasPreferredAnswer
                ? field.preferredAnswer
                : undefined,
              templateFieldId: field.templateFieldId,
            })),
          };
        }),
      };

      return await updateAssignment(assignmentId!, updateData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["assignments"] });
      queryClient.invalidateQueries({ queryKey: ["assignment", assignmentId] });
      toast.success("Assignment updated successfully!");
      navigate(`/assignments/${assignmentId}`);
    },
    onError: (error) => {
      toast.error("Failed to update assignment: " + error);
      console.error("Error updating assignment:", error);
    },
  });

  // Section and field management functions
  const handleDeleteSection = (sectionIndex: number) => {
    setDeleteTarget({ type: "section", sectionIndex });
    setShowDeleteModal(true);
  };

  const handleDeleteField = (sectionIndex: number, fieldIndex: number) => {
    setDeleteTarget({ type: "field", sectionIndex, fieldIndex });
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (deleteTarget) {
      if (deleteTarget.type === "section") {
        removeSection(deleteTarget.sectionIndex);
      } else if (
        deleteTarget.type === "field" &&
        deleteTarget.fieldIndex !== undefined
      ) {
        const currentSection = form.getValues(
          `sections.${deleteTarget.sectionIndex}`
        );
        const updatedFields = currentSection.fields.filter(
          (_, index) => index !== deleteTarget.fieldIndex
        );
        form.setValue(
          `sections.${deleteTarget.sectionIndex}.fields`,
          updatedFields
        );
      }
      setShowDeleteModal(false);
      setDeleteTarget(null);
    }
  };

  const addSection = () => {
    const currentSections = form.getValues("sections");
    const newOrder = Math.max(...currentSections.map((s) => s.order), 0) + 1;

    const newSection = {
      id: Date.now().toString(),
      name: `Section ${currentSections.length + 1}`,
      order: newOrder,
      description: "",
      fields: [
        {
          id: (Date.now() + 1).toString(),
          ...getNewAssignmentFieldDefaults(),
        },
      ],
    };

    appendSection(newSection);
  };

  const addField = (sectionIndex: number) => {
    const currentSection = form.getValues(`sections.${sectionIndex}`);
    const newOrder =
      Math.max(...currentSection.fields.map((f) => f.order), 0) + 1;

    const newField = {
      id: Date.now().toString(),
      ...getNewAssignmentFieldDefaults(),
      order: newOrder,
    };

    const updatedFields = [...currentSection.fields, newField];
    form.setValue(`sections.${sectionIndex}.fields`, updatedFields);
  };

  const togglePreferredAnswer = (sectionIndex: number, fieldIndex: number) => {
    const currentValue = form.getValues(
      `sections.${sectionIndex}.fields.${fieldIndex}.hasPreferredAnswer`
    );
    form.setValue(
      `sections.${sectionIndex}.fields.${fieldIndex}.hasPreferredAnswer`,
      !currentValue
    );

    if (currentValue) {
      form.setValue(
        `sections.${sectionIndex}.fields.${fieldIndex}.preferredAnswer`,
        ""
      );
    }
  };

  // Reordering functions - now only update local state
  const handleMoveSectionUp = (sectionIndex: number) => {
    if (sectionIndex === 0) return; // Can't move first section up

    const currentSections = form.getValues("sections");

    // Swap sections in the array
    const newSections = [...currentSections];
    [newSections[sectionIndex], newSections[sectionIndex - 1]] = [
      newSections[sectionIndex - 1],
      newSections[sectionIndex],
    ];

    // Recalculate orders for all sections to ensure proper sequencing
    const reorderedSections = newSections.map((section, index) => ({
      ...section,
      order: index + 1,
    }));

    form.setValue("sections", reorderedSections);
  };

  const handleMoveSectionDown = (sectionIndex: number) => {
    const currentSections = form.getValues("sections");
    if (sectionIndex === currentSections.length - 1) return; // Can't move last section down

    // Swap sections in the array
    const newSections = [...currentSections];
    [newSections[sectionIndex], newSections[sectionIndex + 1]] = [
      newSections[sectionIndex + 1],
      newSections[sectionIndex],
    ];

    // Recalculate orders for all sections to ensure proper sequencing
    const reorderedSections = newSections.map((section, index) => ({
      ...section,
      order: index + 1,
    }));

    form.setValue("sections", reorderedSections);
  };

  const handleMoveFieldUp = (sectionIndex: number, fieldIndex: number) => {
    const currentSection = form.getValues(`sections.${sectionIndex}`);
    const fieldsArray = [...currentSection.fields];

    if (fieldIndex === 0) return; // Can't move if it's already the first field

    // Find the field to move and the field above it
    const fieldToMove = fieldsArray[fieldIndex];

    // Find the field with the order immediately before this one
    const sortedFields = fieldsArray.sort((a, b) => a.order - b.order);
    const sortedIndex = sortedFields.findIndex((f) => f.id === fieldToMove.id);

    if (sortedIndex === 0) return; // Already at the top visually

    const fieldAbove = sortedFields[sortedIndex - 1];

    // Swap their orders
    const tempOrder = fieldToMove.order;
    fieldToMove.order = fieldAbove.order;
    fieldAbove.order = tempOrder;

    form.setValue(`sections.${sectionIndex}.fields`, fieldsArray);
  };

  const handleMoveFieldDown = (sectionIndex: number, fieldIndex: number) => {
    const currentSection = form.getValues(`sections.${sectionIndex}`);
    const fieldsArray = [...currentSection.fields];

    // Find the field to move
    const fieldToMove = fieldsArray[fieldIndex];

    // Find the field with the order immediately after this one
    const sortedFields = fieldsArray.sort((a, b) => a.order - b.order);
    const sortedIndex = sortedFields.findIndex((f) => f.id === fieldToMove.id);

    if (sortedIndex === sortedFields.length - 1) return; // Already at the bottom visually

    const fieldBelow = sortedFields[sortedIndex + 1];

    // Swap their orders
    const tempOrder = fieldToMove.order;
    fieldToMove.order = fieldBelow.order;
    fieldBelow.order = tempOrder;

    form.setValue(`sections.${sectionIndex}.fields`, fieldsArray);
  };
  const onSubmit = (data: EditAssignmentFormData) => {
    updateMutation.mutate(data);
  };

  if (isAssignmentLoading || isBranchesLoading) {
    return (
      <div className="flex justify-center items-center w-full min-h-screen">
        <Loader2 className="w-6 h-6 text-gray-600 animate-spin" />
        <span className="ml-2 text-gray-600">
          {t("editAssignment.loadingAssignment")}
        </span>
      </div>
    );
  }

  if (assignmentError || branchesError) {
    return (
      <div className="flex justify-center items-center w-full min-h-screen">
        <div className="text-red-600">
          {t("editAssignment.error")}:{" "}
          {assignmentError?.message || branchesError?.message}
        </div>
      </div>
    );
  }

  const handleBack = () => {
    navigate(`/assignments/${assignmentId}`);
  };

  return (
    <div className="min-h-screen">
      <div className="p-6 w-full">
        <div className="flex items-center space-x-4 mb-6">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ChevronLeft className="w-4 h-4" />
          </Button>
        </div>

        <h1 className="mb-6 font-bold text-gray-900 text-2xl">
          {t("editAssignment.editAssignment")}
        </h1>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Assignment Basic Data */}
          <Card className="p-6">
            <h2 className="mb-4 font-semibold text-gray-900 text-lg">
              {t("editAssignment.assignmentDetails")}
            </h2>
            <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
              {/* Branch Selection */}
              <div className="space-y-2">
                <Label htmlFor="branch">Target Branch *</Label>
                <Controller
                  name="branchId"
                  control={form.control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select branch" />
                      </SelectTrigger>
                      <SelectContent>
                        {branches?.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id}>
                            {branch.storeName} - {branch.location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {form.formState.errors.branchId && (
                  <p className="text-red-500 text-sm">
                    {form.formState.errors.branchId.message}
                  </p>
                )}
              </div>

              {/* Assigned To Email */}
              <div className="space-y-2">
                <Label htmlFor="assignedTo">Assign to Email</Label>
                <Controller
                  name="assignedTo"
                  control={form.control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="email"
                      placeholder="Enter user email"
                    />
                  )}
                />
                {form.formState.errors.assignedTo && (
                  <p className="text-red-500 text-sm">
                    {form.formState.errors.assignedTo.message}
                  </p>
                )}
              </div>

              {/* Start Date */}
              <div className="space-y-2">
                <Label>Start Date</Label>
                <Controller
                  name="startDate"
                  control={form.control}
                  render={({ field }) => (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "justify-start w-full font-normal text-left",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 w-4 h-4" />
                          {field.value
                            ? formatDate(field.value)
                            : "Select date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="p-0 w-auto" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  )}
                />
              </div>

              {/* Due Date */}
              <div className="space-y-2">
                <Label>Due Date</Label>
                <Controller
                  name="dueDate"
                  control={form.control}
                  render={({ field }) => (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "justify-start w-full font-normal text-left",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 w-4 h-4" />
                          {field.value
                            ? formatDate(field.value)
                            : "Select date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="p-0 w-auto" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                          disabled={(date) =>
                            form.watch("startDate")
                              ? date < form.watch("startDate")!
                              : false
                          }
                        />
                      </PopoverContent>
                    </Popover>
                  )}
                />
                {form.formState.errors.dueDate && (
                  <p className="text-red-500 text-sm">
                    {form.formState.errors.dueDate.message}
                  </p>
                )}
              </div>

              {/* Frequency */}
              <div className="space-y-2">
                <Label>Frequency (Optional)</Label>
                <Controller
                  name="frequency"
                  control={form.control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className="capitalize">
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(ChecklistAssignmentFrequency).map(
                          (frequency) => (
                            <SelectItem
                              key={frequency}
                              value={frequency}
                              className="capitalize"
                            >
                              {frequency}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>

              {/* Pass Rate */}
              <div className="space-y-2">
                <Label>Pass Rate (%)</Label>
                <Controller
                  name="passRate"
                  control={form.control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="number"
                      min={0}
                      max={100}
                      placeholder="100"
                    />
                  )}
                />
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2 mt-4">
              <Label>Notes / Instructions (Optional)</Label>
              <Controller
                name="notes"
                control={form.control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    placeholder="Add any special instructions or notes for this assignment"
                    className="min-h-24"
                  />
                )}
              />
            </div>
          </Card>

          {/* Sections and Fields */}
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="font-semibold text-gray-900 text-lg">
                {t("editAssignment.sectionsFields")}
              </h2>
              <Button type="button" onClick={addSection} variant="outline">
                <Plus className="mr-2 w-4 h-4" />
                Add Section
              </Button>
            </div>

            {sections.map((section, sectionIndex) => (
              <Card key={section.id} className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex-1 mr-4">
                    <Controller
                      name={`sections.${sectionIndex}.name`}
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          className="font-medium text-gray-900 text-lg"
                          placeholder="Section name"
                        />
                      )}
                    />
                    {form.formState.errors.sections?.[sectionIndex]?.name && (
                      <p className="mt-1 text-red-500 text-sm">
                        {
                          form.formState.errors.sections[sectionIndex]?.name
                            ?.message
                        }
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <TooltipProvider>
                      {/* Move Section Up */}
                      <Tooltip>
                        <TooltipTrigger>
                          <Button
                            variant="ghost"
                            size="sm"
                            type="button"
                            onClick={() => handleMoveSectionUp(sectionIndex)}
                            disabled={sectionIndex === 0}
                          >
                            <ChevronUp
                              className={`w-4 h-4 ${
                                sectionIndex === 0
                                  ? "text-gray-300"
                                  : "text-gray-600 hover:text-gray-800"
                              }`}
                            />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("editAssignment.moveSectionUp")}</p>
                        </TooltipContent>
                      </Tooltip>

                      {/* Move Section Down */}
                      <Tooltip>
                        <TooltipTrigger>
                          <Button
                            variant="ghost"
                            size="sm"
                            type="button"
                            onClick={() => handleMoveSectionDown(sectionIndex)}
                            disabled={sectionIndex === sections.length - 1}
                          >
                            <ChevronDown
                              className={`w-4 h-4 ${
                                sectionIndex === sections.length - 1
                                  ? "text-gray-300"
                                  : "text-gray-600 hover:text-gray-800"
                              }`}
                            />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("editAssignment.moveSectionDown")}</p>
                        </TooltipContent>
                      </Tooltip>

                      {/* Delete Section */}
                      <Tooltip>
                        <TooltipTrigger>
                          <Button
                            variant="ghost"
                            size="sm"
                            type="button"
                            onClick={() => handleDeleteSection(sectionIndex)}
                          >
                            <Trash2 className="w-4 h-4 text-red-500" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Delete section</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>

                {/* Section Description */}
                <div className="mb-4">
                  <Controller
                    name={`sections.${sectionIndex}.description`}
                    control={form.control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        placeholder="Section description (optional)"
                        className="min-h-16"
                      />
                    )}
                  />
                </div>

                {/* Fields within this section */}
                <div className="space-y-4">
                  {form
                    .watch(`sections.${sectionIndex}.fields`)
                    ?.sort((a, b) => a.order - b.order)
                    ?.map((field, fieldIndex) => {
                      // Find the original index in the unsorted array for form operations
                      const originalFieldIndex = form
                        .watch(`sections.${sectionIndex}.fields`)
                        .findIndex((f) => f.id === field.id);

                      return (
                        <Card
                          key={field.id}
                          className="p-4 border-l-4 border-l-blue-500"
                        >
                          <div className="flex justify-between items-center mb-4">
                            <h4 className="font-medium text-gray-800">
                              Field {fieldIndex + 1}
                            </h4>
                            <div className="flex items-center space-x-2">
                              {/* Move Field Up */}
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      type="button"
                                      onClick={() =>
                                        handleMoveFieldUp(
                                          sectionIndex,
                                          originalFieldIndex
                                        )
                                      }
                                      disabled={fieldIndex === 0}
                                    >
                                      <ChevronUp className="w-4 h-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{t("editAssignment.moveFieldUp")}</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>

                              {/* Move Field Down */}
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      type="button"
                                      onClick={() =>
                                        handleMoveFieldDown(
                                          sectionIndex,
                                          originalFieldIndex
                                        )
                                      }
                                      disabled={
                                        fieldIndex ===
                                        form.watch(
                                          `sections.${sectionIndex}.fields`
                                        ).length -
                                          1
                                      }
                                    >
                                      <ChevronDown className="w-4 h-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{t("editAssignment.moveFieldDown")}</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>

                              <Button
                                variant="ghost"
                                size="sm"
                                type="button"
                                onClick={() =>
                                  togglePreferredAnswer(
                                    sectionIndex,
                                    originalFieldIndex
                                  )
                                }
                                className={`${
                                  form.watch(
                                    `sections.${sectionIndex}.fields.${fieldIndex}.hasPreferredAnswer`
                                  )
                                    ? "text-amber-600 hover:text-amber-700"
                                    : "text-gray-400 hover:text-gray-600"
                                }`}
                              >
                                <Star
                                  className={`w-4 h-4 ${
                                    form.watch(
                                      `sections.${sectionIndex}.fields.${fieldIndex}.hasPreferredAnswer`
                                    )
                                      ? "fill-amber-400"
                                      : ""
                                  }`}
                                />
                              </Button>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      type="button"
                                      onClick={() =>
                                        handleDeleteField(
                                          sectionIndex,
                                          originalFieldIndex
                                        )
                                      }
                                    >
                                      <Trash2 className="w-4 h-4 text-red-500" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Delete field</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </div>

                          <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
                            {/* Question Text */}
                            <Controller
                              name={`sections.${sectionIndex}.fields.${originalFieldIndex}.question`}
                              control={form.control}
                              render={({ field }) => (
                                <div className="space-y-2">
                                  <Label>Question text</Label>
                                  <Input
                                    {...field}
                                    placeholder="Enter question text"
                                  />
                                </div>
                              )}
                            />

                            {/* Question Type */}
                            <Controller
                              name={`sections.${sectionIndex}.fields.${originalFieldIndex}.questionType`}
                              control={form.control}
                              render={({ field }) => (
                                <div className="space-y-2">
                                  <Label>Question Type</Label>
                                  <Select
                                    value={field.value}
                                    onValueChange={field.onChange}
                                  >
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="Text">Text</SelectItem>
                                      <SelectItem value="YES/NO">
                                        Yes/No
                                      </SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                              )}
                            />

                            {/* Hint */}
                            <Controller
                              name={`sections.${sectionIndex}.fields.${originalFieldIndex}.hint`}
                              control={form.control}
                              render={({ field }) => (
                                <div className="space-y-2">
                                  <Label>Hint (Optional)</Label>
                                  <Input
                                    {...field}
                                    placeholder="Add a hint for this question"
                                  />
                                </div>
                              )}
                            />

                            {/* Score */}
                            <Controller
                              name={`sections.${sectionIndex}.fields.${originalFieldIndex}.score`}
                              control={form.control}
                              render={({ field }) => (
                                <div className="space-y-2">
                                  <Label>Score Weight</Label>
                                  <Input
                                    {...field}
                                    type="number"
                                    min={1}
                                    max={10}
                                    placeholder="1-10"
                                  />
                                </div>
                              )}
                            />

                            {/* Required Checkbox */}
                            <Controller
                              name={`sections.${sectionIndex}.fields.${originalFieldIndex}.required`}
                              control={form.control}
                              render={({ field }) => (
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    id={`required-${sectionIndex}-${originalFieldIndex}`}
                                  />
                                  <Label
                                    htmlFor={`required-${sectionIndex}-${originalFieldIndex}`}
                                  >
                                    Required field
                                  </Label>
                                </div>
                              )}
                            />

                            {/* Requires Evidence Checkbox */}
                            <Controller
                              name={`sections.${sectionIndex}.fields.${originalFieldIndex}.requiresEvidence`}
                              control={form.control}
                              render={({ field }) => (
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    id={`evidence-${sectionIndex}-${originalFieldIndex}`}
                                  />
                                  <Label
                                    htmlFor={`evidence-${sectionIndex}-${originalFieldIndex}`}
                                  >
                                    Requires evidence
                                  </Label>
                                </div>
                              )}
                            />
                          </div>

                          {/* Preferred Answer Section */}
                          {form.watch(
                            `sections.${sectionIndex}.fields.${originalFieldIndex}.hasPreferredAnswer`
                          ) && (
                            <div className="mt-4">
                              <Controller
                                name={`sections.${sectionIndex}.fields.${originalFieldIndex}.preferredAnswer`}
                                control={form.control}
                                render={({ field }) => (
                                  <div className="space-y-2">
                                    <Label className="flex items-center gap-2">
                                      <Star className="fill-amber-400 w-4 h-4 text-amber-600" />
                                      Preferred Answer
                                    </Label>
                                    {form.watch(
                                      `sections.${sectionIndex}.fields.${originalFieldIndex}.questionType`
                                    ) === "YES/NO" ? (
                                      <Select
                                        value={field.value}
                                        onValueChange={field.onChange}
                                      >
                                        <SelectTrigger>
                                          <SelectValue placeholder="Select preferred answer" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="YES">
                                            Yes
                                          </SelectItem>
                                          <SelectItem value="NO">No</SelectItem>
                                        </SelectContent>
                                      </Select>
                                    ) : (
                                      <Textarea
                                        {...field}
                                        placeholder="Enter the preferred answer for this question"
                                      />
                                    )}
                                    <p className="text-gray-500 text-sm">
                                      This answer will be used as a reference
                                      for scoring or evaluation purposes.
                                    </p>
                                  </div>
                                )}
                              />
                            </div>
                          )}
                        </Card>
                      );
                    })}

                  {/* Add Field Button */}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => addField(sectionIndex)}
                    className="w-full"
                  >
                    <Plus className="mr-2 w-4 h-4" />
                    Add Field
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-4 mt-8">
            <Button type="button" variant="outline" onClick={handleBack}>
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-slate-700 hover:bg-slate-800"
              disabled={updateMutation.isPending}
            >
              {updateMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 w-4 h-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Assignment"
              )}
            </Button>
          </div>
        </form>

        {/* Delete Modal */}
        <DeleteSectionModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={confirmDelete}
          labelToDelete={deleteTarget?.type === "section" ? "Section" : "Field"}
        />
      </div>
    </div>
  );
};

export default EditAssignment;
