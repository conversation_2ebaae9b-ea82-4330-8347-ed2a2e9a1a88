import { supabase } from "@/integrations/supabase/client";
import { fetchWithToken } from "@/lib/fetchWithToken";
import {
  BrandingSettings,
  DefaultsSettings,
  LocalizationSettings,
  UserSettings,
} from "@/lib/types";
import { useCurrentUser } from "@/hooks/useCurrentUser";

export async function fetchDefaultBrandingSettings() {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/defaults/branding`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch default branding settings");
  }
  const data = await response.json();
  return data.defaultBrandingSettings;
}

export async function fetchDefaultLocalizationSettings() {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/defaults/localization`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch default localization settings");
  }
  const data = await response.json();
  return data.defaultLocalizationSettings;
}

export async function fetchDefaultDefaultsSettings() {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/defaults/defaults`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch default defaults settings");
  }
  const data = await response.json();
  return data.defaultDefaultsSettings;
}

export async function fetchDefaultUserSettings() {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/defaults/user`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch default user settings");
  }
  const data = await response.json();
  return data.defaultUserSettings;
}

export async function applyDefaultBrandingSettings() {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/branding/default`,
    {
      method: "POST",
    }
  );
  if (!response.ok) {
    throw new Error("Failed to apply default branding settings");
  }
  return response.json();
}

export async function applyDefaultLocalizationSettings() {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/localization/default`,
    {
      method: "POST",
    }
  );
  if (!response.ok) {
    throw new Error("Failed to apply default localization settings");
  }
  return response.json();
}

export async function applyDefaultDefaultsSettings() {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/defaults/default`,
    {
      method: "POST",
    }
  );
  if (!response.ok) {
    throw new Error("Failed to apply default defaults settings");
  }
  return response.json();
}

export async function applyDefaultUserSettings() {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/user/default`,
    {
      method: "POST",
    }
  );
  if (!response.ok) {
    throw new Error("Failed to apply default user settings");
  }
  return response.json();
}

export async function fetchBrandingSettingsByUserId() {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/branding/${userId}`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch branding settings by user ID");
  }
  const data = await response.json();
  return data.brandingSettings;
}

export async function fetchLocalizationSettingsByUserId() {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/localization/${userId}`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch localization settings by user ID");
  }
  const data = await response.json();
  return data.localizationSettings;
}

export async function fetchDefaultsSettingsByUserId() {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/defaults/${userId}`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch defaults settings by user ID");
  }
  const data = await response.json();
  return data.defaultsSettings;
}

export async function fetchUserSettingsByUserId() {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/user/${userId}`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch user settings by user ID");
  }
  const data = await response.json();
  return data.userSettings;
}

export async function createBrandingSettings(settings: BrandingSettings) {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/branding`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userId, ...settings }),
    }
  );
  if (!response.ok) {
    throw new Error("Failed to create branding settings");
  }
  return response.json();
}

export async function updateBrandingSettings(settings: BrandingSettings) {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/branding`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userId, ...settings }),
    }
  );
  if (!response.ok) {
    throw new Error("Failed to update branding settings");
  }
  return response.json();
}

export async function createLocalizationSettings(
  settings: LocalizationSettings
) {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/localization`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userId, ...settings }),
    }
  );
  if (!response.ok) {
    throw new Error("Failed to create localization settings");
  }
  return response.json();
}

export async function updateLocalizationSettings(
  settings: LocalizationSettings
) {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/localization`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userId, ...settings }),
    }
  );
  if (!response.ok) {
    throw new Error("Failed to update localization settings");
  }
  return response.json();
}

export async function createDefaultsSettings(settings: DefaultsSettings) {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/defaults`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userId, ...settings }),
    }
  );
  if (!response.ok) {
    throw new Error("Failed to create defaults settings");
  }
  return response.json();
}

export async function updateDefaultsSettings(settings: DefaultsSettings) {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/defaults`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userId, ...settings }),
    }
  );
  if (!response.ok) {
    throw new Error("Failed to update defaults settings");
  }
  return response.json();
}

export async function createUserSettings(settings: UserSettings) {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/user`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userId, ...settings }),
    }
  );
  if (!response.ok) {
    throw new Error("Failed to create user settings");
  }
  return response.json();
}

export async function updateUserSettings(settings: UserSettings) {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/user`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userId, ...settings }),
    }
  );
  if (!response.ok) {
    throw new Error("Failed to update user settings");
  }
  return response.json();
}

/**
 * Fetch current user's branding settings with fallback to defaults
 * Returns user's custom branding if exists, otherwise returns default branding
 */
export async function fetchCurrentUserBrandingSettings(): Promise<BrandingSettings> {
  try {
    // First try to get user's custom branding settings
    const userSettings = await fetchBrandingSettingsByUserId();
    return userSettings;
  } catch (error) {
    // If user settings don't exist, fall back to defaults
    console.log("User branding settings not found, using defaults");
    const defaultSettings = await fetchDefaultBrandingSettings();
    return defaultSettings;
  }
}

/**
 * Update current user's branding settings
 */
export async function updateCurrentUserBrandingSettings(
  settings: BrandingSettings
) {
  const userId = await supabase.auth
    .getUser()
    .then(({ data }) => data.user?.id);

  if (!userId) {
    throw new Error("User not authenticated");
  }

  // Try to update first (assumes settings exist)
  let response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/settings/branding`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userId, ...settings }),
    }
  );

  // If update fails (settings don't exist), create them
  if (!response.ok) {
    response = await fetchWithToken(
      `${process.env.API_ENDPOINT}/api/settings/branding`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId, ...settings }),
      }
    );
  }

  if (!response.ok) {
    throw new Error("Failed to update branding settings");
  }

  return response.json();
}
