import React, { useRef } from "react";
import { RotateCcw } from "lucide-react";
import { Toggle } from "../ui/toggle";
import { Spinner } from "../ui/spinner";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  fetchDefaultUserSettings,
  fetchUserSettingsByUserId,
  createUserSettings,
  updateUserSettings,
  applyDefaultUserSettings,
} from "@/data/settings";
import { UserSettings } from "@/lib/types";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

export const UsersTab: React.FC = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const debounceRef = useRef<NodeJS.Timeout>();

  // Fetch default user settings
  const { data: defaultSettings, isLoading: defaultsLoading } = useQuery({
    queryKey: ["defaultUserSettings"],
    queryFn: fetchDefaultUserSettings,
  });

  // Fetch user's current user settings
  const { data: userSettings, isLoading: userSettingsLoading } = useQuery({
    queryKey: ["userSettings"],
    queryFn: fetchUserSettingsByUserId,
    retry: false,
  });

  // Get current values (user settings or defaults)
  const currentSettings = userSettings || defaultSettings;
  const isLoading = defaultsLoading || userSettingsLoading;

  // Mutation to create/update user settings
  const createMutation = useMutation({
    mutationFn: createUserSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userSettings"] });
      toast.success(t("settings.users.settingsCreated"));
    },
    onError: (error) => {
      toast.error(t("settings.users.createFailed") + ": " + error);
    },
  });

  const updateMutation = useMutation({
    mutationFn: updateUserSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userSettings"] });
      toast.success(t("settings.users.settingsUpdated"));
    },
    onError: (error) => {
      toast.error(t("settings.users.updateFailed") + ": " + error);
    },
  });

  // Mutation to apply default settings
  const applyDefaultsMutation = useMutation({
    mutationFn: applyDefaultUserSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userSettings"] });
      toast.success(t("settings.users.defaultsApplied"));
    },
    onError: (error) => {
      toast.error(t("settings.users.defaultsFailed") + ": " + error);
    },
  });

  // Handle save settings
  const handleSaveSettings = (settings: UserSettings) => {
    if (userSettings) {
      updateMutation.mutate(settings);
    } else {
      createMutation.mutate(settings);
    }
  };

  // Handle reset to defaults
  const handleResetToDefaults = () => {
    applyDefaultsMutation.mutate();
  };

  // Handle field changes
  const handleFieldChange = (
    field: keyof UserSettings,
    value: boolean | number
  ) => {
    // Don't update if settings aren't loaded yet
    if (!currentSettings) return;

    const newSettings = { ...currentSettings, [field]: value };

    // Debounce the save operation
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    debounceRef.current = setTimeout(() => {
      handleSaveSettings(newSettings);
    }, 1000);
  };

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
            {t("settings.users.title")}
          </h2>
        </div>
        <div className="flex justify-center items-center py-8">
          <div className="border-primary border-b-2 rounded-full w-8 h-8 animate-spin"></div>
          <span className="ml-3 text-gray-600">
            {t("settings.users.loadingSettings")}
          </span>
        </div>
      </div>
    );
  }

  if (!currentSettings) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
            {t("settings.users.title")}
          </h2>
        </div>
        <div className="flex justify-center items-center py-8">
          <span className="text-gray-600">
            {t("settings.users.noSettingsAvailable")}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header with Reset Button */}
      <div className="flex justify-between items-center">
        <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
          {t("settings.users.title")}
        </h2>
        <button
          onClick={handleResetToDefaults}
          disabled={applyDefaultsMutation.isPending}
          className="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 px-4 py-2 border border-gray-300 rounded-lg transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          <span className="font-medium text-sm">
            {applyDefaultsMutation.isPending
              ? t("settings.users.resetting")
              : t("settings.users.resetToDefaults")}
          </span>
        </button>
      </div>

      {/* Settings Grid */}
      <div className="space-y-6">
        {/* Enable 2FA for Admins */}
        <div className="flex sm:flex-row flex-col sm:items-center gap-3 sm:gap-7">
          <label className="w-full sm:w-44 font-medium text-black-text">
            {t("settings.users.enable2FA")}
          </label>
          <div className="flex items-center">
            <Toggle
              checked={currentSettings.enable2FA}
              onCheckedChange={(checked) =>
                handleFieldChange("enable2FA", checked)
              }
            />
          </div>
        </div>

        {/* Session Timeout */}
        <div className="flex sm:flex-row flex-col sm:items-center gap-3 sm:gap-7">
          <label className="w-full sm:w-44 font-medium text-black-text">
            {t("settings.users.sessionTimeout")}
          </label>
          <div className="flex items-center">
            <Spinner
              value={currentSettings.sessionTimeout}
              onChange={(value) => handleFieldChange("sessionTimeout", value)}
              min={1}
              max={24}
              step={1}
            />
          </div>
        </div>

        {/* Audit Trail Visibility */}
        <div className="flex sm:flex-row flex-col sm:items-center gap-3 sm:gap-7">
          <label className="w-full sm:w-44 font-medium text-black-text">
            {t("settings.users.auditTrailVisibility")}
          </label>
          <div className="flex items-center">
            <Toggle
              checked={currentSettings.auditTrailVisibility}
              onCheckedChange={(checked) =>
                handleFieldChange("auditTrailVisibility", checked)
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
};
