import { BrandingTab } from "@/components/settings/BrandingTab";
import { DefaultsTab } from "@/components/settings/DefaultsTab";
import { LocalizationTab } from "@/components/settings/LocalizationTab";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { useTranslation } from "react-i18next";

const tabs = [
  { id: "branding", labelKey: "settings.tabs.branding" },
  { id: "localization", labelKey: "settings.tabs.localization" },
  { id: "defaults", labelKey: "settings.tabs.defaults" },
] as const;

type TabId = (typeof tabs)[number]["id"];

export default function Settings() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<TabId>("branding");

  const renderTabContent = () => {
    switch (activeTab) {
      case "branding":
        return <BrandingTab />;
      case "localization":
        return <LocalizationTab />;
      case "defaults":
        return <DefaultsTab />;
      default:
        return <BrandingTab />;
    }
  };

  return (
    <div className="settings-container">
      <div className="settings-content">
        {/* Header */}
        <div className="mb-8">
          <h1 className="font-satoshi font-bold text-black-text text-2xl">
            {t("settings.title")}
          </h1>
        </div>

        {/* Settings Layout */}
        <div className="flex lg:flex-row flex-col gap-6">
          {/* Sidebar Navigation */}
          <div className="w-full lg:w-56 lg:shrink-0">
            <div className="p-4 settings-card">
              <nav className="flex flex-row lg:flex-col space-x-2 lg:space-x-0 lg:space-y-1 overflow-x-auto lg:overflow-x-visible">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={cn(
                      "lg:w-full text-left whitespace-nowrap tab-button",
                      activeTab === tab.id
                        ? "tab-button-active"
                        : "tab-button-inactive"
                    )}
                  >
                    {t(tab.labelKey)}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="p-4 sm:p-6 lg:p-8 settings-card">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
