/* Phone Input Custom Styles */
.PhoneInput {
  display: flex !important;
  align-items: center !important;
}

.PhoneInputCountry {
  margin-right: 0.5rem !important;
  flex-shrink: 0 !important;
}

.PhoneInputCountrySelect {
  border: none !important;
  background: transparent !important;
  outline: none !important;
  cursor: pointer !important;
}

.PhoneInputCountrySelectArrow {
  display: none !important; /* Hide the arrow */
}

.PhoneInputCountryIcon {
  width: 1.5rem !important;
  height: 1rem !important;
  border-radius: 2px !important;
}

.PhoneInputInput {
  flex: 1 !important;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  font-size: 0.875rem !important;
}

/* Remove default borders and styling from the library */
.PhoneInput input {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

/* Error states */
.error .PhoneInput {
  border-color: #ef4444 !important;
}

.error .PhoneInput:focus-within {
  box-shadow: 0 0 0 2px #ef4444 !important;
}
