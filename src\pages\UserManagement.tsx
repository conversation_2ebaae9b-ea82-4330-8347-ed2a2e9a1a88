import { useEffect, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { fetchAllInvites } from "@/data/users";
import { canUserAccess } from "@/data/permissions";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Search,
  Plus,
  Edit,
  UserX,
  Shield,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Trash2,
  Eye,
} from "lucide-react";
import { DeleteSectionModal } from "@/components/DeleteSectionModal";
import { deleteUserInvite } from "../data/users";
import { toast } from "sonner";

interface User {
  userId: string | null;
  userName: string | null;
  role: string | null;
  email: string | null;
  branch: string | null;
  status: "active" | "inactive";
}

export default function UserManagement() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useSearchParams();
  const currentPage = Number(searchParams.get("page") || "1");
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [userInviteToDelete, setUserInviteToDelete] = useState(null);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["all-users", currentPage, roleFilter, debouncedSearchTerm],
    queryFn: () =>
      fetchAllInvites({
        page: String(currentPage),
        limit: "10",
        role: roleFilter !== "all" ? roleFilter : undefined,
        userName: debouncedSearchTerm || undefined,
      }),
    staleTime: 0, // Don't use stale data
    gcTime: 0, // Don't cache at all
    refetchOnWindowFocus: false,
    placeholderData: undefined, // Don't keep previous data
  });

  // Check if user can access role permission matrix
  const { data: canAccessRolePermissions = false } = useQuery({
    queryKey: ["user-can-access", "role_permission_matrix"],
    queryFn: () => canUserAccess("role_permission_matrix"),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => deleteUserInvite(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["all-users", currentPage, roleFilter, debouncedSearchTerm],
      });
    },
    onError: (error) => {
      console.error("Delete failed:", error);
      toast.error(t("userManagement.deleteFailedAlert") + ": " + error);
    },
  });

  const users = data?.data || [];
  const totalPages = data?.pagination?.totalPages || 1;

  const handlePageChange = (page: number) => {
    // Clear all cached queries before changing page
    queryClient.removeQueries({
      queryKey: ["all-users"],
    });

    setSearchParams((prev) => {
      prev.set("page", String(page));
      return prev;
    });

    // Force refetch after a small delay
    setTimeout(() => {
      refetch();
    }, 100);
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    // Reset to page 1 when search changes
    if (currentPage !== 1) {
      setSearchParams((prev) => {
        prev.set("page", "1");
        return prev;
      });
    }
  };

  const handleRoleFilterChange = (value: string) => {
    setRoleFilter(value);
    // Reset to page 1 when role filter changes
    if (currentPage !== 1) {
      setSearchParams((prev) => {
        prev.set("page", "1");
        return prev;
      });
    }
  };

  return (
    <div className="space-y-6 p-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="font-bold text-primary text-2xl">
          {t("userManagement.title")}
        </h2>
        <div className="flex items-center gap-4">
          {canAccessRolePermissions && (
            <Link to="/roles">
              <Button
                variant="outline"
                className="hover:bg-primary px-6 border-primary rounded-full text-primary hover:text-white"
              >
                <Shield className="mr-2 w-5 h-5" />
                {t("userManagement.rolePermissions")}
              </Button>
            </Link>
          )}
          <Link to="/invite-user">
            <Button className="bg-primary hover:bg-primary/90 px-6 rounded-full text-white">
              <Plus className="mr-2 w-5 h-5" />
              {t("userManagement.inviteUser")}
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4 bg-secondary-bg shadow-sm px-4 py-2 border border-input rounded-full w-[300px]">
          <Search className="w-5 h-5 text-muted-foreground" />
          <Input
            placeholder={t("userManagement.searchByName")}
            value={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="shadow-none p-0 border-0 focus-visible:ring-0"
          />
        </div>

        <div className="flex items-center gap-4">
          <span className="font-medium text-primary">
            {t("userManagement.role")} :
          </span>
          <Select value={roleFilter} onValueChange={handleRoleFilterChange}>
            <SelectTrigger className="bg-secondary-bg shadow-sm border border-input rounded-full w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {t("userManagement.allRoles")}
              </SelectItem>
              {[
                "ADMIN",
                "EMPLOYEE",
                "STORE_MANAGER",
                "AREA_MANAGER",
                "AUDITOR",
                "CHECKER",
                "VIEWER",
              ].map((role) => (
                <SelectItem key={role} value={role} className="capitalize">
                  {t(`userManagement.roleTypes.${role.toLowerCase()}`)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Data States */}
      {isLoading ? (
        <div className="p-6 text-muted-foreground text-center">
          {t("userManagement.loadingUsers")}
        </div>
      ) : error ? (
        <div className="p-6 text-red-500 text-center">
          {t("userManagement.failedToLoadUsers")}
        </div>
      ) : (
        <div className="bg-secondary-bg shadow-sm border border-border rounded-xl overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-border">
                  <th className="p-4 font-medium text-muted-foreground text-left">
                    {t("userManagement.table.userName")}
                  </th>
                  <th className="p-4 font-medium text-muted-foreground text-left">
                    {t("userManagement.table.role")}
                  </th>
                  <th className="p-4 font-medium text-muted-foreground text-left">
                    {t("userManagement.table.email")}
                  </th>
                  <th className="p-4 font-medium text-muted-foreground text-left">
                    {t("userManagement.table.branch")}
                  </th>
                  <th className="p-4 font-medium text-muted-foreground text-center">
                    {t("userManagement.table.status")}
                  </th>
                  <th className="p-4 font-medium text-muted-foreground text-center">
                    {t("userManagement.table.action")}
                  </th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr
                    key={user.userId}
                    className="border-b border-border last:border-b-0"
                  >
                    <td className="p-4 font-medium text-primary">
                      {user.userName}
                    </td>
                    <td className="p-4 text-primary">{user.role}</td>
                    <td className="p-4 text-primary">{user.email}</td>
                    <td className="p-4 text-primary">{user.branch}</td>
                    <td className="p-4">
                      <div className="flex justify-center items-center gap-2">
                        <Switch
                          checked={user.status === "active"}
                          onCheckedChange={() => {}}
                          className="data-[state=checked]:bg-primary"
                          disabled
                        />
                        <span className="text-primary text-sm">
                          {user.status === "active"
                            ? t("userManagement.active")
                            : t("userManagement.inactive")}
                        </span>
                      </div>
                    </td>
                    <td className="p-4">
                      <TooltipProvider>
                        <div className="flex justify-center items-center gap-0">
                          <Link to={`/edit-invite/${user.userId}`}>
                            <Tooltip>
                              <TooltipTrigger>
                                <Button variant="ghost" size="icon">
                                  <Edit className="w-5 h-5 text-primary" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Edit user invite</p>
                              </TooltipContent>
                            </Tooltip>
                          </Link>
                          <Tooltip>
                            <TooltipTrigger>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  setUserInviteToDelete(user);
                                  setDeleteModalOpen(true);
                                }}
                              >
                                <Trash2 className="w-5 h-5 text-primary" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Delete user invite</p>
                            </TooltipContent>
                          </Tooltip>
                          <Link to={`/invite-user/${user.userId}`}>
                            <Tooltip>
                              <TooltipTrigger>
                                <Button variant="ghost" size="icon">
                                  <Eye className="w-5 h-5 text-primary" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>View user details</p>
                              </TooltipContent>
                            </Tooltip>
                          </Link>
                        </div>
                      </TooltipProvider>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex justify-center items-center gap-2 p-6">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="flex items-center gap-1 border-border rounded-lg"
                  >
                    <ChevronLeft className="w-4 h-4" />
                    {t("userManagement.pagination.back")}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Go to previous page</p>
                </TooltipContent>
              </Tooltip>

              {Array.from({ length: totalPages }).map((_, i) => {
                const pageNum = i + 1;
                const isActive = currentPage === pageNum;
                return (
                  <Tooltip key={pageNum}>
                    <TooltipTrigger>
                      <Button
                        size="sm"
                        variant={isActive ? "default" : "outline"}
                        onClick={() => handlePageChange(pageNum)}
                        className={`rounded-lg w-10 h-10 ${
                          isActive ? "bg-[#85A3D0]" : "border-border"
                        }`}
                      >
                        {pageNum}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Go to page {pageNum}</p>
                    </TooltipContent>
                  </Tooltip>
                );
              })}

              <Tooltip>
                <TooltipTrigger>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="flex items-center gap-1 border-border rounded-lg"
                  >
                    {t("userManagement.pagination.next")}
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Go to next page</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      )}
      <DeleteSectionModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setUserInviteToDelete(null);
        }}
        onConfirm={() => {
          if (userInviteToDelete) {
            deleteMutation.mutate(userInviteToDelete.userId);
            setDeleteModalOpen(false);
            setUserInviteToDelete(null);
          }
        }}
        labelToDelete={t("userManagement.deleteModal.userInvite")}
        disclaimer={t("userManagement.deleteModal.disclaimer")}
        isPending={deleteMutation.isPending}
      />
    </div>
  );
}
