import ReportsTable from "@/components/issue-reports/ReportsTable";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FileText, Plus } from "lucide-react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

const Index = () => {
  const { t } = useTranslation();

  return (
    <div className="p-8">
      <div className="w-full">
        {/* Header */}
        <div className="flex flex-row justify-between items-center w-full">
          <div className="flex items-center gap-3 mb-8">
            <div className="flex justify-center items-center bg-primary rounded-lg w-12 h-12">
              <FileText className="w-6 h-6 text-primary-foreground" />
            </div>
            <div>
              <h1 className="font-bold text-foreground text-2xl">
                {t("branchReport.title")}
              </h1>
              <p className="text-muted-foreground">
                {t("branchReport.subtitle")}
              </p>
            </div>
          </div>
          <Link to="/report-submission">
            <Button className="bg-primary hover:bg-primary/90 w-full text-primary-foreground">
              {t("branchReport.createReport")}
            </Button>
          </Link>
        </div>

        {/* Reports Table */}
        <ReportsTable />
      </div>
    </div>
  );
};

export default Index;
