import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter, FilterField, FilterConfigRecord } from "@/lib/types";
import { companyFilterConfigs } from "@/lib/constants";
import { Button } from "./ui/button";
import { X } from "lucide-react";
import { useTranslation } from "react-i18next";

// Filter Component
interface FilterRowProps {
  filter: Filter;
  onUpdate: (filter: Filter) => void;
  onRemove: (id: string) => void;
  filterConfig: FilterConfigRecord;
}

export default function FilterRow({
  filter,
  onUpdate,
  onRemove,
  filterConfig,
}: FilterRowProps) {
  const { t } = useTranslation();
  const config = companyFilterConfigs[filter.field];

  const renderValueInput = () => {
    switch (config.type) {
      case "string":
        return (
          <Input
            placeholder={t("filterRow.enterValue")}
            value={filter.value as string}
            onChange={(e) => onUpdate({ ...filter, value: e.target.value })}
            className="w-48"
          />
        );
      case "number":
        return (
          <Input
            type="number"
            placeholder={t("filterRow.enterValue")}
            value={filter.value}
            onChange={(e) =>
              onUpdate({ ...filter, value: parseInt(e.target.value) || 0 })
            }
            className="w-48"
          />
        );
      case "enum":
        return (
          <Select
            value={filter.value as string}
            onValueChange={(value) => onUpdate({ ...filter, value })}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder={t("filterRow.selectValue")} />
            </SelectTrigger>
            <SelectContent>
              {config.options?.map((option) => (
                <SelectItem key={option} value={option}>
                  {option.charAt(0).toUpperCase() + option.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      case "date":
        return (
          <Input
            type="date"
            value={filter.value as string}
            onChange={(e) => onUpdate({ ...filter, value: e.target.value })}
            className="w-48"
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex items-center space-x-3 p-3 rounded-lg">
      <Select
        value={filter.field}
        onValueChange={(value) =>
          onUpdate({ ...filter, field: value as FilterField, value: "" })
        }
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder={t("filterRow.selectProperty")} />
        </SelectTrigger>
        <SelectContent>
          {Object.values(filterConfig).map((config) => (
            <SelectItem key={config.field} value={config.field}>
              {config.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <span className="text-slate-500 text-sm">{t("filterRow.is")}</span>

      {renderValueInput()}

      <Button
        variant="ghost"
        size="sm"
        onClick={() => onRemove(filter.id)}
        className="text-slate-400 hover:text-red-500"
      >
        <X className="w-4 h-4" />
      </Button>
    </div>
  );
}
