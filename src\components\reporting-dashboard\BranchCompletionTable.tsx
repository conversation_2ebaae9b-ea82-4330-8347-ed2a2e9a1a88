import { fetchBranchesList } from "@/data/analytics";
import { useQuery } from "@tanstack/react-query";
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Search,
  Calendar,
  ArrowUpDown,
  Eye,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
} from "lucide-react";

export function BranchCompletionTable({ storeId }: { storeId: string }) {
  const { data, isLoading, error } = useQuery({
    queryKey: ["branches", storeId],
    queryFn: () => fetchBranchesList(storeId),
    staleTime: 5 * 60 * 1000,
  });

  console.log(data);

  if (isLoading) {
    return <div className="shadow-sm rounded-xl w-full">Loading...</div>;
  }

  if (error) {
    return (
      <div className="shadow-sm rounded-xl w-full">Error: {error.message}</div>
    );
  }

  const tableData = data.map((row) => ({
    branch: row.branch_name,
    completion: row.completion_rate + "%",
    passingRate: row.pass_rate + "%",
    onTime: row.on_time_rate + "%",
    failedChecklists: row.failed_questions_count,
  }));
  return (
    <div className="shadow-sm rounded-xl w-full">
      {/* Header */}
      <div className="p-5">
        <div className="flex lg:flex-row flex-col lg:justify-between lg:items-center gap-4 mb-6">
          <h2 className="font-bold text-dashboard-primary text-xl">
            Branch completion list
          </h2>
          <div className="flex sm:flex-row flex-col items-start sm:items-center gap-4">
            {/* Search */}
            <div className="flex items-center gap-2 shadow-sm px-4 py-2 border border-dashboard-field-stroke rounded-full w-full sm:min-w-[283px]">
              <Search className="w-5 h-5 text-dashboard-gray" />
              <span className="text-dashboard-gray">Search by name</span>
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-[#EBEFF4] border-b">
              <th className="px-5 py-4 font-medium text-dashboard-gray text-left">
                <div className="flex items-center gap-2">Branch name</div>
              </th>
              <th className="px-5 py-4 font-medium text-dashboard-gray text-left">
                <div className="flex items-center gap-2">
                  Completion %
                  <ArrowUpDown className="w-5 h-5" />
                </div>
              </th>
              <th className="px-5 py-4 font-medium text-dashboard-gray text-left">
                <div className="flex items-center gap-2">
                  Passing rate
                  <ArrowUpDown className="w-5 h-5" />
                </div>
              </th>
              <th className="px-5 py-4 font-medium text-dashboard-gray text-left">
                <div className="flex items-center gap-2">
                  On-time %
                  <ArrowUpDown className="w-5 h-5" />
                </div>
              </th>
              <th className="px-5 py-4 font-medium text-dashboard-gray text-left">
                <div className="flex items-center gap-2">
                  Failed checklists
                  <ArrowUpDown className="w-5 h-5" />
                </div>
              </th>
              <th className="px-5 py-4 font-medium text-dashboard-gray text-left">
                Action
              </th>
            </tr>
          </thead>
          <tbody>
            {tableData.map((row, index) => (
              <tr
                key={index}
                className="border-[#EBEFF4] border-b hover:/50"
              >
                <td className="px-5 py-4 font-medium text-dashboard-primary">
                  {row.branch}
                </td>
                <td className="px-5 py-4 font-medium text-dashboard-primary">
                  {row.completion}
                </td>
                <td className="px-5 py-4 font-medium text-dashboard-primary">
                  {row.passingRate}
                </td>
                <td className="px-5 py-4 font-medium text-dashboard-primary">
                  {row.onTime}
                </td>
                <td className="px-5 py-4 font-medium text-dashboard-primary">
                  {row.failedChecklists}
                </td>
                <td className="px-5 py-4">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Eye className="w-5 h-5 text-dashboard-primary hover:text-dashboard-primary/70 cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>View branch details</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
