import { useQuery } from "@tanstack/react-query";
import { fetchCurrentUser, CurrentUserProfile } from "@/data/users";
import { useAuth } from "@/contexts/AuthContext";

export const useCurrentUser = () => {
  const { session, loading: authLoading } = useAuth();

  return useQuery<CurrentUserProfile>({
    queryKey: ["currentUser", session?.user?.id],
    queryFn: fetchCurrentUser,
    enabled: !!session && !authLoading,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
};
