import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { createAssignment } from "@/data/assignments";
import { fetchAllBranches } from "@/data/branches";
import { fetchTemplateById } from "@/data/templates";
import { fetchAllUsers } from "@/data/users";
import { supabase } from "@/integrations/supabase/client";
import {
  Assignment,
  ChecklistAssignmentFrequency,
  CreateAssignmentProps,
} from "@/lib/types";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { z } from "zod";
import { useDateFormatter } from "@/hooks/useLocalizationSettings";

// Zod schema for form validation
const assignmentSchema = z
  .object({
    templateId: z.string().min(1, "Template is required"),
    branchId: z.string().min(1, "Branch is required"),
    assignedTo: z.string().optional(),
    startDate: z.date().optional(),
    dueDate: z.date().optional(),
    frequency: z.enum(["daily", "weekly", "monthly", "yearly"]).optional(),
    notes: z.string().optional(),
    passRate: z.string(),
  })
  .refine(
    (data) => {
      // Ensure due date is after start date if both are provided
      if (data.startDate && data.dueDate) {
        return data.dueDate >= data.startDate;
      }
      return true;
    },
    {
      message: "Due date must be after start date",
      path: ["dueDate"],
    }
  );

type AssignmentFormData = z.infer<typeof assignmentSchema>;

interface AssignChecklistFormProps {
  selectedTemplateId: string;
  onBack: () => void;
  assignment: Assignment;
}

export const EditChecklistForm = ({
  selectedTemplateId,
  onBack,
  assignment,
}: AssignChecklistFormProps) => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const formatDate = useDateFormatter();

  // Data queries
  const {
    data: template,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["template", selectedTemplateId],
    queryFn: () => fetchTemplateById(selectedTemplateId),
    enabled: !!selectedTemplateId,
  });

  const {
    data: branches,
    isLoading: isBranchesLoading,
    error: branchesError,
  } = useQuery({
    queryKey: ["all-branches"],
    queryFn: () => fetchAllBranches(),
  });

  const {
    data: users,
    isLoading: isUsersLoading,
    error: usersError,
  } = useQuery({
    queryKey: ["all-users"],
    queryFn: () => fetchAllUsers(),
  });

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    reset,
  } = useForm<AssignmentFormData>({
    resolver: zodResolver(assignmentSchema),
    defaultValues: {
      templateId: selectedTemplateId,
      branchId: assignment.branchId,
      assignedTo: assignment.assignedTo,
      startDate: new Date(assignment.startDate),
      dueDate: new Date(assignment.dueDate),
      frequency: assignment.frequency as AssignmentFormData["frequency"],
      notes: assignment.notes,
      passRate: template?.passRate?.trim() || "100",
    },
  });

  const watchedValues = watch();

  // Mutation for creating assignment
  const mutation = useMutation({
    mutationFn: async (formData: CreateAssignmentProps) => {
      return await createAssignment(formData);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["assignments"] });
      toast.success("Assignment created successfully!");
      navigate(`/assignments`);
    },
    onError: (error) => {
      toast.error("Failed to create assignment: " + error);
      console.error("Error creating assignment:", error);
    },
  });

  // Form submission handler
  const onSubmit = async (data: AssignmentFormData) => {
    const user = await supabase.auth.getUser();
    const assignedBy = user?.data.user.id;

    const formattedData: CreateAssignmentProps = {
      ...data,
      assignedBy,
      startDate: data.startDate?.toISOString(),
      dueDate: data.dueDate?.toISOString(),
      templateId: selectedTemplateId,
      branchId: data.branchId,
    };

    mutation.mutate(formattedData);
  };

  useEffect(() => {
    if (template && assignment && branches && users) {
      reset({
        templateId: selectedTemplateId,
        branchId: assignment.branchId,
        assignedTo: assignment.assignedTo,
        startDate: new Date(assignment.startDate),
        dueDate: new Date(assignment.dueDate),
        frequency: assignment.frequency as AssignmentFormData["frequency"],
        notes: assignment.notes,
        passRate: template?.passRate?.trim() || "100",
      });
    }
  }, [template, reset, assignment, selectedTemplateId, branches, users]);

  // Loading state
  if (isLoading || isBranchesLoading || isUsersLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="w-6 h-6 text-gray-600 animate-spin" />
        <span className="ml-2 text-gray-600">Loading form...</span>
      </div>
    );
  }

  // Error state
  if (error || branchesError || usersError) {
    return (
      <div className="bg-red-50 mx-auto mt-10 p-6 border border-red-200 rounded-lg max-w-xl text-red-700">
        <h2 className="mb-2 font-semibold text-lg">Failed to load form data</h2>
        {error && <p>❌ Template error: {String(error)}</p>}
        {branchesError && <p>❌ Branches error: {String(branchesError)}</p>}
        {usersError && <p>❌ Users error: {String(usersError)}</p>}
        <Button onClick={() => window.location.reload()} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="mx-auto px-6 py-8 max-w-4xl container">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h1 className="font-semibold text-gray-900 text-2xl">
            Assign checklist form
          </h1>
        </div>
      </div>

      <Card className="p-8">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
            {/* Template Field */}
            <div className="space-y-2">
              <Label htmlFor="template">Template</Label>
              <Input
                id="template"
                value={template?.title || ""}
                readOnly
                className="pointer-events-none"
              />
            </div>

            {/* Branch Selection */}
            <div className="space-y-2">
              <Label htmlFor="branch">Target Branch *</Label>
              <Controller
                name="branchId"
                control={control}
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={isBranchesLoading}
                  >
                    <SelectTrigger
                      className={errors.branchId ? "border-red-500" : ""}
                    >
                      <SelectValue placeholder="Select branch" />
                    </SelectTrigger>
                    <SelectContent>
                      {isBranchesLoading ? (
                        <div className="p-2 text-gray-500 text-sm">
                          Loading branches...
                        </div>
                      ) : branchesError ? (
                        <div className="p-2 text-gray-500 text-sm">
                          Error loading branches: {String(branchesError)}
                        </div>
                      ) : (
                        branches?.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id}>
                            {`${branch.storeName} - ${branch.location}`}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.branchId && (
                <p className="text-red-500 text-sm">
                  {errors.branchId.message}
                </p>
              )}
            </div>

            {/* Start Date */}
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start w-full font-normal text-left",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 w-4 h-4" />
                        {field.value ? formatDate(field.value) : "Select date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="p-0 w-auto" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                        className="p-3 pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                )}
              />
              {errors.startDate && (
                <p className="text-red-500 text-sm">
                  {errors.startDate.message}
                </p>
              )}
            </div>

            {/* Assign To */}
            <div className="space-y-2">
              <Label htmlFor="assignTo">Assign to (Optional)</Label>
              <Controller
                name="assignedTo"
                control={control}
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={isUsersLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select user" />
                    </SelectTrigger>
                    <SelectContent>
                      {isUsersLoading ? (
                        <div className="p-2 text-gray-500 text-sm">
                          Loading users...
                        </div>
                      ) : usersError ? (
                        <div className="p-2 text-gray-500 text-sm">
                          Error loading users: {String(usersError)}
                        </div>
                      ) : (
                        users?.map((user) => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Due Date */}
            <div className="space-y-2">
              <Label htmlFor="dueDate">Due Date</Label>
              <Controller
                name="dueDate"
                control={control}
                render={({ field }) => (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start w-full font-normal text-left",
                          !field.value && "text-muted-foreground",
                          errors.dueDate && "border-red-500"
                        )}
                      >
                        <CalendarIcon className="mr-2 w-4 h-4" />
                        {field.value ? formatDate(field.value) : "Select date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="p-0 w-auto" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                        className="p-3 pointer-events-auto"
                        disabled={(date) =>
                          watchedValues.startDate
                            ? date < watchedValues.startDate
                            : false
                        }
                      />
                    </PopoverContent>
                  </Popover>
                )}
              />
              {errors.dueDate && (
                <p className="text-red-500 text-sm">{errors.dueDate.message}</p>
              )}
            </div>

            {/* Frequency */}
            <div className="space-y-2">
              <Label htmlFor="frequency">Frequency</Label>
              <Controller
                name="frequency"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className="capitalize">
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(ChecklistAssignmentFrequency).map(
                        (frequency) => (
                          <SelectItem
                            key={frequency}
                            value={frequency}
                            className="capitalize"
                          >
                            {frequency}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
            {/* Frequency */}
            <div className="space-y-2">
              <Label htmlFor="passRate">Pass Rate</Label>
              <Controller
                name="passRate"
                control={control}
                render={({ field }) => (
                  <Input
                    value={field.value}
                    onChange={field.onChange}
                    type="number"
                    min={0}
                    max={100}
                    step={0.01}
                  />
                )}
              />
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes / Instructions (Optional)</Label>
            <Controller
              name="notes"
              control={control}
              render={({ field }) => (
                <Textarea
                  {...field}
                  id="notes"
                  placeholder="Focus on freezer units"
                  className="min-h-32"
                />
              )}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-4 mt-8">
            <Button type="button" variant="outline" onClick={onBack}>
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-slate-700 hover:bg-slate-800"
              disabled={isSubmitting || mutation.isPending}
            >
              {isSubmitting || mutation.isPending ? (
                <>
                  <Loader2 className="mr-2 w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save"
              )}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};
