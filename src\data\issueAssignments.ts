import { fetchWithToken } from "@/lib/fetchWithToken";

// Types for Issue Assignment
export interface CreateIssueAssignmentInput {
  issueId: string;
  maintenanceEmployeeEmail: string;
  status?: "pending" | "in_progress" | "resolved" | "closed";
  comments?: string;
}

export interface UpdateIssueAssignmentInput {
  status?: "pending" | "in_progress" | "resolved" | "closed";
  comments?: string;
  maintenanceEmployeeId?: string;
}

export interface IssueAssignmentResponse {
  id: string;
  issueId: string;
  maintenanceEmployeeId: string;
  assignedBy: string;
  status: "pending" | "in_progress" | "resolved" | "closed";
  comments: string | null;
  completedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  maintenanceEmployee: {
    id: string;
    name: string;
    email: string;
  } | null;
  assignedByUser: {
    id: string;
    name: string;
    email: string;
  } | null;
  issue: {
    id: string;
    title: string;
    description: string;
    branchId: string;
    userId: string;
    createdAt: Date;
  } | null;
}

export interface BasicIssueAssignmentResponse {
  id: string;
  issueId: string;
  maintenanceEmployeeId: string;
  assignedBy: string;
  status: "pending" | "in_progress" | "resolved" | "closed";
  comments: string | null;
  completedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  issueTitle: string | null;
  issueDescription: string | null;
  maintenanceEmployeeName: string | null;
  maintenanceEmployeeEmail: string | null;
}

// Create a new Issue Assignment
export async function createIssueAssignment(
  data: CreateIssueAssignmentInput
): Promise<IssueAssignmentResponse> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/issue-assignments`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to create issue assignment");
  }

  const json = await response.json();
  return json.data.assignment;
}

// Get all issue assignments with optional filters and pagination
interface FetchIssueAssignmentsParams {
  status?: "pending" | "in_progress" | "resolved" | "closed";
  assignedBy?: string;
  maintenanceEmployeeId?: string;
  issueId?: string;
  branchId?: string;
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

interface IssueAssignmentsPaginated {
  assignments: Array<BasicIssueAssignmentResponse>;
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export async function fetchIssueAssignments(
  params: FetchIssueAssignmentsParams = {}
): Promise<IssueAssignmentsPaginated> {
  const queryParams = new URLSearchParams();

  if (params.page) queryParams.append("page", params.page.toString());
  if (params.limit) queryParams.append("limit", params.limit.toString());
  if (params.status) queryParams.append("status", params.status);
  if (params.assignedBy) queryParams.append("assignedBy", params.assignedBy);
  if (params.maintenanceEmployeeId)
    queryParams.append("maintenanceEmployeeId", params.maintenanceEmployeeId);
  if (params.issueId) queryParams.append("issueId", params.issueId);
  if (params.branchId) queryParams.append("branchId", params.branchId);
  if (params.search) queryParams.append("search", params.search);
  if (params.sortBy) queryParams.append("sortBy", params.sortBy);
  if (params.sortOrder) queryParams.append("sortOrder", params.sortOrder);

  const response = await fetchWithToken(
    `${
      process.env.API_ENDPOINT
    }/api/issue-assignments?${queryParams.toString()}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to fetch issue assignments");
  }

  const json = await response.json();

  return {
    assignments: json.data.assignments,
    total: json.pagination.total,
    page: json.pagination.page,
    limit: json.pagination.limit,
    totalPages: json.pagination.totalPages,
  };
}

// Get issue assignment by ID
export async function fetchIssueAssignmentById(
  assignmentId: string
): Promise<IssueAssignmentResponse> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/issue-assignments/${assignmentId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to fetch issue assignment");
  }

  const json = await response.json();
  return json.data.assignment;
}

// Update issue assignment status and comments
export async function updateIssueAssignmentStatus(
  assignmentId: string,
  data: {
    status: "pending" | "in_progress" | "resolved" | "closed";
    comments?: string;
  }
): Promise<IssueAssignmentResponse> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/issue-assignments/${assignmentId}/status`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to update assignment status");
  }

  const json = await response.json();
  return json.data.assignment;
}

// Update issue assignment
export async function updateIssueAssignment(
  assignmentId: string,
  data: UpdateIssueAssignmentInput
): Promise<IssueAssignmentResponse> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/issue-assignments/${assignmentId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to update issue assignment");
  }

  const json = await response.json();
  return json.data.assignment;
}

// Delete issue assignment by ID
export async function deleteIssueAssignment(
  assignmentId: string
): Promise<void> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/issue-assignments/${assignmentId}`,
    {
      method: "DELETE",
      headers: {},
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to delete issue assignment");
  }
}
