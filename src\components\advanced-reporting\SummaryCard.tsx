import React from "react";
import { useTranslation } from "react-i18next";

type Props = {
  title: string;
  totalSubmission: number;
  averageCompletionRate: number;
  averageScore: number;
  onTimeRate: number;
  highlighted?: boolean;
};

const SummaryCard = ({
  title,
  totalSubmission,
  averageCompletionRate,
  averageScore,
  onTimeRate,
  highlighted,
}: Props) => {
  const { t } = useTranslation();
  return highlighted ? (
    <div className="bg-chart-blue p-2.5 rounded-[15px]">
      <div className="mb-2.5 px-2 py-1">
        <h3 className="font-bold text-white text-lg">{title}</h3>
      </div>
      <div className="space-y-2.5 p-2.5 rounded-[9px]">
        <div className="flex justify-between">
          <span className="text-gray-text">
            {t("summaryCard.totalSubmissions")}
          </span>
          <span className="text-gray-text">{totalSubmission}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-text">
            {t("summaryCard.avgCompletionRate")}
          </span>
          <span className="text-gray-text">{averageCompletionRate}%</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-text">{t("summaryCard.avgScore")}</span>
          <span className="text-gray-text">{averageScore}%</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-text">{t("summaryCard.onTimeRate")}</span>
          <span className="text-gray-text">{onTimeRate}%</span>
        </div>
      </div>
    </div>
  ) : (
    <div className="p-2.5 rounded-[15px]">
      <div className="mb-2.5 px-2 py-1">
        <h3 className="font-bold text-black-text text-lg">{title}</h3>
      </div>
      <div className="space-y-2.5 bg-main-bg p-2.5 border border-field-stroke border-dashed rounded-[9px]">
        <div className="flex justify-between">
          <span className="text-gray-text">
            {t("summaryCard.totalSubmissions")}
          </span>
          <span className="text-gray-text">{totalSubmission}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-text">
            {t("summaryCard.avgCompletionRate")}
          </span>
          <span className="text-gray-text">{averageCompletionRate}%</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-text">{t("summaryCard.avgScore")}</span>
          <span className="text-gray-text">{averageScore}%</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-text">{t("summaryCard.onTimeRate")}</span>
          <span className="text-gray-text">{onTimeRate}%</span>
        </div>
      </div>
    </div>
  );
};

export default SummaryCard;
