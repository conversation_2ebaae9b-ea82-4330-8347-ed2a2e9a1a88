import { <PERSON><PERSON><PERSON> } from "@/components/advanced-reporting/BarChart";
import { LineChart } from "@/components/advanced-reporting/LineChart";
import { Pie<PERSON><PERSON> } from "@/components/advanced-reporting/PieChart";
import SummaryCard from "@/components/advanced-reporting/SummaryCard";
import { fetchAdvancedAnalytics } from "@/data/analytics";
import { fetchBranchesOfStore } from "@/data/branches";
import { fetchTemplates } from "@/data/templates";
import { useQuery } from "@tanstack/react-query";
import { Loader2, ChevronDown } from "lucide-react";
import { useState, useEffect, useMemo } from "react";
import { useParams } from "react-router-dom";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { CompletionChart } from "@/components/reporting-dashboard/CompletionChart";
import { SubmissionDistributionChart } from "@/components/reporting-dashboard/SubmissionDistributionChart";
import { useTranslation } from "react-i18next";

const AdvancedReporting = () => {
  const { storeId } = useParams<{ storeId: string }>();
  const [templateId, setTemplateId] = useState<string | undefined>();
  const [branchId, setBranchId] = useState<string | undefined>();
  const { t } = useTranslation();

  // Templates
  const {
    data: templatesData,
    isLoading: templatesIsLoading,
    error: templatesError,
  } = useQuery({
    queryKey: ["templates"],
    queryFn: () => fetchTemplates({ page: 1, limit: 100 }),
    staleTime: 5 * 60 * 1000,
  });
  const templates = useMemo(() => templatesData?.data || [], [templatesData]);

  // Branches
  const {
    data: branchesData,
    isLoading: branchesIsLoading,
    error: branchesError,
  } = useQuery({
    queryKey: ["branches", storeId],
    queryFn: () => fetchBranchesOfStore({ storeId, page: 1, limit: 100 }),
    staleTime: 5 * 60 * 1000,
  });
  const branches = useMemo(() => branchesData || [], [branchesData]);

  // Analytics
  const { data, isLoading, isFetching, error } = useQuery({
    queryKey: ["advanced-analytics", branchId, templateId],
    queryFn: () => fetchAdvancedAnalytics(branchId, templateId),
    staleTime: 5 * 60 * 1000,
    enabled: !!templateId && !!branchId,
  });

  // Auto-select default template and branch
  useEffect(() => {
    if (!templateId && templates.length > 0) {
      setTemplateId(templates[0].id);
    }
  }, [templates, templateId]);

  useEffect(() => {
    if (!branchId && branches.length > 0) {
      setBranchId(branches[0].id);
    }
  }, [branches, branchId]);

  if (isLoading || isFetching || templatesIsLoading || branchesIsLoading) {
    return (
      <div className="flex justify-center items-center min-h-40">
        <Loader2 className="w-6 h-6 text-gray-600 animate-spin" />
        <span className="ml-2 text-gray-600">
          {t("advancedReporting.loading")}
        </span>
      </div>
    );
  }

  if (error || branchesError || templatesError) {
    return (
      <div className="flex justify-center items-center min-h-40 text-red-500">
        <p>❌ {t("advancedReporting.failedToLoad")}</p>
      </div>
    );
  }

  console.log("DATA: ", data);
  return (
    <div className="w-full h-screen overflow-hidden font-satoshi">
      <div className="flex h-[calc(100vh-70px)]">
        <div className="flex-1 overflow-auto">
          <div className="p-5 min-w-[1200px]">
            <div className="p-5 rounded-[15px] w-full">
              {/* Header */}
              <div className="flex flex-wrap justify-between items-center gap-4 mb-8">
                <h1 className="font-bold text-[22px] text-black-text">
                  {t("advancedReporting.templateAnalytics")}
                </h1>

                <div className="flex flex-wrap items-center gap-4">
                  {/* Template Select */}
                  <div className="flex items-center gap-2.5">
                    <span className="font-medium text-black-text">
                      {t("advancedReporting.template")}:
                    </span>
                    <Select onValueChange={setTemplateId} value={templateId}>
                      <SelectTrigger className="shadow-sm px-4 py-2.5 border border-field-stroke rounded-full min-w-[145px]">
                        <SelectValue
                          placeholder={t("advancedReporting.selectTemplate")}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {templates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Branch Select */}
                  <div className="flex items-center gap-2.5">
                    <span className="font-medium text-black-text">
                      {t("advancedReporting.branch")}:
                    </span>
                    <Select onValueChange={setBranchId} value={branchId}>
                      <SelectTrigger className="shadow-sm px-4 py-2.5 border border-field-stroke rounded-full min-w-[145px]">
                        <SelectValue
                          placeholder={t("advancedReporting.selectBranch")}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {branches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id}>
                            {branch.location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Summary Cards */}
              {data && data.length > 0 ? (
                <div className="gap-4 grid grid-cols-1 lg:grid-cols-3 mb-6">
                  {data.slice(0, 8).map((item) => (
                    <SummaryCard
                      key={item.assignment_id}
                      title={item.template_name}
                      totalSubmission={item.total_submissions}
                      averageCompletionRate={item.completion_rate}
                      averageScore={item.average_score}
                      onTimeRate={item.on_time_rate}
                    />
                  ))}
                </div>
              ) : templateId && branchId ? (
                <div className="flex justify-center items-center mb-6 p-8">
                  <p className="text-gray-500 text-lg">
                    {t("advancedReporting.noDataFound")}
                  </p>
                </div>
              ) : (
                <p className="mb-6 text-gray-500">
                  {t("advancedReporting.selectBothTemplateAndBranch")}
                </p>
              )}

              {/* Charts */}
              {data && data.length > 0 ? (
                <>
                  <div className="gap-6 grid grid-cols-1 xl:grid-cols-2 mb-6">
                    <div className="flex justify-center">
                      <CompletionChart storeId={storeId} branchId={branchId} />
                    </div>
                    <div className="flex justify-center">
                      <LineChart
                        title={t("advancedReporting.templateSubmissionTrend")}
                        branchId={branchId}
                      />
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <SubmissionDistributionChart
                      storeId={storeId}
                      filter={"branch"}
                    />
                  </div>
                </>
              ) : templateId && branchId ? (
                <div className="flex justify-center items-center p-16 border-2 border-gray-300 border-dashed rounded-lg">
                  <div className="text-center">
                    <div className="flex justify-center items-center bg-gray-100 mx-auto mb-4 rounded-full w-16 h-16">
                      <svg
                        className="w-8 h-8 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                        />
                      </svg>
                    </div>
                    <h3 className="mb-2 font-medium text-gray-900 text-lg">
                      No available data for charts
                    </h3>
                    <p className="text-gray-500">
                      There is no data available for the selected template and
                      branch combination.
                    </p>
                  </div>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedReporting;
