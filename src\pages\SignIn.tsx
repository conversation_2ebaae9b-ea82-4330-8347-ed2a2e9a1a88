import AuthLayout from "@/components/auth/AuthLayout";
import SignInForm from "@/components/auth/SignInForm";
import { useAuth } from "@/contexts/AuthContext";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const SignIn = () => {
  const navigate = useNavigate();
  const { session, loading } = useAuth();

  useEffect(() => {
    if (session && !loading) {
      navigate("/");
    }
  }, [loading, navigate, session]);

  const handleToggleMode = () => {
    navigate("/signup");
  };

  if (loading) {
    return (
      <AuthLayout>
        <div className="flex justify-center items-center p-8">
          <div className="text-center">Loading...</div>
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout>
      <SignInForm onToggleMode={handleToggleMode} />
    </AuthLayout>
  );
};

export default SignIn;
