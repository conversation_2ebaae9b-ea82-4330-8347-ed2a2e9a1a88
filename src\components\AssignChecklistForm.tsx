import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { createAssignment, updateAssignment } from "@/data/assignments";
import { fetchAllBranches } from "@/data/branches";
import { fetchDefaultsSettingsByUserId } from "@/data/settings";
import { fetchTemplateById } from "@/data/templates";
import { useDateFormatter } from "@/hooks/useLocalizationSettings";
import { supabase } from "@/integrations/supabase/client";
import {
  Assignment,
  ChecklistAssignmentFrequency,
  CreateAssignmentProps,
  UpdateChecklistHierarchyInput,
} from "@/lib/types";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { CalendarIcon, Loader2 } from "lucide-react";
import React from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { z } from "zod";

// Zod schema for form validation
const createAssignmentSchema = (t: (key: string) => string) =>
  z
    .object({
      templateId: z.string().min(1, t("assignChecklistForm.templateRequired")),
      branchId: z.string().min(1, t("assignChecklistForm.branchRequired")),
      assignedTo: z.string().email(t("assignChecklistForm.validEmailRequired")),
      startDate: z.date(),
      dueDate: z.date(),
      frequency: z.enum(["daily", "weekly", "monthly", "yearly"]).optional(),
      notes: z.string().optional(),
      passRate: z.string(),
    })
    .refine(
      (data) => {
        // Ensure due date is after start date if both are provided
        if (data.startDate && data.dueDate) {
          return data.dueDate >= data.startDate;
        }
        return true;
      },
      {
        message: t("assignChecklistForm.dueDateAfterStartDate"),
        path: ["dueDate"],
      }
    );

type AssignmentFormData = z.infer<ReturnType<typeof createAssignmentSchema>>;

interface AssignChecklistFormProps {
  selectedTemplateId: string;
  onBack: () => void;
  assignment?: Assignment | null;
  isEdit?: boolean;
  onUpdate?: (updatedData: Assignment) => void; // Added onUpdate prop
}

export const AssignChecklistForm = ({
  selectedTemplateId,
  onBack,
  assignment,
  isEdit = false,
  onUpdate,
}: AssignChecklistFormProps) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { formatDate } = useDateFormatter();

  const assignmentSchema = createAssignmentSchema(t);

  // Data queries
  const {
    data: template,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["template", selectedTemplateId],
    queryFn: () => fetchTemplateById(selectedTemplateId),
    enabled: !!selectedTemplateId,
  });

  const {
    data: branches,
    isLoading: isBranchesLoading,
    error: branchesError,
  } = useQuery({
    queryKey: ["all-branches"],
    queryFn: () => fetchAllBranches(),
  });

  // Fetch user's defaults settings for default frequency
  const { data: defaultsSettings } = useQuery({
    queryKey: ["defaultsSettings"],
    queryFn: fetchDefaultsSettingsByUserId,
    retry: false,
  });

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    reset,
  } = useForm<AssignmentFormData>({
    resolver: zodResolver(assignmentSchema),
    defaultValues: {
      templateId: selectedTemplateId,
      branchId: assignment?.branchId || "",
      assignedTo: assignment?.assignedTo || "",
      startDate: assignment?.startDate
        ? new Date(assignment?.startDate)
        : undefined,
      dueDate: assignment?.dueDate ? new Date(assignment?.dueDate) : undefined,
      frequency: assignment?.frequency
        ? (assignment?.frequency as AssignmentFormData["frequency"])
        : (defaultsSettings?.defaultChecklistFrequency as AssignmentFormData["frequency"]) ||
          undefined,
      notes: assignment?.notes || "",
      passRate: template?.passRate?.trim() || "100",
    },
  });

  // Update form defaults when defaultsSettings loads
  React.useEffect(() => {
    if (defaultsSettings && !assignment) {
      reset({
        templateId: selectedTemplateId,
        branchId: "",
        assignedTo: "",
        startDate: undefined,
        dueDate: undefined,
        frequency:
          defaultsSettings.defaultChecklistFrequency as AssignmentFormData["frequency"],
        notes: "",
        passRate: template?.passRate?.trim() || "100",
      });
    }
  }, [
    defaultsSettings,
    assignment,
    selectedTemplateId,
    template?.passRate,
    reset,
  ]);

  const watchedValues = watch();

  // Mutation for creating assignment
  const createMutation = useMutation({
    mutationFn: async (formData: CreateAssignmentProps) => {
      return await createAssignment(formData);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["assignments"] });
      toast.success(t("assignmentResponse.assignmentCreatedSuccess"));
      navigate(`/assignments`);
    },
    onError: (error) => {
      toast.error(error.message);
      console.error("Error creating assignment:", error);
    },
  });
  const updateMutation = useMutation({
    mutationFn: async (
      formData: CreateAssignmentProps | UpdateChecklistHierarchyInput
    ) => {
      return await updateAssignment(assignment.id, formData);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["assignments"] });
      toast.success(t("assignChecklistForm.assignmentUpdated"));
      navigate(`/assignments`);
    },
    onError: (error) => {
      toast.error(t("assignChecklistForm.failedToUpdateAssignment") + error);
      console.error("Error updating assignment:", error);
    },
  });

  // Form submission handler
  const onSubmit = async (data: AssignmentFormData) => {
    const user = await supabase.auth.getUser();
    const assignedBy = user?.data.user.id;

    const formattedData: CreateAssignmentProps = {
      ...data,
      assignedBy,
      startDate: data.startDate?.toISOString(),
      dueDate: data.dueDate?.toISOString(),
      templateId: selectedTemplateId,
      branchId: data.branchId,
      assignedTo: data.assignedTo || undefined,
    };

    if (isEdit) {
      // Format data for UpdateChecklistHierarchyInput when editing
      const updateData = {
        assignment: {
          templateId: selectedTemplateId,
          branchId: data.branchId,
          assignedTo: data.assignedTo || undefined,
          assignedBy,
          dueDate: data.dueDate?.toISOString(),
          startDate: data.startDate?.toISOString(),
          frequency: data.frequency,
          notes: data.notes,
          passRate: data.passRate,
        },
      };
      updateMutation.mutate(updateData);
      onUpdate?.({
        ...assignment,
        ...formattedData,
      }); // Call onUpdate with updated data
    } else {
      createMutation.mutate(formattedData);
    }
  };

  // Loading state
  if (isLoading || isBranchesLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="w-6 h-6 text-gray-600 animate-spin" />
        <span className="ml-2 text-gray-600">
          {t("assignChecklistForm.loadingForm")}
        </span>
      </div>
    );
  }

  // Error state
  if (error || branchesError) {
    return (
      <div className="bg-red-50 mx-auto mt-10 p-6 border border-red-200 rounded-lg max-w-xl text-red-700">
        <h2 className="mb-2 font-semibold text-lg">
          {t("assignChecklistForm.failedToLoadFormData")}
        </h2>
        {error && (
          <p>
            ❌ {t("assignChecklistForm.templateError")}: {String(error)}
          </p>
        )}
        {branchesError && (
          <p>
            ❌ {t("assignChecklistForm.branchesError")}: {String(branchesError)}
          </p>
        )}
        <Button onClick={() => window.location.reload()} className="mt-4">
          {t("global.retry")}
        </Button>
      </div>
    );
  }

  return (
    <div className="mx-auto px-6 py-8 max-w-4xl container">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h1 className="font-semibold text-gray-900 text-2xl">
            {t("assignChecklistForm.title")}
          </h1>
        </div>
      </div>

      <Card className="p-8">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
            {/* Template Field */}
            <div className="space-y-2">
              <Label htmlFor="template">{t("global.template")}</Label>
              <Input
                id="template"
                value={template?.title || ""}
                readOnly
                className="pointer-events-none"
              />
            </div>

            {/* Branch Selection */}
            <div className="space-y-2">
              <Label htmlFor="branch">
                {t("assignChecklistForm.targetBranch")}
              </Label>
              <Controller
                name="branchId"
                control={control}
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={isBranchesLoading}
                  >
                    <SelectTrigger
                      className={errors.branchId ? "border-red-500" : ""}
                    >
                      <SelectValue
                        placeholder={t("assignChecklistForm.selectBranch")}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {isBranchesLoading ? (
                        <div className="p-2 text-gray-500 text-sm">
                          {t("assignChecklistForm.loadingBranches")}
                        </div>
                      ) : branchesError ? (
                        <div className="p-2 text-gray-500 text-sm">
                          {t("assignChecklistForm.errorLoadingBranches")}:{" "}
                          {String(branchesError)}
                        </div>
                      ) : (
                        branches?.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id}>
                            {`${branch.location}`}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.branchId && (
                <p className="text-red-500 text-sm">
                  {errors.branchId.message}
                </p>
              )}
            </div>

            {/* Start Date */}
            <div className="space-y-2">
              <Label htmlFor="startDate">
                {t("assignChecklistForm.startDate")}
              </Label>
              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start w-full font-normal text-left",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 w-4 h-4" />
                        {field.value
                          ? formatDate(field.value)
                          : t("assignChecklistForm.selectDate")}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="p-0 w-auto" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                        className="p-3 pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                )}
              />
              {errors.startDate && (
                <p className="text-red-500 text-sm">
                  {errors.startDate.message}
                </p>
              )}
            </div>

            {/* Assign To (Email) */}
            <div className="space-y-2">
              <Label htmlFor="assignedTo">
                {t("assignChecklistForm.assignToEmail")}
              </Label>
              <Controller
                name="assignedTo"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="assignedTo"
                    type="email"
                    placeholder={t("assignChecklistForm.enterUserEmail")}
                  />
                )}
              />
              {errors.assignedTo && (
                <p className="text-red-500 text-sm">
                  {errors.assignedTo.message}
                </p>
              )}
            </div>

            {/* Due Date */}
            <div className="space-y-2">
              <Label htmlFor="dueDate">
                {t("assignChecklistForm.dueDate")}
              </Label>
              <Controller
                name="dueDate"
                control={control}
                render={({ field }) => (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start w-full font-normal text-left",
                          !field.value && "text-muted-foreground",
                          errors.dueDate && "border-red-500"
                        )}
                      >
                        <CalendarIcon className="mr-2 w-4 h-4" />
                        {field.value
                          ? formatDate(field.value)
                          : t("assignChecklistForm.selectDate")}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="p-0 w-auto" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                        className="p-3 pointer-events-auto"
                        disabled={(date) =>
                          watchedValues.startDate
                            ? date < watchedValues.startDate
                            : false
                        }
                      />
                    </PopoverContent>
                  </Popover>
                )}
              />
              {errors.dueDate && (
                <p className="text-red-500 text-sm">{errors.dueDate.message}</p>
              )}
            </div>

            {/* Frequency */}
            <div className="space-y-2">
              <Label htmlFor="frequency">
                {t("assignChecklistForm.frequency")}
              </Label>
              <Controller
                name="frequency"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className="capitalize">
                      <SelectValue
                        placeholder={t("assignChecklistForm.selectFrequency")}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(ChecklistAssignmentFrequency).map(
                        (frequency) => (
                          <SelectItem
                            key={frequency}
                            value={frequency}
                            className="capitalize"
                          >
                            {frequency}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
            {/* Pass Rate */}
            <div className="space-y-2">
              <Label htmlFor="passRate">
                {t("assignChecklistForm.passRate")}
              </Label>
              <Controller
                name="passRate"
                control={control}
                render={({ field }) => (
                  <Input
                    value={field.value}
                    onChange={field.onChange}
                    type="number"
                    min={0}
                    max={100}
                    step={0.01}
                  />
                )}
              />
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">
              {t("assignChecklistForm.notesInstructions")}
            </Label>
            <Controller
              name="notes"
              control={control}
              render={({ field }) => (
                <Textarea
                  {...field}
                  id="notes"
                  placeholder={t("assignChecklistForm.notesPlaceholder")}
                  className="min-h-32"
                />
              )}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-4 mt-8">
            <Button type="button" variant="outline" onClick={onBack}>
              {t("global.cancel")}
            </Button>
            <Button
              type="submit"
              className="bg-slate-700 hover:bg-slate-800"
              disabled={isSubmitting || createMutation.isPending}
            >
              {isSubmitting || createMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 w-4 h-4 animate-spin" />
                  {t("global.saving")}
                </>
              ) : (
                t("global.save")
              )}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};
