@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    /* Status colors */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 71% 95%;

    --warning: 47 78% 60%;
    --warning-foreground: 0 0% 100%;
    --warning-light: 45 93% 95%;

    --error: 0 84% 60%;
    --error-foreground: 0 0% 100%;
    --error-light: 0 84% 95%;

    /* Custom dashboard colors */
    --dashboard-bg: 208 28% 96%;
    --dashboard-primary: 208 34% 27%;
    --dashboard-green: 115 91% 34%;
    --dashboard-yellow: 49 76% 58%;
    --dashboard-red: 356 84% 51%;
    --dashboard-gray: 214 8% 58%;
    --dashboard-field-stroke: 220 13% 89%;
    --dashboard-chart-blue: 214 88% 60%;
    --nobul-white: 0 0% 100%;
    --main-bg: 216 15% 97%;
    --black-text: 218 49% 16%;
    --gray-text: 217 7% 55%;
    --primary-dark: 218 32% 28%;
    --field-stroke: 214 11% 88%;
    --chart-blue: 210 49% 73%;
    --chart-blue-dark: 218 32% 28%;
    --browser-dark: 0 0% 24%;
    --browser-bg: 0 0% 13%;

    /* Dynamic branding colors - these will be overridden by ThemeProvider */
    --color-primary-bg: 243 244 246; /* default gray-100 #f3f4f6 */
    --color-secondary-bg: 255 255 255; /* default white #ffffff */

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Custom dashboard colors for dark mode */
    --dashboard-bg: 217.2 32.6% 17.5%;
    --dashboard-primary: 0 0% 100%;
    --dashboard-green: 115 91% 34%;
    --dashboard-yellow: 49 76% 58%;
    --dashboard-red: 356 84% 51%;
    --dashboard-gray: 215 20.2% 65.1%;
    --dashboard-field-stroke: 217.2 32.6% 17.5%;
    --dashboard-chart-blue: 214 88% 60%;
    --nobul-white: 0 0% 100%;
    --main-bg: 216 15% 97%;
    --black-text: 218 49% 16%;
    --gray-text: 217 7% 55%;
    --primary-dark: 218 32% 28%;
    --field-stroke: 214 11% 88%;
    --chart-blue: 210 49% 73%;
    --chart-blue-dark: 218 32% 28%;
    --browser-dark: 0 0% 24%;
    --browser-bg: 0 0% 13%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .settings-container {
    @apply min-h-screen;
  }

  .settings-content {
    @apply max-w-7xl mx-auto p-6;
  }

  .settings-card {
    @apply rounded-xl shadow-sm border border-[hsl(var(--border))];
  }

  .tab-button {
    @apply px-4 py-2 text-sm font-medium rounded-lg transition-colors;
  }

  .tab-button-active {
    @apply bg-[#85A3D0] text-white;
  }

  .tab-button-inactive {
    @apply bg-white text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))];
  }
}

/* Satoshi Black */
@font-face {
  font-family: "Satoshi";
  src: url("/src/assets/fonts/satoshi/Satoshi-Black.woff2") format("woff2");
  font-weight: 900;
  font-style: normal;
}

/* Satoshi Black Italic */
@font-face {
  font-family: "Satoshi";
  src: url("/src/assets/fonts/satoshi/Satoshi-BlackItalic.woff2")
    format("woff2");
  font-weight: 900;
  font-style: italic;
}

/* Satoshi Bold */
@font-face {
  font-family: "Satoshi";
  src: url("/src/assets/fonts/satoshi/Satoshi-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
}

/* Satoshi Bold Italic */
@font-face {
  font-family: "Satoshi";
  src: url("/src/assets/fonts/satoshi/Satoshi-BoldItalic.woff2") format("woff2");
  font-weight: 700;
  font-style: italic;
}

/* Satoshi Italic (Regular weight) */
@font-face {
  font-family: "Satoshi";
  src: url("/src/assets/fonts/satoshi/Satoshi-Italic.woff2") format("woff2");
  font-weight: 400;
  font-style: italic;
}

/* Satoshi Light */
@font-face {
  font-family: "Satoshi";
  src: url("/src/assets/fonts/satoshi/Satoshi-Light.woff2") format("woff2");
  font-weight: 300;
  font-style: normal;
}

/* Satoshi Light Italic */
@font-face {
  font-family: "Satoshi";
  src: url("/src/assets/fonts/satoshi/Satoshi-LightItalic.woff2")
    format("woff2");
  font-weight: 300;
  font-style: italic;
}

/* Satoshi Medium */
@font-face {
  font-family: "Satoshi";
  src: url("/src/assets/fonts/satoshi/Satoshi-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
}

/* Satoshi Medium Italic */
@font-face {
  font-family: "Satoshi";
  src: url("/src/assets/fonts/satoshi/Satoshi-MediumItalic.woff2")
    format("woff2");
  font-weight: 500;
  font-style: italic;
}

/* Satoshi Regular */
@font-face {
  font-family: "Satoshi";
  src: url("/src/assets/fonts/satoshi/Satoshi-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
}

/* Satoshi Variable */
@font-face {
  font-family: "SatoshiVariable";
  src: url("/src/assets/fonts/satoshi/Satoshi-Variable.woff2")
    format("woff2-variations");
  font-weight: 300 900;
  font-style: normal;
  font-display: swap;
}

/* Satoshi Variable Italic */
@font-face {
  font-family: "SatoshiVariable";
  src: url("/src/assets/fonts/satoshi/Satoshi-VariableItalic.woff2")
    format("woff2-variations");
  font-weight: 300 900;
  font-style: italic;
  font-display: swap;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

html[dir="rtl"] {
  direction: rtl;
  font-family: "Cairo", sans-serif; /* Optional Arabic-friendly font */
}

html[dir="ltr"] {
  direction: ltr;
}

table {
  direction: ltr;
}

/* Custom Sonner Toast Styles */
[data-sonner-toaster] [data-type="error"] {
  background-color: #dc2626 !important; /* red-600 */
  border-color: #dc2626 !important;
  color: white !important;
}

[data-sonner-toaster] [data-type="error"] [data-description] {
  color: #fecaca !important; /* red-200 for description */
}

[data-sonner-toaster] [data-type="error"] [data-icon] {
  color: white !important;
}

[data-sonner-toaster] [data-type="error"] [data-close-button] {
  color: white !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

[data-sonner-toaster] [data-type="error"] [data-close-button]:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}
