const CACHE_NAME = 'Y-verify-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json'
];

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/10.12.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.12.1/firebase-messaging-compat.js');

// Initialize Firebase
firebase.initializeApp({
  apiKey: "AIzaSyCRv1TDKlsVMGdm02uVxYcZXaUNlWm8g_8",
  projectId: "yverify-4e874",
  messagingSenderId: "22852766641",
  appId: "1:22852766641:web:511790cdf28e01e3e98b90",
  authDomain: "yverify-4e874.firebaseapp.com",
  storageBucket: "yverify-4e874.firebasestorage.app"
});

const messaging = firebase.messaging();

// Install event
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
      .then(() => self.skipWaiting())
      .catch((error) => console.error('Install failed:', error))
  );
});

// Activate event
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cache => {
            if (cache !== CACHE_NAME) {
              return caches.delete(cache);
            }
          })
        );
      })
      .then(() => self.clients.claim())
  );
});

// Fetch event
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => response || fetch(event.request))
      .catch((error) => {
        console.error('Fetch failed:', error);
        throw error;
      })
  );
});

// Background message handler
messaging.onBackgroundMessage(async (payload) => {
  try {
    const title = payload.notification?.title || 
                  payload.data?.title || 
                  'New Message';
    
    const body = payload.notification?.body || 
                 payload.data?.body || 
                 'You have a new message';
    
    const icon = payload.notification?.icon || 
                 payload.data?.icon || 
                 '/notification-badge.png';

    const notificationOptions = {
      body: body,
      icon: icon,
      badge: '/notification-badge.png',
      data: {
        ...payload.data,
        messageId: payload.messageId,
        from: payload.from,
        clickAction: payload.data?.click_action || '/'
      },
      tag: payload.messageId || `notification-${Date.now()}`,
      requireInteraction: true,
      silent: false,
      timestamp: Date.now(),
      actions: [
        { action: 'open', title: 'Open' },
        { action: 'close', title: 'Close' }
      ],
      renotify: true,
      vibrate: [200, 100, 200]
    };

    await self.registration.showNotification(title, notificationOptions);
    
  } catch (error) {
    console.error('Background notification failed:', error);
  }
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action === 'close') {
    return;
  }
  
  const urlToOpen = event.notification.data?.clickAction || 
                   event.notification.data?.url || 
                   '/';
  
  const fullUrl = new URL(urlToOpen, self.location.origin).href;
  
  event.waitUntil(
    self.clients.matchAll({ 
      type: 'window',
      includeUncontrolled: true 
    }).then((clientList) => {
      // Focus existing tab if available
      for (const client of clientList) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          return client.focus();
        }
      }
      
      // Open new window/tab
      if (self.clients.openWindow) {
        return self.clients.openWindow(fullUrl);
      }
    }).catch((error) => {
      console.error('Error handling notification click:', error);
    })
  );
});
