import React, { useState } from "react";
import { ChevronDown } from "lucide-react";
import { GranularityType } from "@/lib/types";
import { fetchBranchSubmissionTrends } from "@/data/analytics";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { useDateFormatter } from "@/hooks/useLocalizationSettings";
import {
  ResponsiveContainer,
  LineChart as RechartsLine<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
} from "recharts";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

interface LineChartProps {
  title: string;
  templateId?: string;
  branchId: string;
}

export const LineChart: React.FC<LineChartProps> = ({ title, branchId }) => {
  const { t } = useTranslation();
  const [granularity, setGranularity] = useState<GranularityType>("daily");
  const { formatDate } = useDateFormatter();

  const { data, isLoading, error } = useQuery({
    queryKey: ["branch-submission-trends", granularity, branchId],
    queryFn: () =>
      fetchBranchSubmissionTrends({
        branchId,
        granularity,
      }),
    staleTime: 5 * 60 * 1000,
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">Loading...</div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-full">
        Error: {error.message}
      </div>
    );
  }

  return (
    <div className="shadow-lg p-4 rounded-[20px] w-full h-[450px]">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h3 className="font-bold text-black-text text-xl">{title}</h3>

        {/* Granularity selector */}
        <Select
          value={granularity}
          onValueChange={(value) => setGranularity(value as GranularityType)}
        >
          <SelectTrigger className="border border-field-stroke rounded-full w-[130px] text-sm">
            <SelectValue />
            <ChevronDown className="ml-auto w-4 h-4" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="daily">{t("lineChart.daily")}</SelectItem>
            <SelectItem value="weekly">{t("lineChart.weekly")}</SelectItem>
            <SelectItem value="monthly">{t("lineChart.monthly")}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Real Line Chart */}
      <div className="w-full h-[350px]">
        <ResponsiveContainer width="100%" height="100%">
          <RechartsLineChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatDate(value)}
            />
            <YAxis />
            <Tooltip labelFormatter={(value) => formatDate(value)} />
            <Line
              type="monotone"
              dataKey="submission_count"
              stroke="#4F46E5"
              strokeWidth={2}
              dot={{ r: 3 }}
              activeDot={{ r: 6 }}
            />
          </RechartsLineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};
