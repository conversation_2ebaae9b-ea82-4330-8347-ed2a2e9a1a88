import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Trash2, Plus, Image as ImageIcon } from "lucide-react";

interface Question {
  id: string;
  text: string;
  type: "dropdown" | "text" | "yesno";
  options?: string[];
  required: boolean;
  hint: string;
  requiresEvidence: boolean;
  scoreWeight: number;
  hasImage: boolean;
}

interface Section {
  id: string;
  title: string;
  questions: Question[];
}

interface CreateTemplateFormProps {
  onBack: () => void;
}

export const CreateTemplateForm = ({ onBack }: CreateTemplateFormProps) => {
  const [templateName, setTemplateName] = useState("XYZ template");
  const [industryType, setIndustryType] = useState("Hotel");
  const [status, setStatus] = useState("Published");

  const [sections, setSections] = useState<Section[]>([
    {
      id: "1",
      title: "Section 1",
      questions: [
        {
          id: "1",
          text: "Lorem ipsum dummy text",
          type: "dropdown",
          options: ["Lorem ipsum", "Lorem ipsum", "Lorem ipsum", "Lorem ipsum"],
          required: true,
          hint: "Tooltip appears in checklist",
          requiresEvidence: true,
          scoreWeight: 3,
          hasImage: true,
        },
      ],
    },
    {
      id: "2",
      title: "Section 2",
      questions: [
        {
          id: "2",
          text: "Lorem ipsum dummy text",
          type: "text",
          required: true,
          hint: "Tooltip appears in checklist",
          requiresEvidence: false,
          scoreWeight: 3,
          hasImage: false,
        },
      ],
    },
    {
      id: "3",
      title: "Section 3",
      questions: [
        {
          id: "3",
          text: "Lorem ipsum dummy text",
          type: "yesno",
          required: true,
          hint: "Tooltip appears in checklist",
          requiresEvidence: true,
          scoreWeight: 3,
          hasImage: true,
        },
      ],
    },
  ]);

  const addSection = () => {
    const newSection: Section = {
      id: Date.now().toString(),
      title: `Section ${sections.length + 1}`,
      questions: [],
    };
    setSections([...sections, newSection]);
  };

  const deleteSection = (sectionId: string) => {
    setSections(sections.filter((s) => s.id !== sectionId));
  };

  const addOption = (sectionId: string, questionId: string) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            questions: section.questions.map((q) => {
              if (q.id === questionId && q.options) {
                return {
                  ...q,
                  options: [...q.options, "New Option"],
                };
              }
              return q;
            }),
          };
        }
        return section;
      })
    );
  };

  return (
    <div className="mx-auto px-6 py-8 max-w-4xl container">
      <div className="mb-8">
        <h1 className="font-semibold text-gray-900 text-2xl">
          Create Template
        </h1>
      </div>

      <Card className="p-8">
        <div className="space-y-6">
          <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="templateName">Template name</Label>
              <Input
                id="templateName"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="industryType">Industry type</Label>
              <Select value={industryType} onValueChange={setIndustryType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Hotel">Hotel</SelectItem>
                  <SelectItem value="Restaurant">Restaurant</SelectItem>
                  <SelectItem value="Retail">Retail</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <Label>Status :</Label>
            <div className="flex items-center gap-2">
              <span className="text-sm">Draft</span>
              <div className="inline-block relative mr-2 w-10 align-middle transition duration-200 ease-in select-none">
                <input
                  type="checkbox"
                  checked={status === "Published"}
                  onChange={(e) =>
                    setStatus(e.target.checked ? "Published" : "Draft")
                  }
                  className="block absolute border-4 rounded-full w-6 h-6 appearance-none cursor-pointer toggle-checkbox"
                />
                <label className="block bg-gray-300 rounded-full h-6 overflow-hidden cursor-pointer toggle-label"></label>
              </div>
              <span className="text-sm">Published</span>
            </div>
          </div>

          {sections.map((section, sectionIndex) => (
            <Card key={section.id} className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-medium text-lg">{section.title}</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => deleteSection(section.id)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>

              {section.questions.map((question, questionIndex) => (
                <div key={question.id} className="space-y-4 mb-6">
                  <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Question text</Label>
                      <Input value={question.text} readOnly />
                    </div>

                    <div className="space-y-2">
                      <Select value={question.type}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="dropdown">Dropdown</SelectItem>
                          <SelectItem value="text">Text</SelectItem>
                          <SelectItem value="yesno">Yes/No</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {question.type === "dropdown" && question.options && (
                    <div className="space-y-2">
                      {question.options.map((option, optionIndex) => (
                        <div
                          key={optionIndex}
                          className="flex items-center gap-2"
                        >
                          <span className="font-medium text-sm">
                            {optionIndex + 1}.
                          </span>
                          <Input value={option} readOnly className="flex-1" />
                          <Button variant="ghost" size="sm">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="link"
                        size="sm"
                        onClick={() => addOption(section.id, question.id)}
                        className="text-blue-600"
                      >
                        + Add option
                      </Button>
                    </div>
                  )}

                  {question.type === "text" && (
                    <div className="space-y-2">
                      <Label>Question Note</Label>
                      <Textarea placeholder="Enter details..." />
                    </div>
                  )}

                  <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox checked={question.required} />
                      <Label>Required</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox checked={question.requiresEvidence} />
                      <Label>Requires evidence</Label>
                    </div>
                  </div>

                  <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Add hint/Optional</Label>
                      <Input value={question.hint} readOnly />
                    </div>

                    <div className="space-y-2">
                      <Label>Score weight</Label>
                      <Input
                        type="number"
                        value={question.scoreWeight}
                        readOnly
                      />
                    </div>
                  </div>

                  {question.hasImage && (
                    <div className="p-4 border-2 border-gray-300 border-dashed rounded-lg">
                      <img
                        src="/lovable-uploads/bc9c9d9d-3e20-4df2-a71f-a08efc3288f5.png"
                        alt="Uploaded"
                        className="rounded w-32 h-24 object-cover"
                      />
                    </div>
                  )}

                  {!question.hasImage && (
                    <Button variant="outline" className="w-full">
                      <ImageIcon className="mr-2 w-4 h-4" />
                      Attach Image
                    </Button>
                  )}
                </div>
              ))}
            </Card>
          ))}

          <Button
            variant="outline"
            onClick={addSection}
            className="border-dashed w-full"
          >
            <Plus className="mr-2 w-4 h-4" />
            Add section
          </Button>

          <div className="flex justify-end gap-4 mt-8">
            <Button variant="outline" onClick={onBack}>
              Cancel
            </Button>
            <Button className="bg-slate-700 hover:bg-slate-800">
              Save changes
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};
