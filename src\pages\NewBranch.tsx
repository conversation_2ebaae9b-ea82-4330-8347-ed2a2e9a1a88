import { Header } from "@/components/CompanyHeader";
import { NewBranchForm } from "@/components/NewBranchForm";
import { useTranslation } from "react-i18next";

const NewBranch = () => {
  const { t } = useTranslation();

  return (
    <div className="flex min-h-screen">
      <div className="flex-1">
        <div className="min-h-screen">
          <main className="p-6">
            <div className="mb-8">
              <h1 className="mb-2 font-semibold text-slate-900 text-2xl">
                {t("branch.newBranchTitle")}
              </h1>
            </div>

            <NewBranchForm />
          </main>
        </div>
      </div>
    </div>
  );
};

export default NewBranch;
