import { supabase } from './src/integrations/supabase/client.js';

// Test function to create an ADMIN user
async function testAdminSignup() {
  try {
    console.log('🚀 Starting ADMIN user signup test...');
    
    const adminUserData = {
      email: '<EMAIL>',
      password: 'testpassword123',
      name: 'Test Admin User',
      phone: '+**********',
      role: 'ADMIN'
    };

    console.log('📝 Signing up user with data:', {
      ...adminUserData,
      password: '***hidden***'
    });

    // Use the same signup method as the AuthProvider
    const { data, error } = await supabase.auth.signUp({
      email: adminUserData.email,
      password: adminUserData.password,
      options: {
        emailRedirectTo: `${window.location?.origin || 'http://localhost:3000'}/`,
        data: {
          name: adminUserData.name,
          phone: adminUserData.phone,
          role: adminUserData.role, // This will set the ADMIN role
        },
      },
    });

    if (error) {
      console.error('❌ Signup failed:', error.message);
      return { success: false, error: error.message };
    }

    console.log('✅ Signup successful!');
    console.log('📧 User created:', {
      id: data.user?.id,
      email: data.user?.email,
      role: data.user?.user_metadata?.role,
      name: data.user?.user_metadata?.name,
      phone: data.user?.user_metadata?.phone,
      email_confirmed: data.user?.email_confirmed_at ? 'Yes' : 'No'
    });

    if (!data.user?.email_confirmed_at) {
      console.log('📬 Please check the email inbox for verification link');
    }

    return { 
      success: true, 
      user: data.user,
      session: data.session 
    };

  } catch (error) {
    console.error('💥 Unexpected error:', error);
    return { success: false, error: error.message };
  }
}

// Alternative function using direct Supabase admin API (if you have admin privileges)
async function createAdminUserWithAdminAPI() {
  try {
    console.log('🔐 Creating ADMIN user via Admin API...');
    
    // Note: This requires admin privileges and service role key
    const { data, error } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'testpassword123',
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        name: 'Test Admin User',
        phone: '+**********',
        role: 'ADMIN'
      }
    });

    if (error) {
      console.error('❌ Admin user creation failed:', error.message);
      return { success: false, error: error.message };
    }

    console.log('✅ Admin user created successfully!');
    console.log('👤 User details:', {
      id: data.user?.id,
      email: data.user?.email,
      role: data.user?.user_metadata?.role,
      email_confirmed: 'Yes (auto-confirmed)'
    });

    return { success: true, user: data.user };

  } catch (error) {
    console.error('💥 Admin API error:', error);
    return { success: false, error: error.message };
  }
}

// Function to check if user exists and their role
async function checkUserRole(email) {
  try {
    console.log(`🔍 Checking role for user: ${email}`);
    
    // Sign in to get user data
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email,
      password: 'testpassword123' // Use the test password
    });

    if (error) {
      console.error('❌ Failed to sign in:', error.message);
      return { success: false, error: error.message };
    }

    console.log('✅ User found!');
    console.log('👤 User details:', {
      id: data.user?.id,
      email: data.user?.email,
      role: data.user?.user_metadata?.role,
      name: data.user?.user_metadata?.name,
      phone: data.user?.user_metadata?.phone,
      email_confirmed: data.user?.email_confirmed_at ? 'Yes' : 'No',
      last_sign_in: data.user?.last_sign_in_at
    });

    // Sign out after checking
    await supabase.auth.signOut();
    console.log('🚪 Signed out');

    return { success: true, user: data.user };

  } catch (error) {
    console.error('💥 Error checking user:', error);
    return { success: false, error: error.message };
  }
}

// Main execution
async function main() {
  console.log('🎯 ADMIN User Creation Test Script');
  console.log('=====================================\n');

  // First, try the regular signup method
  console.log('Method 1: Regular signup with ADMIN role');
  const result1 = await testAdminSignup();
  
  if (result1.success) {
    console.log('\n✅ Regular signup method worked!');
  } else {
    console.log('\n❌ Regular signup failed, trying admin API...');
    
    // If regular signup fails, try admin API
    console.log('\nMethod 2: Admin API user creation');
    const result2 = await createAdminUserWithAdminAPI();
    
    if (!result2.success) {
      console.log('\n❌ Both methods failed. Check your Supabase configuration.');
      return;
    }
  }

  // Wait a moment then check the user
  console.log('\n⏳ Waiting 2 seconds before checking user...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('\n🔍 Verifying created user:');
  await checkUserRole('<EMAIL>');
  
  console.log('\n🎉 Test completed!');
}

// Export functions for use in other scripts
export { testAdminSignup, createAdminUserWithAdminAPI, checkUserRole };

// Run the test if this script is executed directly
if (typeof window !== 'undefined') {
  // Browser environment
  window.testAdminSignup = testAdminSignup;
  window.createAdminUserWithAdminAPI = createAdminUserWithAdminAPI;
  window.checkUserRole = checkUserRole;
  console.log('🌐 Functions available in browser console: testAdminSignup(), createAdminUserWithAdminAPI(), checkUserRole()');
} else {
  // Node.js environment
  main().catch(console.error);
}
