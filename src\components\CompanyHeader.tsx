import { Bell, ChevronDown } from "lucide-react";
import { useTranslation } from "react-i18next";

interface HeaderProps {
  title: string;
  breadcrumbs?: string[];
  children?: React.ReactNode;
}

export function Header({ title, breadcrumbs = [], children }: HeaderProps) {
  const { t } = useTranslation();

  return (
    <header className="flex justify-between items-center px-6 border-slate-200 border-b h-16">
      <div className="flex items-center space-x-2">
        {breadcrumbs.map((crumb, index) => (
          <div key={index} className="flex items-center space-x-2">
            <span className="text-slate-500 text-sm">{crumb}</span>
            <span className="text-slate-300">›</span>
          </div>
        ))}
        <span className="font-medium text-slate-900">{title}</span>
      </div>

      <div className="flex items-center space-x-4">
        {children}

        <button className="hover:bg-slate-100 p-2 rounded-lg text-slate-500 hover:text-slate-700 transition-colors">
          <Bell className="w-5 h-5" />
        </button>

        {/* Country selector */}
        <div className="flex items-center space-x-2">
          <img
            src="https://flagcdn.com/24x18/us.png"
            alt={t("header.usFlag")}
            className="rounded-sm w-6 h-4"
          />
          <ChevronDown className="w-4 h-4 text-slate-500" />
        </div>

        {/* User profile */}
        <div className="bg-slate-300 rounded-full w-8 h-8"></div>
      </div>
    </header>
  );
}
