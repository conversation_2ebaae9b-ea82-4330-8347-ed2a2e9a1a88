import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PhoneInput } from "@/components/ui/phone-input";
import "@/components/ui/phone-input.css";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/contexts/AuthContext";
import { sendVerificationEmail } from "@/data/verification";
import { roleLabels, roles } from "@/lib/constants";
import { createSignUpSchema, type SignUpFormData } from "@/lib/validations";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

interface SignUpFormProps {
  onToggleMode: () => void;
}

const SignUpForm = ({ onToggleMode }: SignUpFormProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { signUp, signInWithGoogle } = useAuth();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm<SignUpFormData>({
    resolver: zodResolver(createSignUpSchema(t)),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (formData: SignUpFormData) => {
    const result = await signUp(
      formData.email,
      formData.password,
      formData.name,
      formData.phone,
      formData.role
    );

    // If signup was successful (no error), send verification email and redirect
    if (!result?.error) {
      try {
        // Send verification email
        await sendVerificationEmail({ email: formData.email });
      } catch (error) {
        console.error("Failed to send verification email:", error);
        toast.error(t(error.message));
        // Continue to redirect even if email sending fails
      }
      navigate("/email-verification-pending", { replace: true });
    }
  };
  const handleGoogleSignIn = async () => {
    await signInWithGoogle();
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="font-semibold text-gray-900 text-2xl">
          {t("signup.title")}
        </h2>
        <p className="mt-2 text-gray-500 text-sm">{t("signup.subtitle")}</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <Label htmlFor="name" className="font-medium text-gray-700 text-sm">
            {t("signup.fullName")}
          </Label>
          <div className="mt-1">
            <Input
              {...register("name")}
              placeholder={t("signup.namePlaceholder")}
              className="shadow-sm px-3 py-2 border border-gray-300 focus:border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 w-full placeholder-gray-400"
            />
            {errors.name && (
              <p className="mt-1 text-red-600 text-sm">{errors.name.message}</p>
            )}
          </div>
        </div>

        <div>
          <Label htmlFor="email" className="font-medium text-gray-700 text-sm">
            {t("signup.email")}
          </Label>
          <div className="mt-1">
            <Input
              {...register("email")}
              type="email"
              placeholder={t("signup.emailPlaceholder")}
              className="shadow-sm px-3 py-2 border border-gray-300 focus:border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 w-full placeholder-gray-400"
            />
            {errors.email && (
              <p className="mt-1 text-red-600 text-sm">
                {errors.email.message}
              </p>
            )}
          </div>
        </div>

        <div>
          <Label htmlFor="phone" className="font-medium text-gray-700 text-sm">
            {t("signup.phone")}
          </Label>
          <div className="mt-1">
            <Controller
              name="phone"
              control={control}
              render={({ field: { onChange, value, ...field } }) => (
                <PhoneInput
                  {...field}
                  value={value}
                  onChange={onChange}
                  placeholder={t("signup.phonePlaceholder")}
                  error={!!errors.phone}
                  className={errors.phone ? "error" : ""}
                />
              )}
            />
            {errors.phone && (
              <p className="mt-1 text-red-600 text-sm">
                {errors.phone.message}
              </p>
            )}
          </div>
        </div>

        <div>
          <Label className="font-medium text-gray-700 text-sm">
            {t("signup.role")}
          </Label>
          <div className="mt-1">
            <Select
              onValueChange={(value) =>
                setValue("role", value as SignUpFormData["role"])
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("signup.selectRole")} />
              </SelectTrigger>
              <SelectContent>
                {roles
                  .filter((role) => role !== "ADMIN") // Exclude ADMIN from signup
                  .map((role) => (
                    <SelectItem key={role} value={role}>
                      {roleLabels[role]}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            {errors.role && (
              <p className="mt-1 text-red-600 text-sm">{errors.role.message}</p>
            )}
          </div>
        </div>

        <div>
          <Label
            htmlFor="password"
            className="font-medium text-gray-700 text-sm"
          >
            {t("signup.password")}
          </Label>
          <div className="relative mt-1">
            <Input
              {...register("password")}
              type={showPassword ? "text" : "password"}
              placeholder={t("signup.passwordPlaceholder")}
              className="shadow-sm px-3 py-2 pr-10 border border-gray-300 focus:border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 w-full placeholder-gray-400"
            />
            <button
              type="button"
              className="right-0 absolute inset-y-0 flex items-center pr-3"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="w-4 h-4 text-gray-400" />
              ) : (
                <Eye className="w-4 h-4 text-gray-400" />
              )}
            </button>
            {errors.password && (
              <p className="mt-1 text-red-600 text-sm">
                {errors.password.message}
              </p>
            )}
          </div>
        </div>

        <div>
          <Label
            htmlFor="confirmPassword"
            className="font-medium text-gray-700 text-sm"
          >
            {t("signup.confirmPassword")}
          </Label>
          <div className="relative mt-1">
            <Input
              {...register("confirmPassword")}
              type={showConfirmPassword ? "text" : "password"}
              placeholder={t("signup.confirmPasswordPlaceholder")}
              className="shadow-sm px-3 py-2 pr-10 border border-gray-300 focus:border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 w-full placeholder-gray-400"
            />
            <button
              type="button"
              className="right-0 absolute inset-y-0 flex items-center pr-3"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? (
                <EyeOff className="w-4 h-4 text-gray-400" />
              ) : (
                <Eye className="w-4 h-4 text-gray-400" />
              )}
            </button>
            {errors.confirmPassword && (
              <p className="mt-1 text-red-600 text-sm">
                {errors.confirmPassword.message}
              </p>
            )}
          </div>
        </div>

        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-gray-800 hover:bg-gray-900 px-4 py-2 rounded-md w-full text-white transition-colors duration-200"
        >
          {isSubmitting
            ? t("signup.creatingAccount")
            : t("signup.createAccount")}
        </Button>

        <div className="text-center">
          <span className="text-gray-500 text-sm">
            {t("signup.alreadyHaveAccount")}
          </span>
          <button
            type="button"
            onClick={onToggleMode}
            className="font-medium text-gray-900 hover:text-gray-700 text-sm underline"
          >
            {t("signup.signIn")}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SignUpForm;
