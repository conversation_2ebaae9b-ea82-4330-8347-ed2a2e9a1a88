"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { fetchBranchesOfStore } from "@/data/branches";
import { fetchBranchPassRateChart } from "@/data/analytics";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Branch {
  id?: string;
  location: string;
}

type GranularityType = "daily" | "weekly" | "monthly";

export function CompletionChart({
  storeId,
  branchId,
}: {
  storeId: string;
  branchId?: string;
}) {
  const [selectedBranch, setSelectedBranch] = useState<string | undefined>(
    branchId
  );
  const [granularity, setGranularity] = useState<GranularityType>("monthly");

  const {
    data: branches,
    isLoading: isBranchesLoading,
    error: branchError,
  } = useQuery({
    queryKey: ["store-branches", storeId],
    queryFn: () => fetchBranchesOfStore({ storeId, page: 1, limit: 100 }),
    staleTime: 5 * 60 * 1000,
  });

  const {
    data: chartData,
    isLoading: isChartLoading,
    error: chartError,
  } = useQuery({
    queryKey: ["completion-chart", selectedBranch, granularity],
    queryFn: () =>
      selectedBranch
        ? fetchBranchPassRateChart({
            storeId,
            branchId: selectedBranch,
            granularity,
          })
        : Promise.resolve([]),
    enabled: !!selectedBranch,
  });

  if (isBranchesLoading || isChartLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        Loading...
      </div>
    );
  }

  if (branchError || chartError) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        Error: {(branchError || chartError)?.message}
      </div>
    );
  }

  return (
    <div className="shadow-sm p-5 rounded-xl w-full">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <h2 className="font-bold text-dashboard-primary text-xl">
          Completion & pass rate by branch
        </h2>
        <div className="flex items-center space-x-4">
          {/* Branch Selector */}
          <Select
            value={selectedBranch ?? ""}
            onValueChange={(value) => setSelectedBranch(value)}
            disabled={!!branchId}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select a branch" />
            </SelectTrigger>
            <SelectContent>
              {branches?.map((branch: Branch) => (
                <SelectItem key={branch.id} value={branch.id}>
                  {branch.location}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Granularity Selector */}
          <Select
            value={granularity}
            onValueChange={(value: GranularityType) => setGranularity(value)}
          >
            <SelectTrigger className="w-[130px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Chart */}
      <div className="w-full h-[320px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 10, bottom: 0 }}
          >
            <CartesianGrid stroke="#e0e0e0" strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis
              domain={[0, 100]}
              tickFormatter={(val) => `${val}%`}
              ticks={[0, 20, 40, 60, 80, 100]}
            />
            <Tooltip formatter={(value: number) => `${value}%`} />
            <Line
              type="monotone"
              dataKey="value"
              stroke="#2D3D55"
              strokeWidth={2}
              dot={{ r: 4, stroke: "white", strokeWidth: 2 }}
              activeDot={{ r: 6 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
