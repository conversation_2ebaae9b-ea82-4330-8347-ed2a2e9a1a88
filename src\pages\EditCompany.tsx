import { Header } from "@/components/CompanyHeader";
import { Sidebar } from "@/components/Sidebar";
import { NewCompanyForm } from "@/components/NewCompanyForm";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "react-router-dom";
import { fetchStore } from "@/data/stores";
import { useTranslation } from "react-i18next";

const EditCompany = () => {
  const { t } = useTranslation();
  const { storeId } = useParams<{ storeId: string }>();
  console.log(storeId);
  const { data, isLoading, error } = useQuery({
    queryKey: ["company", storeId],
    queryFn: () => fetchStore(storeId),
    staleTime: 5 * 60 * 1000,
    enabled: !!storeId,
  });
  if (isLoading) {
    return (
      <div className="flex justify-center items-center w-full min-h-screen">
        {t("global.loading")}
      </div>
    );
  }
  if (error) {
    return (
      <div className="flex justify-center items-center w-full min-h-screen">
        {t("global.error")}: {error?.message || t("branch.noDataFound")}
      </div>
    );
  }

  const store = data?.store;
  return (
    <div className="flex min-h-screen">
      <div className="flex-1">
        <div className="min-h-screen">
          <main className="p-6">
            <div className="mb-8">
              <h1 className="mb-2 font-semibold text-slate-900 text-2xl">
                {t("company.editCompanyTitle")}
              </h1>
            </div>

            <NewCompanyForm store={store} />
          </main>
        </div>
      </div>
    </div>
  );
};

export default EditCompany;
