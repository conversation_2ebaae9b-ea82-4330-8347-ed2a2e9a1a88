import { useState } from "react";
import { Calendar } from "@/components/calendar/Calendar";
import { EventModal } from "@/components/calendar/EventModal";
import { useQuery } from "@tanstack/react-query";
import { CalendarReturn, fetchCalendarAssignments } from "@/data/assignments";

export default function AssignmentCalendar() {
  const [selectedEvent, setSelectedEvent] = useState<CalendarReturn | null>(
    null
  );
  const [viewMode, setViewMode] = useState<"weekly" | "monthly">("monthly");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  const { data, isLoading, error } = useQuery({
    queryKey: ["calendar", selectedDate.getFullYear(), selectedDate.getMonth()],
    queryFn: () => fetchCalendarAssignments(selectedDate),
  });

  if (isLoading) return <div>Loading...</div>;

  if (error) return <div>Error: {error.message}</div>;

  const events = data || [];
  console.log(events);

  return (
    <div className="bg-primary-bg min-h-screen">
      <main className="p-2 sm:p-5 w-full">
        <Calendar
          events={events}
          onEventClick={setSelectedEvent}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          selectedDate={selectedDate}
          onDateChange={setSelectedDate}
        />
      </main>
      {selectedEvent && (
        <EventModal
          event={selectedEvent}
          onClose={() => setSelectedEvent(null)}
        />
      )}
    </div>
  );
}
