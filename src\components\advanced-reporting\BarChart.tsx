import React from "react";

interface BarChartProps {
  title: string;
}

export const BarChart: React.FC<BarChartProps> = ({ title }) => {
  return (
    <div className="shadow-lg p-4 rounded-[20px] w-full h-[450px]">
      <h3 className="mb-8 font-bold text-black-text text-xl">{title}</h3>

      {/* Chart Area */}
      <div className="relative w-full h-[354px]">
        {/* Y-axis labels */}
        <div className="top-0 left-0 absolute flex flex-col justify-between h-full text-gray-text text-sm">
          <span>100%</span>
          <span>80%</span>
          <span>60%</span>
          <span>40%</span>
          <span>20%</span>
          <span>0%</span>
        </div>

        {/* Grid lines */}
        <div className="top-0 right-0 left-12 absolute h-full">
          {[0, 1, 2, 3, 4, 5].map((i) => (
            <div
              key={i}
              className="absolute bg-gray-200 w-full h-px"
              style={{ top: `${i * 20}%` }}
            />
          ))}
        </div>

        {/* Bars */}
        <div className="right-0 bottom-16 left-12 absolute flex justify-around items-end h-[280px]">
          {/* Bar 1 */}
          <div className="flex flex-col items-center">
            <div className="bg-chart-blue border border-blue-300 rounded-lg w-3 h-[59px]"></div>
          </div>

          {/* Bar 2 - highlighted */}
          <div className="relative flex flex-col items-center">
            <div className="bg-primary-dark rounded-lg w-3 h-[77px]"></div>
            {/* Tooltip */}
            <div className="-top-20 absolute bg-primary-dark px-4 py-2 rounded-lg font-bold text-white text-sm text-center">
              Riyadh - B1
              <br />
              30%
            </div>
          </div>

          {/* Bar 3 */}
          <div className="flex flex-col items-center">
            <div className="bg-chart-blue border border-blue-300 rounded-lg w-3 h-[117px]"></div>
          </div>

          {/* Bar 4 */}
          <div className="flex flex-col items-center">
            <div className="bg-chart-blue border border-blue-300 rounded-lg w-3 h-[166px]"></div>
          </div>
        </div>

        {/* X-axis labels */}
        <div className="right-0 bottom-0 left-12 absolute flex justify-around text-gray-text text-sm">
          <span>Riyadh- B1</span>
          <span className="font-bold text-black-text">Riyadh- B1</span>
          <span>Riyadh- B1</span>
          <span>Riyadh- B1</span>
        </div>
      </div>
    </div>
  );
};
