import React from "react";

interface PieChartProps {
  title: string;
}

export const PieChart: React.FC<PieChartProps> = ({ title }) => {
  return (
    <div className="shadow-lg p-4 rounded-[20px] w-full h-[450px]">
      <h3 className="mb-6 font-bold text-black-text text-xl">{title}</h3>

      <div className="flex justify-between items-center">
        {/* Pie Chart */}
        <div className="relative">
          <div className="relative rounded-full w-[305px] h-[305px]">
            {/* Mumbai - 25% (Blue) */}
            <svg
              className="absolute inset-0 w-full h-full"
              viewBox="0 0 151 136"
              style={{ transform: "rotate(0deg)" }}
            >
              <path
                d="M0.5 0C36.3379 4.27362e-07 71.0309 12.6215 98.491 35.6496C125.951 58.6778 144.424 90.6413 150.668 125.931L98.109 135.23C94.0506 112.292 82.0432 91.5155 64.1942 76.5473C46.3451 61.579 23.7946 53.375 0.5 53.375V0Z"
                fill="#267CFE"
              />
            </svg>

            {/* Delhi - 25% (Orange) */}
            <svg
              className="absolute inset-0 w-full h-full"
              viewBox="0 0 108 159"
              style={{ transform: "rotate(90deg)" }}
            >
              <path
                d="M106.422 0.615265C110.995 32.1544 105.573 64.3339 90.9165 92.6329C76.2603 120.932 53.1067 143.928 24.7085 158.391L0.485535 110.829C18.9444 101.428 33.9942 86.4807 43.5207 68.0864C53.0472 49.6921 56.5717 28.7754 53.599 8.27492L106.422 0.615265Z"
                fill="#FF8263"
              />
            </svg>

            {/* Bangalore - 20% (Purple) */}
            <svg
              className="absolute inset-0 w-full h-full"
              viewBox="0 0 175 85"
              style={{ transform: "rotate(180deg)" }}
            >
              <path
                d="M174.948 70.7125C146.045 84.1899 113.644 88.2671 82.3031 82.3705C50.9621 76.474 22.2582 60.9004 0.22937 37.8406L38.8241 0.971375C53.1429 15.9603 71.8004 26.0831 92.172 29.9159C112.544 33.7486 133.604 31.0984 152.391 22.3381L174.948 70.7125Z"
                fill="#C07CFB"
              />
            </svg>

            {/* Hyderabad - 15% (Green) */}
            <svg
              className="absolute inset-0 w-full h-full"
              viewBox="0 0 79 163"
              style={{ transform: "rotate(252deg)" }}
            >
              <path
                d="M38.9712 162.321C19.5715 140.69 6.75489 113.968 2.02993 85.2993C-2.69503 56.6306 0.86834 27.2087 12.3006 0.496819L61.3704 21.4979C53.9394 38.8607 51.6232 57.9849 54.6945 76.6196C57.7657 95.2542 66.0965 112.624 78.7063 126.683L38.9712 162.321Z"
                fill="#82E3B2"
              />
            </svg>

            {/* Chennai - 15% (Yellow) */}
            <svg
              className="absolute inset-0 w-full h-full"
              viewBox="0 0 135 110"
              style={{ transform: "rotate(306deg)" }}
            >
              <path
                d="M0.917364 86.718C12.8262 61.8108 31.273 40.6035 54.29 25.3586C77.3071 10.1137 104.031 1.40297 131.61 0.155708L134.022 53.4762C116.095 54.2869 98.7246 59.9489 83.7635 69.8581C68.8025 79.7673 56.812 93.552 49.0713 109.742L0.917364 86.718Z"
                fill="#ECE58A"
              />
            </svg>

            {/* Center text */}
            <div className="absolute inset-0 flex justify-center items-center">
              <div className="text-center">
                <div className="font-bold text-black-text text-xl">Branch</div>
                <div className="font-bold text-black-text text-xl">
                  distribution
                </div>
              </div>
            </div>

            {/* Mumbai tooltip */}
            <div className="top-[84px] right-[40px] absolute bg-primary-dark shadow-lg px-4 py-2 rounded-lg font-bold text-white text-sm text-center">
              Mumbai 30%
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="flex flex-col gap-5 ml-8">
          <div className="flex items-center gap-2.5">
            <div className="bg-[#267CFE] shadow-lg border-2 border-white rounded-full w-2.5 h-2.5"></div>
            <span className="text-black-text text-lg">Mumbai - 25%</span>
          </div>
          <div className="flex items-center gap-2.5">
            <div className="bg-[#FF8263] shadow-lg border-2 border-white rounded-full w-2.5 h-2.5"></div>
            <span className="text-black-text text-lg">Delhi - 25%</span>
          </div>
          <div className="flex items-center gap-2.5">
            <div className="bg-[#C07CFB] shadow-lg border-2 border-white rounded-full w-2.5 h-2.5"></div>
            <span className="text-black-text text-lg">Bangalore - 20%</span>
          </div>
          <div className="flex items-center gap-2.5">
            <div className="bg-[#82E3B2] shadow-lg border-2 border-white rounded-full w-2.5 h-2.5"></div>
            <span className="text-black-text text-lg">Hyderabad - 15%</span>
          </div>
          <div className="flex items-center gap-2.5">
            <div className="bg-[#ECE58A] shadow-lg border-2 border-white rounded-full w-2.5 h-2.5"></div>
            <span className="text-black-text text-lg">Chennai - 15%</span>
          </div>
        </div>
      </div>
    </div>
  );
};
