import { DeleteSectionModal } from "@/components/DeleteSectionModal";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { zodResolver } from "@hookform/resolvers/zod";
import { Edit, Plus, Trash2, ChevronUp, ChevronDown } from "lucide-react";
import { useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import * as z from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { isUUID } from "@/lib/utils";
import { createTemplate, updateTemplate } from "@/data/templates";
import { useTranslation } from "react-i18next";
import { useFieldDefaults } from "@/hooks/useFieldDefaults";

// Enhanced Zod schemas with preferred answer
const templateFieldSchema = z.object({
  id: z.string(),
  questionText: z.string().min(1, "Question text is required"),
  questionType: z.enum(["Text", "YES/NO"]),
  options: z.array(z.string()).optional(),
  required: z.boolean(),
  addHint: z.string().optional(),
  requireEvidence: z.boolean(),
  scoreWeight: z
    .number()
    .min(1, "Score weight must be at least 1")
    .max(10, "Score weight cannot exceed 10"),
  placeholder: z.string().optional(),
  order: z.number().min(1, "Order must be a positive integer"),
  preferredAnswer: z.string().optional(),
  hasPreferredAnswer: z.boolean().default(false),
});

const sectionSchema = z.object({
  id: z.string(),
  order: z.number(),
  fields: z.array(templateFieldSchema),
});

const templateFormSchema = z.object({
  title: z.string().min(3, "Template name is required"),
  businessType: z.string().min(1, "Industry type is required"),
  passRate: z.string().regex(/^\d+$/, "Pass rate must be a number"),
  sections: z.array(sectionSchema).min(1, "At least one section is required"),
});

export type TemplateFormData = z.infer<typeof templateFormSchema>;

interface TemplateFormProps {
  defaultValues?: Partial<TemplateFormData>;
  isEdit?: boolean;
  templateId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const TemplateForm = ({
  defaultValues,
  isEdit = false,
  templateId,
  onSuccess,
  onCancel,
}: TemplateFormProps) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { getNewFieldDefaults, getNewSectionWithFieldDefaults } =
    useFieldDefaults();

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState<{
    type: "section" | "field";
    sectionIndex: number;
    fieldIndex?: number;
  } | null>(null);

  const form = useForm<TemplateFormData>({
    resolver: zodResolver(templateFormSchema),
    defaultValues: {
      ...defaultValues,
    },
  });

  const {
    fields: sections,
    append: appendSection,
    remove: removeSection,
  } = useFieldArray({
    control: form.control,
    name: "sections",
  });

  const handleDeleteSection = (sectionIndex: number) => {
    setDeleteTarget({ type: "section", sectionIndex });
    setShowDeleteModal(true);
  };

  const handleDeleteField = (sectionIndex: number, fieldIndex: number) => {
    setDeleteTarget({ type: "field", sectionIndex, fieldIndex });
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (deleteTarget) {
      if (deleteTarget.type === "section") {
        removeSection(deleteTarget.sectionIndex);
      } else if (
        deleteTarget.type === "field" &&
        deleteTarget.fieldIndex !== undefined
      ) {
        const currentSection = form.getValues(
          `sections.${deleteTarget.sectionIndex}`
        );
        const updatedFields = currentSection.fields.filter(
          (_, index) => index !== deleteTarget.fieldIndex
        );
        form.setValue(
          `sections.${deleteTarget.sectionIndex}.fields`,
          updatedFields
        );
      }
      setShowDeleteModal(false);
      setDeleteTarget(null);
    }
  };

  const addSection = () => {
    const currentSections = form.getValues("sections");
    const newOrder = Math.max(...currentSections.map((s) => s.order), 0) + 1;

    const sectionDefaults = getNewSectionWithFieldDefaults();

    const newSection = {
      id: Date.now().toString(),
      ...sectionDefaults,
      order: newOrder,
      fields: [
        {
          id: (Date.now() + 1).toString(),
          ...getNewFieldDefaults(),
          questionText: t("templateBuilder.dummyText"),
          addHint: t("templateBuilder.tooltipHint"),
          order: 1,
          hasPreferredAnswer: false, // Text questions don't have preferred answers by default
          questionType: "Text" as const, // Explicitly set default
        },
      ],
    };
    appendSection(newSection);
  };

  const addField = (sectionIndex: number) => {
    const currentSection = form.getValues(`sections.${sectionIndex}`);
    const newOrder =
      Math.max(...currentSection.fields.map((f) => f.order), 0) + 1;

    const newField = {
      id: Date.now().toString(),
      ...getNewFieldDefaults(),
      questionText: t("templateBuilder.newQuestion"),
      order: newOrder,
      hasPreferredAnswer: false, // Text is default, will be updated if changed to YES/NO
      questionType: "Text" as const, // Explicitly set default
    };

    const updatedFields = [...currentSection.fields, newField];
    form.setValue(`sections.${sectionIndex}.fields`, updatedFields);
  };

  // Function to update hasPreferredAnswer based on question type
  const updatePreferredAnswerFlag = (
    sectionIndex: number,
    fieldIndex: number,
    questionType: string
  ) => {
    const shouldHavePreferredAnswer = questionType === "YES/NO";
    form.setValue(
      `sections.${sectionIndex}.fields.${fieldIndex}.hasPreferredAnswer`,
      shouldHavePreferredAnswer
    );

    // Clear preferred answer if changing from YES/NO to Text
    if (!shouldHavePreferredAnswer) {
      form.setValue(
        `sections.${sectionIndex}.fields.${fieldIndex}.preferredAnswer`,
        ""
      );
    }
  };

  // Reordering functions - update local state only
  const handleMoveSectionUp = (sectionIndex: number) => {
    if (sectionIndex === 0) return; // Can't move first section up

    const currentSections = form.getValues("sections");

    // Swap sections in the array
    const newSections = [...currentSections];
    [newSections[sectionIndex], newSections[sectionIndex - 1]] = [
      newSections[sectionIndex - 1],
      newSections[sectionIndex],
    ];

    // Recalculate orders for all sections to ensure proper sequencing
    const reorderedSections = newSections.map((section, index) => ({
      ...section,
      order: index + 1,
    }));

    form.setValue("sections", reorderedSections);
  };

  const handleMoveSectionDown = (sectionIndex: number) => {
    const currentSections = form.getValues("sections");
    if (sectionIndex === currentSections.length - 1) return; // Can't move last section down

    // Swap sections in the array
    const newSections = [...currentSections];
    [newSections[sectionIndex], newSections[sectionIndex + 1]] = [
      newSections[sectionIndex + 1],
      newSections[sectionIndex],
    ];

    // Recalculate orders for all sections to ensure proper sequencing
    const reorderedSections = newSections.map((section, index) => ({
      ...section,
      order: index + 1,
    }));

    form.setValue("sections", reorderedSections);
  };

  const handleMoveFieldUp = (sectionIndex: number, fieldIndex: number) => {
    if (fieldIndex === 0) return; // Can't move first field up

    const currentSection = form.getValues(`sections.${sectionIndex}`);
    const sortedFields = [...currentSection.fields].sort(
      (a, b) => a.order - b.order
    );

    if (fieldIndex === 0) return; // Can't move first field up in sorted order

    // Find the actual array indices of the fields to swap
    const currentFieldOrder = sortedFields[fieldIndex].order;
    const previousFieldOrder = sortedFields[fieldIndex - 1].order;

    // Swap the order values
    const updatedFields = currentSection.fields.map((field) => {
      if (field.order === currentFieldOrder) {
        return { ...field, order: previousFieldOrder };
      } else if (field.order === previousFieldOrder) {
        return { ...field, order: currentFieldOrder };
      }
      return field;
    });

    form.setValue(`sections.${sectionIndex}.fields`, updatedFields);
  };

  const handleMoveFieldDown = (sectionIndex: number, fieldIndex: number) => {
    const currentSection = form.getValues(`sections.${sectionIndex}`);
    const sortedFields = [...currentSection.fields].sort(
      (a, b) => a.order - b.order
    );

    if (fieldIndex === sortedFields.length - 1) return; // Can't move last field down

    // Find the actual array indices of the fields to swap
    const currentFieldOrder = sortedFields[fieldIndex].order;
    const nextFieldOrder = sortedFields[fieldIndex + 1].order;

    // Swap the order values
    const updatedFields = currentSection.fields.map((field) => {
      if (field.order === currentFieldOrder) {
        return { ...field, order: nextFieldOrder };
      } else if (field.order === nextFieldOrder) {
        return { ...field, order: currentFieldOrder };
      }
      return field;
    });

    form.setValue(`sections.${sectionIndex}.fields`, updatedFields);
  };

  // Mutations
  const createTemplateMutation = useMutation({
    mutationFn: createTemplate,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
      toast.success("Template created successfully!");
      onSuccess?.();
    },
    onError: (error) => {
      toast.error("Failed to create template: " + error);
      console.error("Error creating template:", error);
    },
  });

  const updateTemplateMutation = useMutation({
    mutationFn: (templateData: TemplateFormData) => {
      if (!templateId) throw new Error("Template ID is required for update");
      return updateTemplate(templateId, templateData);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
      queryClient.invalidateQueries({ queryKey: ["template", templateId] });
      toast.success("Template updated successfully!");
      onSuccess?.();
    },
    onError: (error) => {
      toast.error("Failed to update template: " + error);
      console.error("Error updating template:", error);
    },
  });

  const onSubmit = (data: TemplateFormData) => {
    // Sort sections and fields by their current order in the form to ensure proper sequencing
    const sortedSections = [...data.sections].sort((a, b) => a.order - b.order);

    const templateData = {
      ...data,
      sections: sortedSections.map((section, index) => ({
        name: t("templateBuilder.section", { number: index + 1 }),
        order: section.order,
        id: isUUID(section.id) ? section.id : undefined,
        fields: [...section.fields]
          .sort((a, b) => a.order - b.order)
          .map((field) => ({
            id: isUUID(field.id) ? field.id : undefined,
            question: field.questionText,
            questionType: field.questionType,
            required: field.required,
            requiresEvidence: field.requireEvidence,
            score: field.scoreWeight,
            hint: field.addHint,
            order: field.order,
            preferredAnswer: field.hasPreferredAnswer
              ? field.preferredAnswer
              : undefined,
          })),
      })),
    };

    if (isEdit) {
      updateTemplateMutation.mutate(templateData);
    } else {
      createTemplateMutation.mutate(templateData);
    }
  };

  const isLoading =
    createTemplateMutation.isPending || updateTemplateMutation.isPending;

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Template Info */}
            <Card className="mb-6 p-6">
              <div className="flex flex-row *:flex-1 gap-6 w-full">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("templateBuilder.templateName")}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="businessType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("templateBuilder.industryType")}</FormLabel>
                      <Input {...field} />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </Card>

            {/* Sections */}
            <div className="space-y-6">
              {[...sections]
                .sort((a, b) => a.order - b.order)
                .map((section, displayIndex) => {
                  // Find the original index in the sections array
                  const originalSectionIndex = sections.findIndex(
                    (s) => s.id === section.id
                  );

                  return (
                    <Card key={section.id} className="p-6">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="font-medium text-gray-900 text-lg">
                          {t("templateBuilder.section", {
                            number: displayIndex + 1,
                          })}
                        </h3>
                        <div className="flex items-center space-x-2">
                          {/* Section reordering buttons */}
                          <Tooltip>
                            <TooltipTrigger>
                              <Button
                                variant="ghost"
                                size="sm"
                                type="button"
                                onClick={() =>
                                  handleMoveSectionUp(displayIndex)
                                }
                                disabled={displayIndex === 0}
                              >
                                <ChevronUp className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{t("editAssignment.moveSectionUp")}</p>
                            </TooltipContent>
                          </Tooltip>
                          <Tooltip>
                            <TooltipTrigger>
                              <Button
                                variant="ghost"
                                size="sm"
                                type="button"
                                onClick={() =>
                                  handleMoveSectionDown(displayIndex)
                                }
                                disabled={displayIndex === sections.length - 1}
                              >
                                <ChevronDown className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{t("editAssignment.moveSectionDown")}</p>
                            </TooltipContent>
                          </Tooltip>
                          <Button variant="ghost" size="sm" type="button">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            type="button"
                            onClick={() =>
                              handleDeleteSection(originalSectionIndex)
                            }
                          >
                            <Trash2 className="w-4 h-4 text-red-500" />
                          </Button>
                        </div>
                      </div>

                      {/* Fields within this section */}
                      <div className="space-y-4">
                        {[
                          ...form.watch(
                            `sections.${originalSectionIndex}.fields`
                          ),
                        ]
                          .sort((a, b) => a.order - b.order)
                          .map((field, fieldDisplayIndex) => {
                            // Find the original field index in the unsorted array
                            const sectionFields = form.getValues(
                              `sections.${originalSectionIndex}.fields`
                            );
                            const originalFieldIndex = sectionFields.findIndex(
                              (f) => f.id === field.id
                            );

                            return (
                              <Card
                                key={field.id}
                                className="p-4 border-l-4 border-l-blue-500"
                              >
                                <div className="flex justify-between items-center mb-4">
                                  <h4 className="font-medium text-gray-800">
                                    {t("templateBuilder.field", {
                                      number: fieldDisplayIndex + 1,
                                    })}
                                  </h4>
                                  <div className="flex items-center space-x-2">
                                    {/* Field reordering buttons */}
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          type="button"
                                          onClick={() =>
                                            handleMoveFieldUp(
                                              originalSectionIndex,
                                              fieldDisplayIndex
                                            )
                                          }
                                          disabled={fieldDisplayIndex === 0}
                                        >
                                          <ChevronUp className="w-4 h-4" />
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>{t("editAssignment.moveFieldUp")}</p>
                                      </TooltipContent>
                                    </Tooltip>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          type="button"
                                          onClick={() =>
                                            handleMoveFieldDown(
                                              originalSectionIndex,
                                              fieldDisplayIndex
                                            )
                                          }
                                          disabled={
                                            fieldDisplayIndex ===
                                            form.watch(
                                              `sections.${originalSectionIndex}.fields`
                                            ).length -
                                              1
                                          }
                                        >
                                          <ChevronDown className="w-4 h-4" />
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>
                                          {t("editAssignment.moveFieldDown")}
                                        </p>
                                      </TooltipContent>
                                    </Tooltip>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      type="button"
                                      onClick={() =>
                                        handleDeleteField(
                                          originalSectionIndex,
                                          originalFieldIndex
                                        )
                                      }
                                    >
                                      <Trash2 className="w-4 h-4 text-red-500" />
                                    </Button>
                                  </div>
                                </div>

                                <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
                                  <FormField
                                    control={form.control}
                                    name={`sections.${originalSectionIndex}.fields.${originalFieldIndex}.questionText`}
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>
                                          {t("templateBuilder.questionText")}
                                        </FormLabel>
                                        <FormControl>
                                          <Input {...field} />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <FormField
                                    control={form.control}
                                    name={`sections.${originalSectionIndex}.fields.${originalFieldIndex}.questionType`}
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>
                                          {t("templateBuilder.questionType")}
                                        </FormLabel>
                                        <Select
                                          onValueChange={(value) => {
                                            field.onChange(value);
                                            updatePreferredAnswerFlag(
                                              originalSectionIndex,
                                              originalFieldIndex,
                                              value
                                            );
                                          }}
                                          defaultValue={field.value}
                                        >
                                          <FormControl>
                                            <SelectTrigger>
                                              <SelectValue />
                                            </SelectTrigger>
                                          </FormControl>
                                          <SelectContent>
                                            <SelectItem value="Text">
                                              Text
                                            </SelectItem>
                                            <SelectItem value="YES/NO">
                                              Yes/No
                                            </SelectItem>
                                          </SelectContent>
                                        </Select>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  {form.watch(
                                    `sections.${originalSectionIndex}.fields.${originalFieldIndex}.questionType`
                                  ) === "Text" && (
                                    <div className="md:col-span-2">
                                      <FormField
                                        control={form.control}
                                        name={`sections.${originalSectionIndex}.fields.${originalFieldIndex}.placeholder`}
                                        render={({ field }) => (
                                          <FormItem>
                                            <FormLabel>
                                              {t("templateBuilder.placeholder")}
                                            </FormLabel>
                                            <FormControl>
                                              <Textarea {...field} />
                                            </FormControl>
                                            <FormMessage />
                                          </FormItem>
                                        )}
                                      />
                                    </div>
                                  )}

                                  {/* Preferred Answer Section */}
                                  {form.watch(
                                    `sections.${originalSectionIndex}.fields.${originalFieldIndex}.hasPreferredAnswer`
                                  ) && (
                                    <div className="md:col-span-2">
                                      <FormField
                                        control={form.control}
                                        name={`sections.${originalSectionIndex}.fields.${originalFieldIndex}.preferredAnswer`}
                                        render={({ field }) => (
                                          <FormItem>
                                            <FormLabel>
                                              Preferred Answer
                                            </FormLabel>
                                            <FormControl>
                                              {form.watch(
                                                `sections.${originalSectionIndex}.fields.${originalFieldIndex}.questionType`
                                              ) === "YES/NO" ? (
                                                <Select
                                                  onValueChange={field.onChange}
                                                  defaultValue={field.value}
                                                >
                                                  <SelectTrigger>
                                                    <SelectValue placeholder="Select preferred answer" />
                                                  </SelectTrigger>
                                                  <SelectContent>
                                                    <SelectItem value="YES">
                                                      Yes
                                                    </SelectItem>
                                                    <SelectItem value="NO">
                                                      No
                                                    </SelectItem>
                                                  </SelectContent>
                                                </Select>
                                              ) : (
                                                <Textarea
                                                  {...field}
                                                  placeholder="Enter the preferred answer for this question"
                                                />
                                              )}
                                            </FormControl>
                                            <FormDescription>
                                              This answer will be used as a
                                              reference for scoring or
                                              evaluation purposes.
                                            </FormDescription>
                                            <FormMessage />
                                          </FormItem>
                                        )}
                                      />
                                    </div>
                                  )}

                                  <FormField
                                    control={form.control}
                                    name={`sections.${originalSectionIndex}.fields.${originalFieldIndex}.addHint`}
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>
                                          {t("templateBuilder.addHint")}
                                        </FormLabel>
                                        <FormControl>
                                          <Input {...field} />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <FormField
                                    control={form.control}
                                    name={`sections.${originalSectionIndex}.fields.${originalFieldIndex}.scoreWeight`}
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>
                                          {t("templateBuilder.scoreWeight")}
                                        </FormLabel>
                                        <FormControl>
                                          <Input
                                            type="number"
                                            {...field}
                                            onChange={(e) =>
                                              field.onChange(
                                                Number(e.target.value)
                                              )
                                            }
                                          />
                                        </FormControl>
                                        <FormDescription>
                                          {t("templateBuilder.scoreWeightNote")}
                                        </FormDescription>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <div className="space-y-4 md:col-span-2">
                                    <FormField
                                      control={form.control}
                                      name={`sections.${originalSectionIndex}.fields.${originalFieldIndex}.required`}
                                      render={({ field }) => (
                                        <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                                          <FormControl>
                                            <Checkbox
                                              checked={field.value}
                                              onCheckedChange={field.onChange}
                                            />
                                          </FormControl>
                                          <FormLabel className="text-sm">
                                            {t("templateBuilder.required")}
                                          </FormLabel>
                                        </FormItem>
                                      )}
                                    />

                                    <FormField
                                      control={form.control}
                                      name={`sections.${originalSectionIndex}.fields.${originalFieldIndex}.requireEvidence`}
                                      render={({ field }) => (
                                        <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                                          <FormControl>
                                            <Checkbox
                                              checked={field.value}
                                              onCheckedChange={field.onChange}
                                            />
                                          </FormControl>
                                          <FormLabel className="text-sm">
                                            {t(
                                              "templateBuilder.requireEvidence"
                                            )}
                                          </FormLabel>
                                        </FormItem>
                                      )}
                                    />

                                    {form.watch(
                                      `sections.${originalSectionIndex}.fields.${originalFieldIndex}.requireEvidence`
                                    ) && (
                                      <div className="p-3 border rounded-lg">
                                        <div className="flex items-center space-x-2 text-gray-600 text-sm">
                                          <span>📷</span>
                                          <span>Attach image</span>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </Card>
                            );
                          })}

                        {/* Add Field Button */}
                        <Button
                          variant="outline"
                          onClick={() => addField(originalSectionIndex)}
                          type="button"
                          className="border-2 border-blue-300 hover:border-blue-400 border-dashed w-full h-10"
                        >
                          <Plus className="mr-2 w-4 h-4" />
                          {t("templateBuilder.addField")}
                        </Button>
                      </div>
                    </Card>
                  );
                })}

              {/* Add Section Button */}
              <Button
                variant="outline"
                onClick={addSection}
                type="button"
                className="border-2 border-gray-300 hover:border-gray-400 border-dashed w-full h-12"
              >
                <Plus className="mr-2 w-4 h-4" />
                {t("templateBuilder.addSection")}
              </Button>
            </div>
            <FormField
              control={form.control}
              name={"passRate"}
              render={({ field }) => (
                <FormItem className="flex-1 mr-4">
                  <label htmlFor="passRate">
                    {t("templateBuilder.passRate")}
                  </label>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      min={0}
                      max={100}
                      step={0.01}
                      placeholder={t("templateBuilder.passRatePlaceholder")}
                      className="font-medium text-gray-900 text-lg"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Footer Buttons */}
            <div className="flex justify-end items-center space-x-4 mt-8">
              <Button variant="outline" type="button" onClick={onCancel}>
                {t("templateBuilder.cancel")}
              </Button>
              <Button
                type="submit"
                className="bg-slate-700 hover:bg-slate-800"
                disabled={isLoading}
              >
                {isLoading
                  ? isEdit
                    ? t("templateBuilder.updating")
                    : t("templateBuilder.creating")
                  : isEdit
                  ? t("templateBuilder.update")
                  : t("templateBuilder.create")}
              </Button>
            </div>
          </form>
        </Form>

        {/* Delete Modal */}
        <DeleteSectionModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={confirmDelete}
        />
      </div>
    </TooltipProvider>
  );
};

export default TemplateForm;
