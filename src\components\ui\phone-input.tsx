import React from "react";
import PhoneInputWithCountrySelect from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { cn } from "@/lib/utils";

interface PhoneInputProps {
  value?: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  error?: boolean;
}

const PhoneInput = React.forwardRef<HTMLInputElement, PhoneInputProps>(
  ({ className, error, onChange, ...props }, ref) => {
    const handleChange = (value: string | undefined) => {
      console.log("Phone input changed:", value);
      onChange(value);
    };

    return (
      <div className={cn("relative", className, error && "error")}>
        <PhoneInputWithCountrySelect
          international
          countryCallingCodeEditable={false}
          defaultCountry="US"
          {...props}
          onChange={handleChange}
          className={cn(
            "flex bg-background disabled:opacity-50 px-3 py-2 border border-input rounded-md focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring ring-offset-background focus-visible:ring-offset-2 w-full h-10 placeholder:text-muted-foreground text-sm disabled:cursor-not-allowed",
            error && "border-red-500 focus-visible:ring-red-500"
          )}
        />
      </div>
    );
  }
);

PhoneInput.displayName = "PhoneInput";

export { PhoneInput };
