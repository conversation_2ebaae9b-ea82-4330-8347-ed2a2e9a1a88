import React, { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  CheckCircle,
  MessageCircle,
  Eye,
  Calendar,
  User,
  Building,
} from "lucide-react";
import {
  fetchIssueAssignments,
  BasicIssueAssignmentResponse,
} from "@/data/issueAssignments";
import { supabase } from "@/integrations/supabase/client";
import MaintainerActionsModal from "@/components/issue-reports/MaintainerActionsModal";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from "@/components/ui/dialog";

const MyAssignments: React.FC = () => {
  const { t } = useTranslation();
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [selectedAssignment, setSelectedAssignment] =
    useState<BasicIssueAssignmentResponse | null>(null);
  const [maintainerModalOpen, setMaintainerModalOpen] = useState(false);
  const [maintainerAction, setMaintainerAction] = useState<{
    type: "comments" | "complete";
    assignmentId: string;
    reportTitle: string;
    currentComments?: string;
  } | null>(null);

  // Get current user email
  useEffect(() => {
    const fetchUserEmail = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session?.user?.email) {
        setUserEmail(data.session.user.email);
      }
    };
    fetchUserEmail();
  }, []);

  // Fetch assignments for current maintainer
  const {
    data: assignmentsResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["my-issue-assignments", userEmail],
    queryFn: () => fetchIssueAssignments({ limit: 100 }),
    enabled: !!userEmail,
    staleTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: 1000,
  });

  // Filter assignments for current user
  const myAssignments =
    assignmentsResponse?.assignments.filter(
      (assignment) => assignment.maintenanceEmployeeEmail === userEmail
    ) || [];

  const handleMaintainerAction = (
    type: "comments" | "complete",
    assignmentId: string,
    reportTitle: string,
    currentComments?: string
  ) => {
    setMaintainerAction({ type, assignmentId, reportTitle, currentComments });
    setMaintainerModalOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800", label: "Pending" },
      in_progress: { color: "bg-blue-100 text-blue-800", label: "In Progress" },
      resolved: { color: "bg-green-100 text-green-800", label: "Resolved" },
      closed: { color: "bg-gray-100 text-gray-800", label: "Closed" },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getPriorityLevel = (createdAt: string) => {
    const created = new Date(createdAt);
    const now = new Date();
    const hoursDiff = (now.getTime() - created.getTime()) / (1000 * 60 * 60);

    if (hoursDiff > 48) return { level: "High", color: "text-red-600" };
    if (hoursDiff > 24) return { level: "Medium", color: "text-yellow-600" };
    return { level: "Normal", color: "text-green-600" };
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <Skeleton className="w-64 h-8" />
            <Skeleton className="w-96 h-4" />
          </CardHeader>
        </Card>
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="space-y-3">
                <Skeleton className="w-full h-6" />
                <Skeleton className="w-3/4 h-4" />
                <div className="flex space-x-2">
                  <Skeleton className="w-20 h-8" />
                  <Skeleton className="w-20 h-8" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="text-red-600">
            {t("branchReport.table.errorLoading")}: {String(error)}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-6 h-6" />
            {t("branchReport.myAssignments")}
          </CardTitle>
          <p className="text-gray-600">
            {t("branchReport.myAssignmentsDescription")}
          </p>
        </CardHeader>
      </Card>

      {myAssignments.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-gray-500">
              <CheckCircle className="mx-auto mb-4 w-12 h-12 text-gray-300" />
              <h3 className="mb-2 font-medium text-lg">
                {t("branchReport.noAssignments")}
              </h3>
              <p>{t("branchReport.noAssignmentsDescription")}</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="gap-4 grid">
          {myAssignments.map((assignment) => {
            const priority = getPriorityLevel(assignment.createdAt.toString());
            const canTakeAction =
              assignment.status !== "resolved" &&
              assignment.status !== "closed";

            return (
              <Card
                key={assignment.id}
                className="hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1 space-y-3">
                      {/* Title and Status */}
                      <div className="flex justify-between items-start">
                        <h3 className="font-semibold text-gray-900 text-lg line-clamp-2">
                          {assignment.issueTitle || t("common.notAvailable")}
                        </h3>
                        <div className="flex items-center gap-2 ml-4">
                          {getStatusBadge(assignment.status)}
                          <span
                            className={`text-xs font-medium ${priority.color}`}
                          >
                            {priority.level} Priority
                          </span>
                        </div>
                      </div>

                      {/* Description */}
                      {assignment.issueDescription && (
                        <p className="text-gray-600 line-clamp-3">
                          {assignment.issueDescription}
                        </p>
                      )}

                      {/* Metadata */}
                      <div className="flex items-center gap-4 text-gray-500 text-sm">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>
                            {new Date(
                              assignment.createdAt
                            ).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Building className="w-4 h-4" />
                          <span>Assigned to you</span>
                        </div>
                      </div>

                      {/* Comments */}
                      {assignment.comments && (
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="mb-1 font-medium text-gray-700 text-sm">
                            {t("branchReport.comments")}:
                          </div>
                          <p className="text-gray-600 text-sm line-clamp-2">
                            {assignment.comments}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-between items-center mt-4 pt-4 border-gray-200 border-t">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedAssignment(assignment)}
                        >
                          <Eye className="mr-2 w-4 h-4" />
                          {t("branchReport.table.viewDetails")}
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>
                            {selectedAssignment?.issueTitle ||
                              t("branchReport.assignmentDetails")}
                          </DialogTitle>
                          <DialogDescription>
                            <div className="space-y-4 mt-4">
                              <div>
                                <h4 className="mb-2 font-medium">
                                  {t("branchReport.description")}:
                                </h4>
                                <p className="text-gray-600">
                                  {selectedAssignment?.issueDescription ||
                                    t("branchReport.table.noDescription")}
                                </p>
                              </div>

                              <div className="gap-4 grid grid-cols-2">
                                <div>
                                  <h4 className="mb-1 font-medium">
                                    {t("branchReport.table.status")}:
                                  </h4>
                                  {selectedAssignment &&
                                    getStatusBadge(selectedAssignment.status)}
                                </div>
                                <div>
                                  <h4 className="mb-1 font-medium">
                                    {t("common.priority")}:
                                  </h4>
                                  {selectedAssignment && (
                                    <span
                                      className={`text-sm font-medium ${
                                        getPriorityLevel(
                                          selectedAssignment.createdAt.toString()
                                        ).color
                                      }`}
                                    >
                                      {
                                        getPriorityLevel(
                                          selectedAssignment.createdAt.toString()
                                        ).level
                                      }
                                    </span>
                                  )}
                                </div>
                              </div>

                              <div>
                                <h4 className="mb-1 font-medium">
                                  {t("branchReport.assignedDate")}:
                                </h4>
                                <p className="text-gray-600">
                                  {selectedAssignment &&
                                    new Date(
                                      selectedAssignment.createdAt
                                    ).toLocaleString()}
                                </p>
                              </div>

                              {selectedAssignment?.comments && (
                                <div>
                                  <h4 className="mb-2 font-medium">
                                    {t("branchReport.comments")}:
                                  </h4>
                                  <div className="bg-gray-50 p-3 rounded-lg">
                                    <p className="text-gray-600">
                                      {selectedAssignment.comments}
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>
                          </DialogDescription>
                        </DialogHeader>
                      </DialogContent>
                    </Dialog>

                    {canTakeAction && (
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleMaintainerAction(
                              "comments",
                              assignment.id,
                              assignment.issueTitle || "Report",
                              assignment.comments || ""
                            )
                          }
                        >
                          <MessageCircle className="mr-2 w-4 h-4" />
                          {t("branchReport.addComments")}
                        </Button>
                        <Button
                          size="sm"
                          onClick={() =>
                            handleMaintainerAction(
                              "complete",
                              assignment.id,
                              assignment.issueTitle || "Report",
                              assignment.comments || ""
                            )
                          }
                        >
                          <CheckCircle className="mr-2 w-4 h-4" />
                          {t("branchReport.markAsCompleted")}
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Maintainer Actions Modal */}
      {maintainerAction && (
        <MaintainerActionsModal
          isOpen={maintainerModalOpen}
          onClose={() => {
            setMaintainerModalOpen(false);
            setMaintainerAction(null);
          }}
          assignmentId={maintainerAction.assignmentId}
          reportTitle={maintainerAction.reportTitle}
          actionType={maintainerAction.type}
          currentComments={maintainerAction.currentComments}
        />
      )}
    </div>
  );
};

export default MyAssignments;
