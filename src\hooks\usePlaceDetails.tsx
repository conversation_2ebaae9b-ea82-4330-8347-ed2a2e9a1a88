import { useCallback, useState } from 'react';

export function usePlaceDetails() {
  const [details, setDetails] = useState<google.maps.places.PlaceResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchDetails = useCallback((placeId: string) => {
    const service = new google.maps.places.PlacesService(document.createElement('div'));
    service.getDetails(
      { placeId, fields: ['name', 'rating', 'reviews', 'geometry', 'formatted_address'] },
      (place, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK) {
          setDetails(place);
        } else {
          setError(status);
        }
      }
    );
  }, []);

  return { details, error, fetchDetails };
}