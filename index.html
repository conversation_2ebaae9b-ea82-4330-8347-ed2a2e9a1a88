
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Y-Verify - Branch Report & Audit Management System</title>
    <meta name="description" content="Stay ahead with a cloud-based, AI-powered checklist and audit management system designed to simplify operations, improve compliance, and ensure quality across all locations." />
    <meta name="author" content="Y-Verify" />
    <meta name="theme-color" content="#374151" />
    <meta name="keywords" content="audit, checklist, branch management, compliance, quality assurance, business operations" />
    
    <!-- Manifest and Icons -->
    <link rel="manifest" href="/manifest.json" />
    <link rel="icon" type="image/svg+xml" href="/placeholder.svg" />
    <link rel="icon" type="image/png" sizes="192x192" href="/logo192.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/notification-icon.png" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Y-Verify - Branch Report & Audit Management System" />
    <meta property="og:description" content="Stay ahead with a cloud-based, AI-powered checklist and audit management system designed to simplify operations, improve compliance, and ensure quality across all locations." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://console.y-verify.com" />
    <meta property="og:image" content="/logo192.png" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Y-Verify - Branch Report & Audit Management System" />
    <meta name="twitter:description" content="Cloud-based audit management system for better compliance and quality assurance." />
    <meta name="twitter:image" content="/logo192.png" />
    
    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="Y-Verify">
    
    <!-- iOS PWA Support -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Y-Verify" />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/logo192.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/logo192.png" />
    
    <!-- Windows PWA Support -->
    <meta name="msapplication-TileImage" content="/logo192.png" />
    <meta name="msapplication-TileColor" content="#374151" />
    <meta name="msapplication-config" content="none" />
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="/sw.js" as="script" />
    
    <!-- Cairo Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo&display=swap" rel="stylesheet">
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- PWA Install Prompt Script -->
    <script>
      // Add install prompt event listener early
      let deferredPrompt;
      
      window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent Chrome 67 and earlier from automatically showing the prompt
        e.preventDefault();
        // Stash the event so it can be triggered later
        deferredPrompt = e;
        console.log('PWA install prompt available');
      });
      
      window.addEventListener('appinstalled', () => {
        console.log('PWA was installed');
        // Hide the install promotion
        deferredPrompt = null;
      });
    </script>
  </body>
</html>
