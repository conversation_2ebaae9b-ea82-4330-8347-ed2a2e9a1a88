import { fetchSubmissionDistribution } from "@/data/analytics";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip, ResponsiveContainer } from "recharts";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";

export function SubmissionDistributionChart({
  storeId,
  filter: defaultFilter = "template",
}: {
  storeId: string;
  filter?: "template" | "branch" | "assignment";
}) {
  const [filter, setFilter] = useState<"template" | "branch" | "assignment">(
    defaultFilter
  );

  const { data, isLoading, error } = useQuery({
    queryKey: ["submission-distribution", storeId, filter],
    queryFn: () => fetchSubmissionDistribution(filter, storeId),
    staleTime: 5 * 60 * 1000,
  });
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-40">
        Loading...
      </div>
    );
  }

  if (error) {
    console.error(error);
    return (
      <div className="flex justify-center items-center min-h-40 text-red-500">
        Error: {error.message}
      </div>
    );
  }

  console.log(data);

  const COLORS = [
    "#267CFE",
    "#FF8263",
    "#C07CFB",
    "#82E3B2",
    "#ECE58A",
    "#FFB347",
    "#A4DE02",
    "#FC6C85",
  ];

  const chartData = data.map((item, index) => ({
    ...item,
    color: COLORS[index % COLORS.length],
    percentage: Number(item.percentage),
  }));

  console.log(chartData);

  return (
    <Card className="p-6 w-full">
      <div className="flex md:flex-row flex-col md:justify-between md:items-center gap-4 mb-6">
        <h2 className="font-bold text-dashboard-primary text-lg">
          Submission distribution
        </h2>
        <Select
          value={filter}
          onValueChange={(val) => setFilter(val as typeof filter)}
          disabled={!!defaultFilter}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select filter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="template">By Template</SelectItem>
            <SelectItem value="branch">By Branch</SelectItem>
            <SelectItem value="assignment">By Assignment</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex md:flex-row flex-col justify-center items-center gap-10">
        <div className="w-[300px] h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                dataKey="percentage"
                nameKey="name"
                innerRadius={70}
                outerRadius={120}
                paddingAngle={2}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value: number | string) => `${value}%`} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="flex flex-col gap-3">
          {chartData.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className="shadow-sm border-2 border-white rounded-full w-2.5 h-2.5"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-dashboard-primary text-lg">
                {item.name} – {item.percentage}%
              </span>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
}
