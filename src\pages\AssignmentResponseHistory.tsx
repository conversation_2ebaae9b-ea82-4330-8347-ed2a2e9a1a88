import { useState, useEffect, useCallback } from "react";
import { Link, useSearchParams } from "react-router-dom";
import {
  Search,
  Calendar,
  ChevronDown,
  Eye,
  Edit,
  Loader2,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { fetchAllResponses } from "@/data/response";
import {
  fetchDefaultsSettingsByUserId,
  fetchDefaultDefaultsSettings,
} from "@/data/settings";
import { useTranslation } from "react-i18next";
import { useDateFormatter } from "@/hooks/useLocalizationSettings";
import { useCurrentUser } from "@/hooks/useCurrentUser";

interface Submission {
  id: string;
  templateName: string;
  submissionDate: string;
  branch: string;
  status: string;
  score: string;
  scoreColor: "success" | "warning" | "error";
  userName: string;
  userId: string;
}

interface DateRange {
  startDate: Date;
  endDate: Date;
}

const AssignmentResponseHistory = () => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const itemsPerPage = 10;
  const { formatDate } = useDateFormatter();
  const { data: currentUser } = useCurrentUser();

  // Fetch user's defaults settings with fallback to default settings
  const { data: userDefaultsSettings } = useQuery({
    queryKey: ["defaultsSettings"],
    queryFn: fetchDefaultsSettingsByUserId,
    retry: false,
  });

  const { data: defaultDefaultsSettings } = useQuery({
    queryKey: ["defaultDefaultsSettings"],
    queryFn: fetchDefaultDefaultsSettings,
  });

  // Get the current settings (user's or defaults)
  const currentDefaultsSettings =
    userDefaultsSettings || defaultDefaultsSettings;

  // URL search params state
  const status = searchParams.get("status") || "all";
  const templateName = searchParams.get("templateName") || "";
  const page = parseInt(searchParams.get("page") || "1", 10);

  // Date range state
  const [dateRange, setDateRange] = useState<DateRange | null>(null);

  // Popover states for date pickers
  const [startDateOpen, setStartDateOpen] = useState(false);
  const [endDateOpen, setEndDateOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(templateName);
  // Update URL search params helper
  const updateSearchParams = useCallback(
    (updates: Record<string, string | number>) => {
      const newParams = new URLSearchParams(searchParams);

      Object.entries(updates).forEach(([key, value]) => {
        if (value === "" || value === "all" || value === 1) {
          newParams.delete(key);
        } else {
          newParams.set(key, value.toString());
        }
      });

      setSearchParams(newParams);
    },
    [searchParams, setSearchParams]
  );
  // Add these useEffects after your existing useEffects
  useEffect(() => {
    setSearchTerm(templateName);
  }, [templateName]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm !== templateName) {
        updateSearchParams({ templateName: searchTerm, page: 1 });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, templateName, updateSearchParams]);

  // Fetch responses using React Query
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["responses", status, page, dateRange, templateName],
    queryFn: () =>
      fetchAllResponses({
        status: status === "all" ? undefined : status,
        page,
        limit: itemsPerPage,
        dateRange,
        templateName: templateName || undefined,
      }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const responses = data?.result || [];
  const pagination = data?.pagination;
  // Transform API data to match the component's expected format
  const transformedSubmissions: Submission[] =
    responses?.map((response) => ({
      id: response.id,
      templateName:
        response.assignmentTitle ||
        t("assignmentResponseHistory.unknownTemplate"),
      submissionDate: response.submittedAt,
      branch: `${response.storeName} - ${response.branchLocation}`,
      status: response.status,
      score: response.scoreSummary
        ? `${response.scoreSummary.percentage.toFixed(2)}% - ${
            response.scoreSummary.passed
              ? t("assignmentResponseHistory.passed")
              : t("assignmentResponseHistory.failed")
          }`
        : t("assignmentResponseHistory.noScore"),
      scoreColor: response.scoreSummary.passed ? "success" : "error",
      userName: response.userName || t("assignmentResponseHistory.unknownUser"),
      userId: response.userId,
    })) || [];

  const totalPages = Math.ceil((pagination?.total || 0) / itemsPerPage);

  // Handle status change
  const handleStatusChange = (newStatus: string) => {
    updateSearchParams({ status: newStatus, page: 1 });
  };

  const handleTemplateNameChange = (newTemplateName: string) => {
    setSearchTerm(newTemplateName);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    updateSearchParams({ page: newPage });
  };

  // Handle date range change
  const handleStartDateChange = (date: Date | undefined) => {
    if (date) {
      setDateRange((prev) => ({
        startDate: date,
        endDate: prev?.endDate || date,
      }));
      setStartDateOpen(false);
    }
  };

  const handleEndDateChange = (date: Date | undefined) => {
    if (date) {
      setDateRange((prev) => ({
        startDate: prev?.startDate || date,
        endDate: date,
      }));
      setEndDateOpen(false);
    }
  };

  // Clear date range
  const clearDateRange = () => {
    setDateRange(null);
  };

  // Format date for display - this function is now replaced by the hook
  // const formatDate = (date: Date) => {
  //   return date.toLocaleDateString("en-US", {
  //     month: "short",
  //     day: "numeric",
  //     year: "numeric",
  //   });
  // };

  if (isLoading) {
    return (
      <div className="bg-background p-6 min-h-screen">
        <div className="mx-auto max-w-7xl">
          <div className="flex justify-center items-center h-64">
            <div className="flex flex-col items-center gap-4">
              <Loader2 className="w-8 h-8 text-primary animate-spin" />
              <p className="text-muted-foreground">
                {t("assignmentResponseHistory.loadingSubmissions")}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="bg-background p-6 min-h-screen">
        <div className="mx-auto max-w-7xl">
          <Alert variant="destructive" className="mx-auto mt-8 max-w-md">
            <AlertCircle className="w-4 h-4" />
            <AlertDescription>
              {t("assignmentResponseHistory.failedToLoad")}{" "}
              {error instanceof Error
                ? error.message
                : t("assignmentResponseHistory.tryAgainLater")}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background p-6 min-h-screen">
      <div className="mx-auto max-w-7xl">
        {/* Header */}
        <div className="mb-6">
          <h1 className="mb-6 font-semibold text-foreground text-2xl">
            {t("assignmentResponseHistory.checklistSubmission")}
          </h1>

          {/* Filters */}
          <div className="flex sm:flex-row flex-col gap-4 mb-6">
            <div className="relative flex-1 max-w-xs">
              <Search className="top-1/2 left-3 absolute w-4 h-4 text-muted-foreground -translate-y-1/2 transform" />
              <Input
                placeholder={t("assignmentResponseHistory.searchPlaceholder")}
                className="pl-10"
                value={searchTerm}
                onChange={(e) => handleTemplateNameChange(e.target.value)}
              />
            </div>

            <div className="flex gap-4">
              {/* Date Range Filter */}
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground text-sm">
                  {t("assignmentResponseHistory.dateRange")} :
                </span>
                <div className="flex gap-2">
                  <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="gap-2">
                        <Calendar className="w-4 h-4" />
                        {dateRange?.startDate
                          ? formatDate(dateRange.startDate)
                          : t("assignmentResponseHistory.startDate")}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="p-0 w-auto" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={dateRange?.startDate}
                        onSelect={handleStartDateChange}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>

                  <span className="text-muted-foreground">
                    {t("assignmentResponseHistory.to")}
                  </span>

                  <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="gap-2">
                        <Calendar className="w-4 h-4" />
                        {dateRange?.endDate
                          ? formatDate(dateRange.endDate)
                          : t("assignmentResponseHistory.endDate")}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="p-0 w-auto" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={dateRange?.endDate}
                        onSelect={handleEndDateChange}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>

                  {dateRange && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={clearDateRange}
                            className="p-0 w-8 h-8"
                          >
                            ✕
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Clear date range</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
              </div>

              {/* Status Filter */}
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground text-sm">
                  {t("assignmentResponseHistory.status")} :
                </span>
                <Select value={status} onValueChange={handleStatusChange}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      {t("assignmentResponseHistory.all")}
                    </SelectItem>
                    <SelectItem value="on-time">
                      {t("assignmentResponseHistory.onTime")}
                    </SelectItem>
                    <SelectItem value="overdue">
                      {t("assignmentResponseHistory.overdue")}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Table */}
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-muted/30 border-b">
                  <tr>
                    <th className="p-4 font-medium text-muted-foreground text-left">
                      {t("assignmentResponseHistory.userName")}
                    </th>
                    <th className="p-4 font-medium text-muted-foreground text-left">
                      {t("assignmentResponseHistory.templateName")}
                    </th>
                    <th className="p-4 font-medium text-muted-foreground text-left">
                      {t("assignmentResponseHistory.submissionDate")}
                    </th>
                    <th className="p-4 font-medium text-muted-foreground text-left">
                      {t("assignmentResponseHistory.branch")}
                    </th>
                    <th className="p-4 font-medium text-muted-foreground text-left">
                      {t("assignmentResponseHistory.status")}
                    </th>
                    <th className="p-4 font-medium text-muted-foreground text-left">
                      {t("assignmentResponseHistory.score")}
                    </th>
                    <th className="p-4 font-medium text-muted-foreground text-left">
                      {t("assignmentResponseHistory.action")}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {transformedSubmissions.length > 0 ? (
                    transformedSubmissions.map((submission) => (
                      <tr
                        key={submission.id}
                        className="hover:bg-muted/20 border-b"
                      >
                        <td className="p-4 font-medium">
                          {submission.userName}
                        </td>
                        <td className="p-4 font-medium">
                          {submission.templateName}
                        </td>
                        <td className="p-4 text-muted-foreground">
                          {formatDate(submission.submissionDate)}
                        </td>
                        <td className="p-4 text-muted-foreground">
                          {submission.branch}
                        </td>
                        <td className="p-4">
                          <Badge
                            variant={
                              submission.status === "On Time"
                                ? "success"
                                : "destructive"
                            }
                          >
                            {submission.status}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <Badge variant={submission.scoreColor}>
                            {submission.score}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center gap-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Link
                                    to={`/assignment-response/response/${submission.id}`}
                                  >
                                    <Button variant="ghost" size="sm">
                                      <Eye className="w-4 h-4" />
                                    </Button>
                                  </Link>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>View details of submission</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>

                            {/* Edit button - only show if current user owns the response and allows edits after submission */}
                            {currentUser?.id === submission.userId &&
                              currentDefaultsSettings?.allowEditAfterSubmission !==
                                false && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Link
                                        to={`/assignment-response/edit/${submission.id}`}
                                      >
                                        <Button variant="ghost" size="sm">
                                          <Edit className="w-4 h-4" />
                                        </Button>
                                      </Link>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Edit response</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={7}
                        className="p-8 text-muted-foreground text-center"
                      >
                        {t("assignmentResponseHistory.noSubmissionsFound")}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-between items-center mt-6">
            <div className="text-muted-foreground text-sm">
              {t("assignmentResponseHistory.showingResults", {
                start: (page - 1) * itemsPerPage + 1,
                end: Math.min(page * itemsPerPage, pagination?.total || 0),
                total: pagination?.total || 0,
              })}
            </div>
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(page - 1)}
                      disabled={page === 1}
                    >
                      <ChevronLeft className="mr-1 w-4 h-4" />
                      {t("global.previous")}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Go to previous page</p>
                  </TooltipContent>
                </Tooltip>

                <div className="flex gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNumber =
                      Math.max(1, Math.min(totalPages - 4, page - 2)) + i;
                    return (
                      <Tooltip key={pageNumber}>
                        <TooltipTrigger asChild>
                          <Button
                            variant={
                              page === pageNumber ? "default" : "outline"
                            }
                            size="sm"
                            onClick={() => handlePageChange(pageNumber)}
                            className="w-8 h-8"
                          >
                            {pageNumber}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Go to page {pageNumber}</p>
                        </TooltipContent>
                      </Tooltip>
                    );
                  })}
                </div>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(page + 1)}
                      disabled={page === totalPages}
                    >
                      {t("global.next")}
                      <ChevronRight className="ml-1 w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Go to next page</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AssignmentResponseHistory;
