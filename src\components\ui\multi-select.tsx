// components/ui/multi-select.tsx
import { Check, ChevronsUpDown } from "lucide-react";
import {
  Command,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useState } from "react";

export type MultiSelectOption = {
  label: string;
  value: string;
};

interface MultiSelectProps {
  options: MultiSelectOption[];
  selected: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  isLoading?: boolean;
  disabled?: boolean;
}

export function MultiSelect({
  options,
  selected,
  onChange,
  placeholder = "Select...",
  isLoading = false,
  disabled = false,
}: MultiSelectProps) {
  const [open, setOpen] = useState(false);

  const toggleSelect = (value: string) => {
    if (selected.includes(value)) {
      onChange(selected.filter((v) => v !== value));
    } else {
      onChange([...selected, value]);
    }
  };

  const getLabel = (value: string) =>
    options.find((opt) => opt.value === value)?.label ?? value;

  return (
    <Popover
      open={open && !disabled}
      onOpenChange={disabled ? undefined : setOpen}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            "shadow-sm px-3 pt-2 pb-1 border border-border rounded-xl w-full min-h-[44px] text-left",
            "flex flex-wrap items-start gap-2",
            disabled && "opacity-50 cursor-not-allowed"
          )}
        >
          <div className="flex flex-wrap flex-1 items-center gap-1">
            {selected.slice(0, 2).map((value) => (
              <Badge
                key={value}
                variant="secondary"
                className="px-2 py-1 rounded-full text-xs"
              >
                {getLabel(value)}
              </Badge>
            ))}

            {selected.length > 2 && (
              <span className="text-muted-foreground text-xs">
                +{selected.length - 2} more
              </span>
            )}

            {selected.length === 0 && (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </div>

          <ChevronsUpDown className="opacity-50 mt-1 w-4 h-4 shrink-0" />
        </Button>
      </PopoverTrigger>

      <PopoverContent className="relative p-0 w-[300px] max-h-64 overflow-y-auto">
        <Command>
          <CommandInput placeholder="Search..." />
          <CommandList>
            {isLoading ? (
              <CommandItem disabled>Loading...</CommandItem>
            ) : options.length === 0 ? (
              <CommandItem disabled>No options found</CommandItem>
            ) : (
              options.map((option) => (
                <CommandItem
                  key={option.value}
                  onSelect={() => !disabled && toggleSelect(option.value)}
                  disabled={disabled}
                >
                  <div className="flex items-center gap-2">
                    <Check
                      className={cn(
                        "w-4 h-4",
                        selected.includes(option.value)
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {option.label}
                  </div>
                </CommandItem>
              ))
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
