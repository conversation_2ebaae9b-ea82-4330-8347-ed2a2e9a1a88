import React, { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";

import { updateIssueAssignmentStatus } from "@/data/issueAssignments";

const maintainerCommentsSchema = z.object({
  comments: z.string().optional(),
});

type MaintainerCommentsFormData = z.infer<typeof maintainerCommentsSchema>;

interface MaintainerActionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  assignmentId: string;
  reportTitle: string;
  actionType: "comments" | "complete";
  currentComments?: string;
}

const MaintainerActionsModal: React.FC<MaintainerActionsModalProps> = ({
  isOpen,
  onClose,
  assignmentId,
  reportTitle,
  actionType,
  currentComments = "",
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const form = useForm<MaintainerCommentsFormData>({
    resolver: zodResolver(maintainerCommentsSchema),
    defaultValues: {
      comments: currentComments,
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: MaintainerCommentsFormData) => {
      const status =
        actionType === "complete"
          ? ("resolved" as const)
          : ("in_progress" as const);
      return updateIssueAssignmentStatus(assignmentId, {
        status,
        comments: data.comments?.trim() || undefined,
      });
    },
    onSuccess: () => {
      const successMessage =
        actionType === "complete"
          ? t("branchReport.markCompletedSuccess")
          : t("branchReport.commentsUpdatedSuccess");
      toast.success(successMessage);

      queryClient.invalidateQueries({ queryKey: ["issue-reports"] });
      queryClient.invalidateQueries({ queryKey: ["issue-assignments"] });
      queryClient.invalidateQueries({
        queryKey: ["issue-assignment", assignmentId],
      });

      form.reset();
      onClose();
    },
    onError: (error) => {
      console.error("Update failed:", error);
      const errorMessage =
        actionType === "complete"
          ? t("branchReport.markCompletedFailed")
          : t("branchReport.commentsUpdateFailed");
      toast.error(error.message || errorMessage);
    },
  });

  const onSubmit = (data: MaintainerCommentsFormData) => {
    updateMutation.mutate(data);
  };

  const handleClose = () => {
    if (!updateMutation.isPending) {
      form.reset({ comments: currentComments });
      onClose();
    }
  };

  const isCompleting = actionType === "complete";
  const title = isCompleting
    ? t("branchReport.markAsCompleted")
    : t("branchReport.addComments");
  const description = isCompleting
    ? t("branchReport.markAsCompletedDescription", { reportTitle })
    : t("branchReport.addCommentsDescription", { reportTitle });
  const submitText = isCompleting
    ? t("branchReport.markCompleted")
    : t("branchReport.updateComments");

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="comments"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {isCompleting
                      ? t("branchReport.completionComments")
                      : t("branchReport.comments")}
                    {!isCompleting && " (" + t("global.optional") + ")"}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={
                        isCompleting
                          ? t("branchReport.completionCommentsPlaceholder")
                          : t("branchReport.commentsPlaceholder")
                      }
                      rows={4}
                      {...field}
                      disabled={updateMutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={updateMutation.isPending}
              >
                {t("global.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={updateMutation.isPending}
                variant={isCompleting ? "default" : "outline"}
              >
                {updateMutation.isPending && (
                  <Loader2 className="mr-2 w-4 h-4 animate-spin" />
                )}
                {submitText}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default MaintainerActionsModal;
