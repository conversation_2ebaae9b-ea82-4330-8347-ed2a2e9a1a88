import React, { useRef } from "react";
import { RotateCcw } from "lucide-react";
import { Select } from "../ui/select-settings";
import { useTranslation } from "react-i18next";
import { languageOptions } from "@/lib/utils";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  fetchDefaultLocalizationSettings,
  fetchLocalizationSettingsByUserId,
  createLocalizationSettings,
  updateLocalizationSettings,
  applyDefaultLocalizationSettings,
} from "@/data/settings";
import { LocalizationSettings } from "@/lib/types";
import { toast } from "sonner";
import { TIMEZONE_OPTIONS } from "@/lib/timezone";

export const LocalizationTab: React.FC = () => {
  const { i18n, t } = useTranslation();
  const language = i18n.language;
  const queryClient = useQueryClient();
  const debounceRef = useRef<NodeJS.Timeout>();

  // Fetch default localization settings
  const { data: defaultSettings, isLoading: defaultsLoading } = useQuery({
    queryKey: ["defaultLocalizationSettings"],
    queryFn: fetchDefaultLocalizationSettings,
  });

  // Fetch user's current localization settings
  const { data: userSettings, isLoading: userSettingsLoading } = useQuery({
    queryKey: ["localizationSettings"],
    queryFn: fetchLocalizationSettingsByUserId,
    retry: false,
  });

  // Get current values (user settings or defaults)
  const currentSettings = userSettings || defaultSettings;
  const isLoading = defaultsLoading || userSettingsLoading;

  // Mutation to create/update localization settings with optimistic updates
  const createMutation = useMutation({
    mutationFn: createLocalizationSettings,
    onMutate: async (newSettings) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["localizationSettings"] });

      // Snapshot the previous value
      const previousSettings = queryClient.getQueryData([
        "localizationSettings",
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData(["localizationSettings"], newSettings);

      // Return a context object with the snapshotted value
      return { previousSettings };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["localizationSettings"] });
      toast.success(t("settings.localization.settingsCreated"));
    },
    onError: (error, newSettings, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(
        ["localizationSettings"],
        context?.previousSettings
      );
      toast.error(t("settings.localization.createFailed") + ": " + error);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({ queryKey: ["localizationSettings"] });
    },
  });

  const updateMutation = useMutation({
    mutationFn: updateLocalizationSettings,
    onMutate: async (newSettings) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["localizationSettings"] });

      // Snapshot the previous value
      const previousSettings = queryClient.getQueryData([
        "localizationSettings",
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData(["localizationSettings"], newSettings);

      // Return a context object with the snapshotted value
      return { previousSettings };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["localizationSettings"] });
      toast.success(t("settings.localization.settingsUpdated"));
    },
    onError: (error, newSettings, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(
        ["localizationSettings"],
        context?.previousSettings
      );
      toast.error(t("settings.localization.updateFailed") + ": " + error);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({ queryKey: ["localizationSettings"] });
    },
  });

  // Mutation to apply default settings with optimistic updates
  const applyDefaultsMutation = useMutation({
    mutationFn: applyDefaultLocalizationSettings,
    onMutate: async () => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["localizationSettings"] });

      // Snapshot the previous value
      const previousSettings = queryClient.getQueryData([
        "localizationSettings",
      ]);

      // Optimistically update to default settings
      if (defaultSettings) {
        queryClient.setQueryData(["localizationSettings"], defaultSettings);
      }

      // Return a context object with the snapshotted value
      return { previousSettings };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["localizationSettings"] });
      toast.success(t("settings.localization.defaultsApplied"));
    },
    onError: (error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(
        ["localizationSettings"],
        context?.previousSettings
      );
      toast.error(t("settings.localization.defaultsFailed") + ": " + error);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({ queryKey: ["localizationSettings"] });
    },
  });

  const handleChangeLanguage = (value: string) => {
    // Immediately apply the language change for instant UI feedback
    i18n.changeLanguage(value);
    document.dir = value === "ar" ? "rtl" : "ltr";

    // Save the language change with optimistic update
    handleFieldChange("language", value);
  };

  // Handle reset to defaults
  const handleResetToDefaults = () => {
    applyDefaultsMutation.mutate();
  };

  // Handle field changes with optimistic updates
  const handleFieldChange = (
    field: keyof LocalizationSettings,
    value: string
  ) => {
    // Don't update if settings aren't loaded yet
    if (!currentSettings) return;

    const newSettings = { ...currentSettings, [field]: value };

    // Immediately update the UI optimistically
    queryClient.setQueryData(["localizationSettings"], newSettings);

    // Clear any existing debounce timeout
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Debounce the actual API call
    debounceRef.current = setTimeout(() => {
      if (userSettings) {
        updateMutation.mutate(newSettings);
      } else {
        createMutation.mutate(newSettings);
      }
    }, 1000);
  };

  const dateFormatOptions = [
    { value: "dd-mm-yyyy", label: "DD-MM-YYYY" },
    { value: "mm-dd-yyyy", label: "MM-DD-YYYY" },
    { value: "yyyy-mm-dd", label: "YYYY-MM-DD" },
  ];

  // Use the comprehensive timezone options from our timezone utility
  const timeZoneOptions = [...TIMEZONE_OPTIONS];

  const weekDayOptions = [
    { value: "sunday", label: t("calendarComponent.dayNames.sunday") },
    { value: "monday", label: t("calendarComponent.dayNames.monday") },
    { value: "tuesday", label: t("calendarComponent.dayNames.tuesday") },
    { value: "wednesday", label: t("calendarComponent.dayNames.wednesday") },
    { value: "thursday", label: t("calendarComponent.dayNames.thursday") },
    { value: "friday", label: t("calendarComponent.dayNames.friday") },
    { value: "saturday", label: t("calendarComponent.dayNames.saturday") },
  ];

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
            {t("settings.localization.title")}
          </h2>
        </div>
        <div className="flex justify-center items-center py-8">
          <div className="border-primary border-b-2 rounded-full w-8 h-8 animate-spin"></div>
          <span className="ml-3 text-gray-600">
            {t("settings.localization.loadingSettings")}
          </span>
        </div>
      </div>
    );
  }

  if (!currentSettings) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
            {t("settings.localization.title")}
          </h2>
        </div>
        <div className="flex justify-center items-center py-8">
          <span className="text-gray-600">
            {t("settings.localization.noSettingsAvailable")}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header with Reset Button */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
            {t("settings.localization.title")}
          </h2>
          {/* Show saving indicator when mutations are in progress */}
          {(createMutation.isPending || updateMutation.isPending) && (
            <div className="flex items-center gap-2 text-gray-600 text-sm">
              <div className="border-primary border-b-2 rounded-full w-3 h-3 animate-spin"></div>
              <span>{t("settings.localization.saving")}</span>
            </div>
          )}
        </div>
        <button
          onClick={handleResetToDefaults}
          disabled={applyDefaultsMutation.isPending}
          className="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 px-4 py-2 border border-gray-300 rounded-lg transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          <span className="font-medium text-sm">
            {applyDefaultsMutation.isPending
              ? t("settings.localization.resetting")
              : t("settings.localization.resetToDefaults")}
          </span>
        </button>
      </div>

      {/* Settings Grid */}
      <div className="space-y-6">
        {/* Language */}
        <div className="flex sm:flex-row flex-col sm:items-center gap-3 sm:gap-7">
          <label className="w-full sm:w-32 font-medium text-black-text">
            {t("settings.localization.language")}
          </label>
          <div className="flex-1 max-w-md">
            <Select
              value={language}
              onValueChange={(value) => handleChangeLanguage(value)}
              options={languageOptions}
            />
          </div>
        </div>

        {/* Date Format */}
        <div className="flex sm:flex-row flex-col sm:items-center gap-3 sm:gap-7">
          <label className="w-full sm:w-32 font-medium text-black-text">
            {t("settings.localization.dateFormat")}
          </label>
          <div className="flex-1 max-w-md">
            <Select
              value={currentSettings.dateFormat}
              onValueChange={(value) => handleFieldChange("dateFormat", value)}
              options={dateFormatOptions}
            />
          </div>
        </div>

        {/* Time Zone */}
        <div className="flex sm:flex-row flex-col sm:items-center gap-3 sm:gap-7">
          <label className="w-full sm:w-32 font-medium text-black-text">
            {t("settings.localization.timeZone")}
          </label>
          <div className="flex-1 max-w-md">
            <Select
              value={currentSettings.timeZone}
              onValueChange={(value) => handleFieldChange("timeZone", value)}
              options={timeZoneOptions}
            />
          </div>
        </div>

        {/* First Day of Week */}
        <div className="flex sm:flex-row flex-col sm:items-center gap-3 sm:gap-7">
          <label className="w-full sm:w-32 font-medium text-black-text">
            {t("settings.localization.firstDayOfWeek")}
          </label>
          <div className="flex-1 max-w-md">
            <Select
              value={currentSettings.firstDayOfWeek}
              onValueChange={(value) =>
                handleFieldChange("firstDayOfWeek", value)
              }
              options={weekDayOptions}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
