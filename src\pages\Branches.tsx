import { BranchTable } from "@/components/BranchTable";
import { Header } from "@/components/CompanyHeader";
import { Sidebar } from "@/components/Sidebar";
import { CompanyTable } from "@/components/CompanyTable";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useQuery } from "@tanstack/react-query";
import { canUserAccess } from "@/data/permissions";
import { useAuth } from "@/contexts/AuthContext";

const BranchManagement = () => {
  const { t } = useTranslation();
  const { storeId } = useParams<{ storeId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Check if user can create branches
  const { data: canCreateBranches = false } = useQuery({
    queryKey: ["canCreateBranches"],
    queryFn: () => canUserAccess("crud_branches"),
    enabled: !!user,
    staleTime: Infinity, // Cache until logout
    gcTime: Infinity,
  });

  return (
    <div className="flex min-h-screen">
      <div className="flex-1">
        <div className="min-h-screen">
          {canCreateBranches && (
            <div className="p-6">
              <Button
                onClick={() => navigate("/companies/branches/new")}
                className="bg-slate-800 hover:bg-slate-900 text-white"
              >
                <Plus className="mr-2 w-4 h-4" />
                {t("branchManagement.newBranch")}
              </Button>
            </div>
          )}

          <main className="p-6">
            <div className="mb-6">
              <h1 className="mb-2 font-semibold text-slate-900 text-2xl">
                {t("branchManagement.title")}
              </h1>
            </div>

            <BranchTable storeId={storeId} />
          </main>
        </div>
      </div>
    </div>
  );
};

export default BranchManagement;
