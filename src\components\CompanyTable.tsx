import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { deleteCompany, fetchCompanies } from "@/data/stores";
import { canUserAccess } from "@/data/permissions";
import { companyFilterConfigs } from "@/lib/constants";
import { Filter } from "@/lib/types";
import {
  addFilter,
  generatePaginationButtons,
  parseFiltersFromUrl,
  removeFilter,
  updateFilter,
  updateUrlParams,
} from "@/lib/utils";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  BarChart,
  Edit2,
  Eye,
  MoreHorizontal,
  Plus,
  Trash2,
} from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { DeleteSectionModal } from "./DeleteSectionModal";
import FilterRow from "./FilterRow";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

export const ITEMS_PER_PAGE = 10;

export function CompanyTable() {
  const { t, i18n } = useTranslation();
  const language = i18n.language || "en";
  const direction = i18n.dir(language) || "ltr";
  const [searchParams, setSearchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [companyToDelete, setCompanyToDelete] = useState(null);

  const [filters, setFilters] = useState<Filter[]>(
    parseFiltersFromUrl(searchParams)
  );
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Update URL when filters change
  useEffect(() => {
    const newParams = new URLSearchParams(searchParams);

    if (filters.length > 0) {
      newParams.set("filters", encodeURIComponent(JSON.stringify(filters)));
    } else {
      newParams.delete("filters");
    }

    // Reset to first page when filters change
    newParams.delete("page");

    setSearchParams(newParams);
  }, [filters, searchParams, setSearchParams]);

  const deleteMutation = useMutation({
    mutationFn: (companyId: string) => deleteCompany(companyId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["companies"] });
    },
    onError: (error) => {
      console.error("Delete failed:", error);
      toast.error("Failed to delete company: " + error);
    },
  });

  const {
    data: companiesResult,
    isLoading: companiesLoading,
    error: companiesError,
  } = useQuery({
    queryKey: ["companies", filters, currentPage],
    queryFn: () => fetchCompanies(filters, currentPage),
    staleTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: 1000,
  });

  // Check if user can edit/delete companies
  const { data: canEditCompanies = false } = useQuery({
    queryKey: ["user-can-access", "crud_stores"],
    queryFn: () => canUserAccess("crud_stores"),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const companies = companiesResult?.data || [];
  const totalPages = companiesResult?.totalPages || 0;
  const total = companiesResult?.total || 0;

  if (companiesError) {
    console.error(companiesError);
  }

  const paginationButtons = generatePaginationButtons({
    currentPage,
    totalPages,
  });

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="font-medium text-slate-900 text-lg">
            {t("companyManagement.filters")}
          </h3>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={() => addFilter(filters, setFilters)}
                  variant="outline"
                  size="sm"
                >
                  <Plus className="mr-2 w-4 h-4" />
                  {t("companyManagement.addFilter")}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{t("common.addNewFilter")}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {filters.length > 0 && (
          <div className="space-y-3">
            {filters.map((filter) => (
              <FilterRow
                key={filter.id}
                filter={filter}
                onUpdate={(newFilter) =>
                  updateFilter(newFilter, filters, setFilters)
                }
                onRemove={(id) => removeFilter(id, filters, setFilters)}
                filterConfig={companyFilterConfigs}
              />
            ))}
          </div>
        )}

        {filters.length === 0 && (
          <div className="py-8 text-slate-500 text-center">
            <p>{t("companyTable.noFiltersApplied")}</p>
          </div>
        )}
      </div>

      {/* Results info */}
      <div className="flex justify-between items-center">
        <div className="text-slate-600 text-sm">
          {t("global.showingToOfResults", {
            "0":
              companies.length > 0 ? (currentPage - 1) * ITEMS_PER_PAGE + 1 : 0,
            "1": Math.min(currentPage * ITEMS_PER_PAGE, total),
            "2": total,
          })}
        </div>
      </div>

      {/* Table */}
      <div className="border border-slate-200 rounded-lg overflow-hidden">
        <table className="w-full" dir={"ltr"}>
          <thead className="border-slate-200 border-b">
            <tr>
              <th className="px-4 py-3 font-medium text-slate-600 text-sm text-left">
                {t("companyManagement.table.companyName")}
              </th>
              <th className="px-4 py-3 font-medium text-slate-600 text-sm text-left">
                {t("companyManagement.table.businessType")}
              </th>
              <th className="px-4 py-3 font-medium text-slate-600 text-sm text-left">
                {t("companyManagement.table.branchesRooms")}
              </th>
              <th className="px-4 py-3 font-medium text-slate-600 text-sm text-left">
                {t("companyManagement.table.storeAdmin")}
              </th>
              <th className="px-4 py-3 font-medium text-slate-600 text-sm text-left">
                {t("companyManagement.table.status")}
              </th>
              <th className="px-4 py-3 font-medium text-slate-600 text-sm text-left">
                {t("companyManagement.table.createdDate")}
              </th>
              <th className="px-4 py-3 font-medium text-slate-600 text-sm text-left">
                {t("companyManagement.table.action")}
              </th>
            </tr>
          </thead>
          <tbody>
            {companiesLoading ? (
              <tr className={""}>
                <td className="px-4 py-4 font-medium text-slate-900 text-sm">
                  {t("common.loading")}
                </td>
                <td className="px-4 py-4 text-slate-600 text-sm"></td>
                <td className="px-4 py-4 text-slate-600 text-sm"></td>
                <td className="px-4 py-4 text-slate-600 text-sm"></td>
                <td className="px-4 py-4">
                  <div className="flex items-center space-x-2"></div>
                </td>
                <td className="px-4 py-4 text-slate-600 text-sm"></td>
                <td className="px-4 py-4">
                  <div className="flex items-center space-x-2">
                    <button className="p-1 text-slate-400 hover:text-slate-600 transition-colors">
                      <Edit2 className="w-4 h-4" />
                    </button>
                    <Button variant="ghost" size="sm" className="p-0">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                    <button className="p-1 text-slate-400 hover:text-slate-600 transition-colors">
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ) : companies.length === 0 ? (
              <tr className="">
                <td
                  colSpan={7}
                  className="px-4 py-8 text-slate-500 text-center"
                >
                  {t("companyTable.noCompaniesFound")}
                </td>
              </tr>
            ) : (
              companies.map((company, index) => (
                <tr key={company.id}>
                  <td className="px-4 py-4 font-medium text-slate-900 text-sm">
                    {company.name}
                  </td>
                  <td className="px-4 py-4 text-slate-600 text-sm">
                    {company.business_type}
                  </td>
                  <td className="flex flex-row items-center gap-2 px-4 py-4 text-slate-600 text-sm">
                    <span>
                      {company.number_of_branches} {t("companyTable.rooms")}
                    </span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Link to={`/companies/${company.id}/branches`}>
                            <Button variant="ghost" size="sm" className="p-0">
                              <Eye className="w-4 h-4" />
                            </Button>
                          </Link>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>View branches</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </td>
                  <td className="px-4 py-4 text-slate-600 text-sm">
                    {company.store_admin}
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={company.status}
                        disabled
                        className="data-[state=checked]:bg-slate-800"
                        dir={"ltr"}
                      />
                      <span className="text-slate-600 text-sm">
                        {company.status
                          ? t("companyManagement.table.active")
                          : t("companyManagement.table.inactive")}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-4 text-slate-600 text-sm">
                    {new Date(company.created_at).toLocaleString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    })}
                  </td>
                  <td className="px-4 py-4">
                    <TooltipProvider>
                      <div className="flex items-center space-x-2">
                        {canEditCompanies && (
                          <Link to={`/companies/edit/${company.id}`}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <button className="p-1 text-slate-400 hover:text-slate-600 transition-colors">
                                  <Edit2 className="w-4 h-4" />
                                </button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Edit company</p>
                              </TooltipContent>
                            </Tooltip>
                          </Link>
                        )}
                        <Link to={`/advanced-reporting/${company.id}`}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <button className="p-1 text-slate-400 hover:text-slate-600 transition-colors">
                                <BarChart className="w-4 h-4" />
                              </button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Advanced reporting</p>
                            </TooltipContent>
                          </Tooltip>
                        </Link>
                        {canEditCompanies && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="p-0 text-slate-400 hover:text-slate-600 transition-colors"
                                onClick={() => {
                                  setCompanyToDelete(company);
                                  setDeleteModalOpen(true);
                                }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Delete company</p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                        <Link to={`/reporting-dashboard/${company.id}`}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <button className="p-1 text-slate-400 hover:text-slate-600 transition-colors">
                                <MoreHorizontal className="w-4 h-4" />
                              </button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Reporting dashboard</p>
                            </TooltipContent>
                          </Tooltip>
                        </Link>
                      </div>
                    </TooltipProvider>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2">
          <button
            onClick={() =>
              updateUrlParams(
                {
                  page: currentPage - 1,
                },
                searchParams,
                setSearchParams
              )
            }
            disabled={currentPage === 1}
            className={`px-3 py-2 text-sm transition-colors ${
              currentPage === 1
                ? "text-slate-300 cursor-not-allowed"
                : "text-slate-500 hover:text-slate-700"
            }`}
          >
            ← {t("global.previous")}
          </button>

          {/* Show first page if not in visible range */}
          {paginationButtons[0] > 1 && (
            <>
              <button
                onClick={() =>
                  updateUrlParams({ page: 1 }, searchParams, setSearchParams)
                }
                className="px-3 py-2 rounded text-slate-500 hover:text-slate-700 text-sm transition-colors"
              >
                1
              </button>
              {paginationButtons[0] > 2 && (
                <span className="px-3 py-2 text-slate-400 text-sm">...</span>
              )}
            </>
          )}

          {/* Page buttons */}
          {paginationButtons.map((page) => (
            <button
              key={page}
              onClick={() =>
                updateUrlParams({ page }, searchParams, setSearchParams)
              }
              className={`px-3 py-2 rounded text-sm transition-colors ${
                page === currentPage
                  ? "bg-slate-200 text-slate-900"
                  : "text-slate-500 hover:text-slate-700"
              }`}
            >
              {page}
            </button>
          ))}

          {/* Show last page if not in visible range */}
          {paginationButtons[paginationButtons.length - 1] < totalPages && (
            <>
              {paginationButtons[paginationButtons.length - 1] <
                totalPages - 1 && (
                <span className="px-3 py-2 text-slate-400 text-sm">...</span>
              )}
              <button
                onClick={() =>
                  updateUrlParams(
                    {
                      page: totalPages,
                    },
                    searchParams,
                    setSearchParams
                  )
                }
                className="px-3 py-2 rounded text-slate-500 hover:text-slate-700 text-sm transition-colors"
              >
                {totalPages}
              </button>
            </>
          )}

          <button
            onClick={() =>
              updateUrlParams(
                { page: currentPage + 1 },
                searchParams,
                setSearchParams
              )
            }
            disabled={currentPage === totalPages}
            className={`px-3 py-2 text-sm transition-colors ${
              currentPage === totalPages
                ? "text-slate-300 cursor-not-allowed"
                : "text-slate-500 hover:text-slate-700"
            }`}
          >
            {t("global.next")} →
          </button>
        </div>
      )}
      <DeleteSectionModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setCompanyToDelete(null);
        }}
        onConfirm={() => {
          if (companyToDelete) {
            deleteMutation.mutate(companyToDelete.id);
          }
        }}
        labelToDelete={t("companyTable.deleteLabel")}
        isPending={deleteMutation.isPending}
      />
    </div>
  );
}
