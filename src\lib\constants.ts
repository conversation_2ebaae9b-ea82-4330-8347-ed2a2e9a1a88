import { FilterConfigRecord, Roles } from "./types";

export const companyFilterConfigs: FilterConfigRecord = {
  name: { field: "name", type: "string", label: "Company Name" },
  business_type: {
    field: "business_type",
    type: "string",
    label: "Business Type",
  },
  number_of_branches: {
    field: "number_of_branches",
    type: "number",
    label: "Number of Branches",
  },
  store_admin_email: {
    field: "store_admin_email",
    type: "string",
    label: "Store Admin Email",
  },
  created_at: { field: "created_at", type: "date", label: "Created Date" },
  status: {
    field: "status",
    type: "enum",
    label: "Status",
    options: ["active", "inactive", "archived"],
  },
};
export const branchesFilterConfigs: FilterConfigRecord = {
  store_admin_email: {
    field: "store_admin_email",
    type: "string",
    label: "Branch Admin Email",
  },
  created_at: { field: "created_at", type: "date", label: "Created Date" },
  status: {
    field: "status",
    type: "enum",
    label: "Status",
    options: ["active", "inactive", "archived"],
  },
};

export const DEFAULT_USER_SETTINGS = {
  logoId: "",
  primaryColor: "#000000",
  secondaryColor: "#FFFFFF",
  loginMessage: "Welcome to Y-Verify",
  language: "en",
  dateFormat: "MM/DD/YYYY",
  timeZone: "UTC",
  firstDayOfWeek: "Monday",
  defaultChecklistFrequency: "weekly",
  requireEvidenceByDefault: true,
  allowEditAfterSubmission: false,
  autoArchiveOldChecklists: true,
  archiveThresholdDays: 365,
};

export const MEDIA_BUCKET = "media-bucket";
export const roleLabels: Partial<Record<Roles, string>> = {
  ADMIN: "Administrator",
  STORE_MANAGER: "Store Manager",
  BRANCH_MANAGER: "Branch Manager",
  AUDITOR: "Auditor",
  CHECKER: "Checker",
  MAINTAINER: "Maintainer",
  VIEWER: "Viewer",
};
export const roles = [
  "ADMIN",
  "STORE_MANAGER",
  "BRANCH_MANAGER",
  "AUDITOR",
  "CHECKER",
  "MAINTAINER",
  "VIEWER",
] as const;
