import React from "react";
import { But<PERSON> } from "react-day-picker";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

const ResponseSuccessful = () => {
  const { t } = useTranslation();

  return (
    <div className="flex justify-center items-center bg-[#f4f6f8] m-5 rounded-2xl w-full min-h-[88vh]">
      <div className="flex flex-col items-center gap-7">
        {/* Image */}
        <div className="flex justify-center items-center w-[19.375rem]">
          <img
            src="/assets/images/submit-successful.png"
            alt={t("responseSuccessful.altText")}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex flex-col items-center gap-[1.88rem]">
          <span className="font-bold text-[1.75rem] text-black">
            {t("responseSuccessful.successMessage")}
          </span>
          <Link to="/assignment-response/history">
            <button className="flex justify-center items-center bg-[#2d3d55] px-[1.375rem] py-[0.625rem] rounded-[2.5rem] w-[9.375rem] font-bold text-white">
              {t("responseSuccessful.historyButton")}
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ResponseSuccessful;
