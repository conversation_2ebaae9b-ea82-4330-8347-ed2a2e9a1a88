import { DeleteSectionModal } from "@/components/DeleteSectionModal";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { Edit, Plus, Trash2, Star } from "lucide-react";
import { useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import * as z from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { isUUID } from "@/lib/utils";
import { updateAssignment } from "@/data/assignments";
import { Assignment, UpdateChecklistHierarchyInput } from "@/lib/types";
import { useFieldDefaults } from "@/hooks/useFieldDefaults";

// Enhanced Zod schemas with preferred answer
const assignmentFieldSchema = z.object({
  id: z.string(),
  questionText: z.string().min(1, "Question text is required"),
  questionType: z.enum(["Text", "YES/NO"]),
  options: z.array(z.string()).optional(),
  required: z.boolean(),
  addHint: z.string().optional(),
  requireEvidence: z.boolean(),
  scoreWeight: z
    .number()
    .min(1, "Score weight must be at least 1")
    .max(10, "Score weight cannot exceed 10"),
  placeholder: z.string().optional(),
  order: z.number().min(1, "Order must be a positive integer"),
  preferredAnswer: z.string().optional(),
  hasPreferredAnswer: z.boolean().default(false),
  templateFieldId: z.string().optional(),
});

const assignmentSectionSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Section name is required"),
  order: z.number().min(1, "Order must be a positive integer"),
  fields: z
    .array(assignmentFieldSchema)
    .min(1, "At least one field is required"),
});

const assignmentFormSchema = z.object({
  title: z.string().min(3, "Assignment name is required"),
  businessType: z.string().min(1, "Industry type is required"),
  passRate: z
    .string()
    .regex(
      /^\d+(\.\d{1,2})?$/,
      "Pass rate must be a number with up to two decimal places"
    ),
  sections: z
    .array(assignmentSectionSchema)
    .min(1, "At least one section is required"),
});

export type AssignmentFormData = z.infer<typeof assignmentFormSchema>;

interface AssignmentFormProps {
  defaultValues?: Partial<Assignment>;
  assignmentId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const EditAssignmentForm = ({
  defaultValues,
  assignmentId,
  onSuccess,
  onCancel,
}: AssignmentFormProps) => {
  const queryClient = useQueryClient();
  const { getNewFieldDefaults } = useFieldDefaults();

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState<{
    type: "section" | "field";
    sectionIndex: number;
    fieldIndex?: number;
  } | null>(null);

  const form = useForm<AssignmentFormData>({
    resolver: zodResolver(assignmentFormSchema),
    defaultValues: {
      ...defaultValues,
    },
  });

  const {
    fields: sections,
    append: appendSection,
    remove: removeSection,
  } = useFieldArray({
    control: form.control,
    name: "sections",
  });

  const handleDeleteSection = (sectionIndex: number) => {
    setDeleteTarget({ type: "section", sectionIndex });
    setShowDeleteModal(true);
  };

  const handleDeleteField = (sectionIndex: number, fieldIndex: number) => {
    setDeleteTarget({ type: "field", sectionIndex, fieldIndex });
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (deleteTarget) {
      if (deleteTarget.type === "section") {
        removeSection(deleteTarget.sectionIndex);
      } else if (
        deleteTarget.type === "field" &&
        deleteTarget.fieldIndex !== undefined
      ) {
        const currentSection = form.getValues(
          `sections.${deleteTarget.sectionIndex}`
        );
        const updatedFields = currentSection.fields.filter(
          (_, index) => index !== deleteTarget.fieldIndex
        );
        form.setValue(
          `sections.${deleteTarget.sectionIndex}.fields`,
          updatedFields
        );
      }
      setShowDeleteModal(false);
      setDeleteTarget(null);
    }
  };

  const addSection = () => {
    const newSection = {
      id: Date.now().toString(),
      name: `Section ${sections.length + 1}`,
      order: sections.length + 1,
      fields: [
        {
          id: Date.now().toString(),
          ...getNewFieldDefaults(),
          questionText: "Lorem ipsum dummy text",
          addHint: "Tooltip appear in checklist",
          order: 1,
        },
      ],
    };
    appendSection(newSection);
  };

  const addField = (sectionIndex: number) => {
    const currentSection = form.getValues(`sections.${sectionIndex}`);
    const newField = {
      id: Date.now().toString(),
      ...getNewFieldDefaults(),
      questionText: "New question",
      order: currentSection.fields.length + 1,
    };

    const updatedFields = [...currentSection.fields, newField];
    form.setValue(`sections.${sectionIndex}.fields`, updatedFields);
  };

  // Function to toggle preferred answer
  const togglePreferredAnswer = (sectionIndex: number, fieldIndex: number) => {
    const currentValue = form.getValues(
      `sections.${sectionIndex}.fields.${fieldIndex}.hasPreferredAnswer`
    );
    form.setValue(
      `sections.${sectionIndex}.fields.${fieldIndex}.hasPreferredAnswer`,
      !currentValue
    );

    // Clear preferred answer if disabling
    if (currentValue) {
      form.setValue(
        `sections.${sectionIndex}.fields.${fieldIndex}.preferredAnswer`,
        ""
      );
    }
  };

  // Mutations
  const updateAssignmentMutation = useMutation({
    mutationFn: (assignmentData: UpdateChecklistHierarchyInput) => {
      if (!assignmentId)
        throw new Error("Assignment ID is required for update");
      return updateAssignment(assignmentId, assignmentData);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["assignments"] });
      queryClient.invalidateQueries({ queryKey: ["assignment", assignmentId] });
      toast.success("Assignment updated successfully!");
      onSuccess?.();
    },
    onError: (error) => {
      toast.error("Failed to update assignment: " + error);
      console.error("Error updating assignment:", error);
    },
  });

  const onSubmit = (data: AssignmentFormData) => {
    // Format data according to UpdateChecklistHierarchyInput interface
    const assignmentData: UpdateChecklistHierarchyInput = {
      assignment: {
        templateId: defaultValues?.templateId || "",
        branchId: defaultValues?.branchId || "",
        assignedTo: defaultValues?.assignedTo,
        assignedBy: defaultValues?.assignedBy || "",
        dueDate: defaultValues?.dueDate,
        startDate: defaultValues?.startDate,
        status: defaultValues?.status as
          | "Active"
          | "Scheduled"
          | "Expired"
          | undefined,
        frequency: defaultValues?.frequency as
          | "daily"
          | "weekly"
          | "monthly"
          | "yearly"
          | undefined,
        notes: defaultValues?.notes,
        passRate: data.passRate,
      },
      sections: data.sections.map((section) => ({
        id: isUUID(section.id) ? section.id : undefined,
        name: section.name,
        order: section.order,
        fields: section.fields.map((field) => ({
          id: isUUID(field.id) ? field.id : undefined,
          question: field.questionText,
          questionType: field.questionType,
          required: field.required,
          requiresEvidence: field.requireEvidence,
          score: field.scoreWeight.toString(),
          hint: field.addHint,
          order: field.order,
          preferredAnswer: field.hasPreferredAnswer
            ? field.preferredAnswer
            : undefined,
          templateFieldId: field.templateFieldId,
        })),
      })),
    };

    updateAssignmentMutation.mutate(assignmentData);
  };

  const isLoading = updateAssignmentMutation.isPending;

  return (
    <div className="space-y-6 p-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Assignment Info */}
          <Card className="mb-6 p-6">
            <div className="flex flex-row *:flex-1 gap-6 w-full">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assignment name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="businessType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Industry type</FormLabel>
                    <Input {...field} />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </Card>

          {/* Sections */}
          <div className="space-y-6">
            {sections.map((section, sectionIndex) => (
              <Card key={section.id} className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <FormField
                    control={form.control}
                    name={`sections.${sectionIndex}.name`}
                    render={({ field }) => (
                      <FormItem className="flex-1 mr-4">
                        <FormControl>
                          <Input
                            {...field}
                            className="font-medium text-gray-900 text-lg"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" type="button">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      type="button"
                      onClick={() => handleDeleteSection(sectionIndex)}
                    >
                      <Trash2 className="w-4 h-4 text-red-500" />
                    </Button>
                  </div>
                </div>

                {/* Fields within this section */}
                <div className="space-y-4">
                  {form
                    .watch(`sections.${sectionIndex}.fields`)
                    ?.map((field, fieldIndex) => (
                      <Card
                        key={field.id}
                        className="p-4 border-l-4 border-l-blue-500"
                      >
                        <div className="flex justify-between items-center mb-4">
                          <h4 className="font-medium text-gray-800">
                            Field {fieldIndex + 1}
                          </h4>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              type="button"
                              onClick={() =>
                                togglePreferredAnswer(sectionIndex, fieldIndex)
                              }
                              className={`${
                                form.watch(
                                  `sections.${sectionIndex}.fields.${fieldIndex}.hasPreferredAnswer`
                                )
                                  ? "text-amber-600 hover:text-amber-700"
                                  : "text-gray-400 hover:text-gray-600"
                              }`}
                            >
                              <Star
                                className={`w-4 h-4 ${
                                  form.watch(
                                    `sections.${sectionIndex}.fields.${fieldIndex}.hasPreferredAnswer`
                                  )
                                    ? "fill-amber-400"
                                    : ""
                                }`}
                              />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              type="button"
                              onClick={() =>
                                handleDeleteField(sectionIndex, fieldIndex)
                              }
                            >
                              <Trash2 className="w-4 h-4 text-red-500" />
                            </Button>
                          </div>
                        </div>

                        <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
                          <FormField
                            control={form.control}
                            name={`sections.${sectionIndex}.fields.${fieldIndex}.questionText`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Question text</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`sections.${sectionIndex}.fields.${fieldIndex}.questionType`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Question Type</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="Text">Text</SelectItem>
                                    <SelectItem value="YES/NO">
                                      Yes/No
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {form.watch(
                            `sections.${sectionIndex}.fields.${fieldIndex}.questionType`
                          ) === "Text" && (
                            <div className="md:col-span-2">
                              <FormField
                                control={form.control}
                                name={`sections.${sectionIndex}.fields.${fieldIndex}.placeholder`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Placeholder</FormLabel>
                                    <FormControl>
                                      <Textarea {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          )}

                          {/* Preferred Answer Section */}
                          {form.watch(
                            `sections.${sectionIndex}.fields.${fieldIndex}.hasPreferredAnswer`
                          ) && (
                            <div className="md:col-span-2">
                              <FormField
                                control={form.control}
                                name={`sections.${sectionIndex}.fields.${fieldIndex}.preferredAnswer`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="flex items-center gap-2">
                                      <Star className="fill-amber-400 w-4 h-4 text-amber-600" />
                                      Preferred Answer
                                    </FormLabel>
                                    <FormControl>
                                      {form.watch(
                                        `sections.${sectionIndex}.fields.${fieldIndex}.questionType`
                                      ) === "YES/NO" ? (
                                        <Select
                                          onValueChange={field.onChange}
                                          defaultValue={field.value}
                                        >
                                          <SelectTrigger>
                                            <SelectValue placeholder="Select preferred answer" />
                                          </SelectTrigger>
                                          <SelectContent>
                                            <SelectItem value="YES">
                                              Yes
                                            </SelectItem>
                                            <SelectItem value="NO">
                                              No
                                            </SelectItem>
                                          </SelectContent>
                                        </Select>
                                      ) : (
                                        <Textarea
                                          {...field}
                                          placeholder="Enter the preferred answer for this question"
                                        />
                                      )}
                                    </FormControl>
                                    <FormDescription>
                                      This answer will be used as a reference
                                      for scoring or evaluation purposes.
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          )}

                          <FormField
                            control={form.control}
                            name={`sections.${sectionIndex}.fields.${fieldIndex}.addHint`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Add hint (Optional)</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`sections.${sectionIndex}.fields.${fieldIndex}.scoreWeight`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Score weight</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    {...field}
                                    onChange={(e) =>
                                      field.onChange(Number(e.target.value))
                                    }
                                  />
                                </FormControl>
                                <FormDescription>
                                  Enter a value between 1 and 10
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="space-y-4 md:col-span-2">
                            <FormField
                              control={form.control}
                              name={`sections.${sectionIndex}.fields.${fieldIndex}.required`}
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                                  <FormControl>
                                    <Checkbox
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                  <FormLabel className="text-sm">
                                    Required
                                  </FormLabel>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name={`sections.${sectionIndex}.fields.${fieldIndex}.requireEvidence`}
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                                  <FormControl>
                                    <Checkbox
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                  <FormLabel className="text-sm">
                                    Require evidence
                                  </FormLabel>
                                </FormItem>
                              )}
                            />

                            {form.watch(
                              `sections.${sectionIndex}.fields.${fieldIndex}.requireEvidence`
                            ) && (
                              <div className="p-3 border rounded-lg">
                                <div className="flex items-center space-x-2 text-gray-600 text-sm">
                                  <span>📷</span>
                                  <span>Attach image</span>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </Card>
                    ))}

                  {/* Add Field Button */}
                  <Button
                    variant="outline"
                    onClick={() => addField(sectionIndex)}
                    type="button"
                    className="border-2 border-blue-300 hover:border-blue-400 border-dashed w-full h-10"
                  >
                    <Plus className="mr-2 w-4 h-4" />
                    Add field
                  </Button>
                </div>
              </Card>
            ))}

            {/* Add Section Button */}
            <Button
              variant="outline"
              onClick={addSection}
              type="button"
              className="border-2 border-gray-300 hover:border-gray-400 border-dashed w-full h-12"
            >
              <Plus className="mr-2 w-4 h-4" />
              Add section
            </Button>
          </div>
          <FormField
            control={form.control}
            name={"passRate"}
            render={({ field }) => (
              <FormItem className="flex-1 mr-4">
                <label htmlFor="passRate">Pass rate</label>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    min={0}
                    max={100}
                    step={0.01}
                    placeholder="Enter pass rate percentage (0-100)"
                    className="font-medium text-gray-900 text-lg"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Footer Buttons */}
          <div className="flex justify-end items-center space-x-4 mt-8">
            <Button variant="outline" type="button" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-slate-700 hover:bg-slate-800"
              disabled={isLoading}
            >
              {isLoading ? "Updating..." : "Update"}
            </Button>
          </div>
        </form>
      </Form>

      {/* Delete Modal */}
      <DeleteSectionModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={confirmDelete}
      />
    </div>
  );
};

export default EditAssignmentForm;
