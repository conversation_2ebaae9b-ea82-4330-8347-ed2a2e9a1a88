import React, { useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { verifyEmailToken } from "@/data/verification";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";

const EmailVerification: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");

  const {
    data: verificationResult,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["verifyEmail", token],
    queryFn: () => {
      if (!token) {
        throw new Error("No verification token provided");
      }
      return verifyEmailToken(token);
    },
    enabled: !!token,
    retry: false, // Don't retry verification requests
  });

  // Redirect to dashboard after successful verification
  useEffect(() => {
    if (verificationResult?.success) {
      const timer = setTimeout(() => {
        navigate("/", { replace: true });
      }, 3000); // Redirect after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [verificationResult, navigate]);

  if (!token) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-secondary-bg shadow-lg p-8 rounded-lg w-full max-w-md text-center">
          <div className="flex justify-center items-center bg-red-100 mx-auto mb-4 rounded-full w-16 h-16">
            <XCircle className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="mb-2 font-bold text-gray-900 text-2xl">
            {t("emailVerification.invalidLink")}
          </h1>
          <p className="mb-6 text-gray-600">
            {t("emailVerification.noTokenProvided")}
          </p>
          <Button onClick={() => navigate("/signin")} className="w-full">
            {t("emailVerification.backToLogin")}
          </Button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-secondary-bg shadow-lg p-8 rounded-lg w-full max-w-md text-center">
          <div className="flex justify-center items-center bg-blue-100 mx-auto mb-4 rounded-full w-16 h-16">
            <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
          </div>
          <h1 className="mb-2 font-bold text-gray-900 text-2xl">
            {t("emailVerification.verifying")}
          </h1>
          <p className="text-gray-600">
            {t("emailVerification.verifyingDescription")}
          </p>
        </div>
      </div>
    );
  }

  if (isError || !verificationResult?.success) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-secondary-bg shadow-lg p-8 rounded-lg w-full max-w-md text-center">
          <div className="flex justify-center items-center bg-red-100 mx-auto mb-4 rounded-full w-16 h-16">
            <XCircle className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="mb-2 font-bold text-gray-900 text-2xl">
            {t("emailVerification.verificationFailed")}
          </h1>
          <p className="mb-6 text-gray-600">
            {error?.message ||
              verificationResult?.message ||
              t("emailVerification.verificationFailedDescription")}
          </p>
          <div className="space-y-3">
            <Button
              onClick={() => navigate("/email-verification-pending")}
              className="w-full"
            >
              {t("emailVerification.requestNewLink")}
            </Button>
            <Button
              onClick={() => navigate("/signin")}
              variant="outline"
              className="w-full"
            >
              {t("emailVerification.backToLogin")}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Success state
  return (
    <div className="flex justify-center items-center min-h-screen">
      <div className="bg-secondary-bg shadow-lg p-8 rounded-lg w-full max-w-md text-center">
        <div className="flex justify-center items-center bg-green-100 mx-auto mb-4 rounded-full w-16 h-16">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <h1 className="mb-2 font-bold text-gray-900 text-2xl">
          {t("emailVerification.verificationSuccess")}
        </h1>
        <p className="mb-6 text-gray-600">
          {t("emailVerification.verificationSuccessDescription")}
        </p>
        <p className="mb-4 text-gray-500 text-sm">
          {t("emailVerification.redirectingToDashboard")}
        </p>
        <Button onClick={() => navigate("/")} className="w-full">
          {t("emailVerification.goToDashboard")}
        </Button>
      </div>
    </div>
  );
};

export default EmailVerification;
