import {
  format,
  formatInTimeZone,
  toZonedTime,
  fromZonedTime,
} from "date-fns-tz";
import { parseISO } from "date-fns";

// Common timezone mappings for the Middle East region and others
export const TIMEZONE_OPTIONS = [
  { value: "Asia/Riyadh", label: "Saudi Arabia (GMT+3)" },
  { value: "Asia/Dubai", label: "UAE (GMT+4)" },
  { value: "Africa/Cairo", label: "Egypt (GMT+2)" },
  { value: "Asia/Kuwait", label: "Kuwait (GMT+3)" },
  { value: "Asia/Qatar", label: "Qatar (GMT+3)" },
  { value: "Asia/Bahrain", label: "Bahrain (GMT+3)" },
  { value: "Asia/Baghdad", label: "Iraq (GMT+3)" },
  { value: "Asia/Beirut", label: "Lebanon (GMT+2/3)" },
  { value: "Asia/Damascus", label: "Syria (GMT+2/3)" },
  { value: "Asia/Amman", label: "Jordan (GMT+2/3)" },
  { value: "Europe/London", label: "London (GMT+0/1)" },
  { value: "Europe/Paris", label: "Paris (GMT+1/2)" },
  { value: "Europe/Berlin", label: "Berlin (GMT+1/2)" },
  { value: "America/New_York", label: "New York (GMT-5/-4)" },
  { value: "America/Los_Angeles", label: "Los Angeles (GMT-8/-7)" },
  { value: "Asia/Tokyo", label: "Tokyo (GMT+9)" },
  { value: "Asia/Shanghai", label: "Shanghai (GMT+8)" },
  { value: "Australia/Sydney", label: "Sydney (GMT+10/11)" },
  { value: "UTC", label: "UTC (GMT+0)" },
] as const;

// Map old timezone values to new IANA timezone identifiers
export const TIMEZONE_MIGRATION_MAP = {
  "saudi-arabia": "Asia/Riyadh",
  uae: "Asia/Dubai",
  egypt: "Africa/Cairo",
} as const;

// Get user's browser timezone as fallback
export const getUserBrowserTimezone = (): string => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.warn("Could not detect user timezone, falling back to UTC");
    return "UTC";
  }
};

// Convert legacy timezone values to IANA timezone identifiers
export const normalizeTimezone = (timezone: string): string => {
  // If it's already a valid IANA timezone, return as-is
  if (timezone.includes("/") || timezone === "UTC") {
    return timezone;
  }

  // Check if it's a legacy value that needs migration
  const mapped =
    TIMEZONE_MIGRATION_MAP[timezone as keyof typeof TIMEZONE_MIGRATION_MAP];
  if (mapped) {
    return mapped;
  }

  // Fallback to user's browser timezone or UTC
  return getUserBrowserTimezone();
};

// Format a date with timezone support
export const formatDateWithTimezone = (
  date: Date | string,
  dateFormat: string = "MM/dd/yyyy",
  timezone: string = "UTC"
): string => {
  if (!date) return "";

  try {
    const normalizedTimezone = normalizeTimezone(timezone);
    const dateObj = typeof date === "string" ? parseISO(date) : date;

    return formatInTimeZone(dateObj, normalizedTimezone, dateFormat);
  } catch (error) {
    console.error("Error formatting date with timezone:", error);
    // Fallback to basic formatting
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return format(dateObj, dateFormat);
  }
};

// Format a date with time in timezone
export const formatDateTimeWithTimezone = (
  date: Date | string,
  dateFormat: string = "MM/dd/yyyy HH:mm",
  timezone: string = "UTC"
): string => {
  if (!date) return "";

  try {
    const normalizedTimezone = normalizeTimezone(timezone);
    const dateObj = typeof date === "string" ? parseISO(date) : date;

    return formatInTimeZone(dateObj, normalizedTimezone, dateFormat);
  } catch (error) {
    console.error("Error formatting datetime with timezone:", error);
    // Fallback to basic formatting
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return format(dateObj, dateFormat + " HH:mm");
  }
};

// Convert a date from user's timezone to UTC for storage
export const convertToUTC = (date: Date, timezone: string): Date => {
  try {
    const normalizedTimezone = normalizeTimezone(timezone);
    return fromZonedTime(date, normalizedTimezone);
  } catch (error) {
    console.error("Error converting to UTC:", error);
    return date;
  }
};

// Convert a UTC date to user's timezone for display
export const convertFromUTC = (
  utcDate: Date | string,
  timezone: string
): Date => {
  try {
    const normalizedTimezone = normalizeTimezone(timezone);
    const dateObj = typeof utcDate === "string" ? parseISO(utcDate) : utcDate;
    return toZonedTime(dateObj, normalizedTimezone);
  } catch (error) {
    console.error("Error converting from UTC:", error);
    const dateObj = typeof utcDate === "string" ? new Date(utcDate) : utcDate;
    return dateObj;
  }
};

// Get timezone offset string (e.g., "+03:00", "-05:00")
export const getTimezoneOffset = (
  timezone: string,
  date: Date = new Date()
): string => {
  try {
    const normalizedTimezone = normalizeTimezone(timezone);
    const formatted = formatInTimeZone(date, normalizedTimezone, "xxx");
    return formatted;
  } catch (error) {
    console.error("Error getting timezone offset:", error);
    return "+00:00";
  }
};

// Get timezone display name (e.g., "Eastern Standard Time")
export const getTimezoneDisplayName = (
  timezone: string,
  locale: string = "en"
): string => {
  try {
    const normalizedTimezone = normalizeTimezone(timezone);
    return (
      new Intl.DateTimeFormat(locale, {
        timeZoneName: "long",
        timeZone: normalizedTimezone,
      })
        .formatToParts(new Date())
        .find((part) => part.type === "timeZoneName")?.value || timezone
    );
  } catch (error) {
    console.error("Error getting timezone display name:", error);
    return timezone;
  }
};

// Validate if a timezone is valid
export const isValidTimezone = (timezone: string): boolean => {
  try {
    const normalizedTimezone = normalizeTimezone(timezone);
    Intl.DateTimeFormat(undefined, { timeZone: normalizedTimezone });
    return true;
  } catch (error) {
    return false;
  }
};
