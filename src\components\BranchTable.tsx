import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { deleteBranch, fetchBranches } from "@/data/branches";
import { canUserAccess } from "@/data/permissions";
import { branchesFilterConfigs } from "@/lib/constants";
import { Filter } from "@/lib/types";
import {
  addFilter,
  generatePaginationButtons,
  parseFiltersFromUrl,
  removeFilter,
  updateFilter,
  updateUrlParams,
} from "@/lib/utils";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Edit2, Plus, Trash, MapPin } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { DeleteSectionModal } from "./DeleteSectionModal";
import FilterRow from "./FilterRow";
import { Skeleton } from "./ui/skeleton";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { MapViewDialog } from "./MapViewDialog";

export const ITEMS_PER_PAGE = 10;

export function BranchTable({ storeId }: { storeId: string }) {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState<Filter[]>(
    parseFiltersFromUrl(searchParams)
  );
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [branchToDelete, setBranchToDelete] = useState(null);
  const [mapDialogOpen, setMapDialogOpen] = useState(false);
  const [selectedBranch, setSelectedBranch] = useState<{
    storeName: string;
    latitude: number;
    longitude: number;
  } | null>(null);
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Update URL when filters change
  useEffect(() => {
    const newParams = new URLSearchParams(searchParams);

    if (filters.length > 0) {
      newParams.set("filters", encodeURIComponent(JSON.stringify(filters)));
    } else {
      newParams.delete("filters");
    }

    // Reset to first page when filters change
    newParams.delete("page");

    setSearchParams(newParams);
  }, [filters, searchParams, setSearchParams]);

  const deleteMutation = useMutation({
    mutationFn: (branchId: string) => deleteBranch(branchId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["branches"] });
    },
    onError: (error) => {
      console.error("Delete failed:", error);
      toast.error("Failed to delete branch: " + error);
    },
  });

  const {
    data: branchesResult,
    isLoading: branchesLoading,
    error: branchesError,
  } = useQuery({
    queryKey: ["branches", filters, currentPage, storeId],
    queryFn: () => fetchBranches(filters, currentPage, storeId),
    staleTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: 1000,
  });

  // Check if user can edit/delete branches
  const { data: canEditBranches = false } = useQuery({
    queryKey: ["user-can-access", "crud_branches"],
    queryFn: () => canUserAccess("crud_branches"),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const branches = branchesResult?.data || [];
  const totalPages = branchesResult?.totalPages || 0;
  const total = branchesResult?.total || 0;

  if (branchesError) {
    console.error(branchesError);
  }

  const paginationButtons = generatePaginationButtons({
    currentPage,
    totalPages,
  });

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="font-medium text-slate-900 text-lg">
            {t("branchManagement.filtersTitle")}
          </h3>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={() => addFilter(filters, setFilters)}
                  variant="outline"
                  size="sm"
                >
                  <Plus className="mr-2 w-4 h-4" />
                  {t("branchManagement.addFilter")}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{t("common.addNewFilter")}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {filters.length > 0 && (
          <div className="space-y-3">
            {filters.map((filter) => (
              <FilterRow
                key={filter.id}
                filter={filter}
                onUpdate={(newFilter) =>
                  updateFilter(newFilter, filters, setFilters)
                }
                onRemove={(id) => removeFilter(id, filters, setFilters)}
                filterConfig={branchesFilterConfigs}
              />
            ))}
          </div>
        )}

        {filters.length === 0 && (
          <div className="py-8 text-slate-500 text-center">
            <p>{t("branchManagement.noFiltersApplied")}</p>
          </div>
        )}
      </div>

      {/* Results info */}
      <div className="flex justify-between items-center">
        <div className="text-slate-600 text-sm">
          {t("global.showingToOfResults", {
            "0":
              branches.length > 0 ? (currentPage - 1) * ITEMS_PER_PAGE + 1 : 0,
            "1": Math.min(currentPage * ITEMS_PER_PAGE, total),
            "2": total,
          })}
        </div>
      </div>

      {/* Table */}
      <div className="border border-slate-200 rounded-lg overflow-hidden">
        <table className="w-full">
          <thead>
            <tr className="border-gray-200 border-b">
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchManagement.table.storeName")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchManagement.table.location")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchManagement.table.map")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchManagement.table.status")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchManagement.table.branchManager")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchManagement.table.action")}
              </th>
            </tr>
          </thead>
          <tbody>
            {branchesLoading ? (
              // Better loading state with multiple skeleton rows
              Array.from({ length: 3 }).map((_, index) => (
                <tr key={index} className="border-gray-100 border-b">
                  <td className="px-4 py-3">
                    <Skeleton className="w-24 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-32 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-20 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-28 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-28 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-16 h-4" />
                  </td>
                </tr>
              ))
            ) : branchesError ? (
              <tr>
                <td colSpan={6} className="px-4 py-8 text-red-600 text-center">
                  {t("branchManagement.table.errorLoading")}:{" "}
                  {String(branchesError)}
                </td>
              </tr>
            ) : branches.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-4 py-8 text-gray-500 text-center">
                  {t("branchManagement.table.noBranches")}
                </td>
              </tr>
            ) : (
              branches.map((branch, index) => (
                <tr key={index} className="border-gray-100 border-b hover:">
                  <td className="px-4 py-3 text-gray-900 text-sm">
                    {branch.storeName || t("common.notAvailable")}
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    {branch.location}
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    {branch.latitude && branch.longitude ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          console.log("Branch coordinates:", {
                            lat: branch.latitude,
                            lng: branch.longitude,
                            latType: typeof branch.latitude,
                            lngType: typeof branch.longitude,
                          });
                          setSelectedBranch({
                            storeName: branch.storeName,
                            latitude: parseFloat(branch.latitude.toString()),
                            longitude: parseFloat(branch.longitude.toString()),
                          });
                          setMapDialogOpen(true);
                        }}
                        className="flex items-center gap-1"
                      >
                        <MapPin className="w-4 h-4" />
                        {t("branchManagement.showOnMap")}
                      </Button>
                    ) : (
                      <span className="text-gray-400">
                        {t("common.notAvailable")}
                      </span>
                    )}
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={branch.status === "active"}
                        disabled
                        className="data-[state=checked]:bg-slate-800"
                      />
                      <span className="text-slate-600 text-sm">
                        {branch.status === "active"
                          ? t("status.active")
                          : t("status.inactive")}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    {branch.managerEmail || t("common.notAvailable")}
                  </td>
                  <td className="flex flex-row items-center gap-4 px-4 py-3 text-gray-700 text-sm">
                    {canEditBranches && (
                      <>
                        <Link to={`/companies/branches/edit/${branch.id}`}>
                          <Button variant="ghost" size="sm" className="p-0">
                            <Edit2 className="w-4 h-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-0"
                          onClick={() => {
                            setBranchToDelete(branch);
                            setDeleteModalOpen(true);
                          }}
                        >
                          <Trash className="w-4 h-4" />
                        </Button>
                      </>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2">
          <button
            onClick={() =>
              updateUrlParams(
                {
                  page: currentPage - 1,
                },
                searchParams,
                setSearchParams
              )
            }
            disabled={currentPage === 1}
            className={`px-3 py-2 text-sm transition-colors ${
              currentPage === 1
                ? "text-slate-300 cursor-not-allowed"
                : "text-slate-500 hover:text-slate-700"
            }`}
          >
            {t("pagination.back")}
          </button>

          {/* Show first page if not in visible range */}
          {paginationButtons[0] > 1 && (
            <>
              <button
                onClick={() =>
                  updateUrlParams({ page: 1 }, searchParams, setSearchParams)
                }
                className="px-3 py-2 rounded text-slate-500 hover:text-slate-700 text-sm transition-colors"
              >
                1
              </button>
              {paginationButtons[0] > 2 && (
                <span className="px-3 py-2 text-slate-400 text-sm">...</span>
              )}
            </>
          )}

          {/* Page buttons */}
          {paginationButtons.map((page) => (
            <button
              key={page}
              onClick={() =>
                updateUrlParams({ page }, searchParams, setSearchParams)
              }
              className={`px-3 py-2 rounded text-sm transition-colors ${
                page === currentPage
                  ? "bg-slate-200 text-slate-900"
                  : "text-slate-500 hover:text-slate-700"
              }`}
            >
              {page}
            </button>
          ))}

          {/* Show last page if not in visible range */}
          {paginationButtons[paginationButtons.length - 1] < totalPages && (
            <>
              {paginationButtons[paginationButtons.length - 1] <
                totalPages - 1 && (
                <span className="px-3 py-2 text-slate-400 text-sm">...</span>
              )}
              <button
                onClick={() =>
                  updateUrlParams(
                    {
                      page: totalPages,
                    },
                    searchParams,
                    setSearchParams
                  )
                }
                className="px-3 py-2 rounded text-slate-500 hover:text-slate-700 text-sm transition-colors"
              >
                {totalPages}
              </button>
            </>
          )}

          <button
            onClick={() =>
              updateUrlParams(
                { page: currentPage + 1 },
                searchParams,
                setSearchParams
              )
            }
            disabled={currentPage === totalPages}
            className={`px-3 py-2 text-sm transition-colors ${
              currentPage === totalPages
                ? "text-slate-300 cursor-not-allowed"
                : "text-slate-500 hover:text-slate-700"
            }`}
          >
            {t("pagination.next")}
          </button>
        </div>
      )}
      <DeleteSectionModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setBranchToDelete(null);
        }}
        onConfirm={() => {
          if (branchToDelete) {
            deleteMutation.mutate(branchToDelete.id);
          }
        }}
        labelToDelete={t("branchManagement.labels.branch")}
        isPending={deleteMutation.isPending}
      />

      {/* Map View Dialog */}
      {selectedBranch && (
        <MapViewDialog
          isOpen={mapDialogOpen}
          onClose={() => {
            setMapDialogOpen(false);
            setSelectedBranch(null);
          }}
          latitude={selectedBranch.latitude}
          longitude={selectedBranch.longitude}
          title={selectedBranch.storeName}
        />
      )}
    </div>
  );
}
