import { fetchWithToken } from "@/lib/fetchWithToken";

export interface Notification {
  id: string;
  userId: string;
  title: string;
  body: string;
  type?: string | null;
  data?: string | null;
  channel: string;
  status: string;
  error?: string | null;
  isRead: boolean;
  sentAt: string;
  readAt?: string | null;
}

export interface NotificationsResponse {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  items: Notification[];
}

export interface GetNotificationsParams {
  userId: string;
  page?: number;
  pageSize?: number;
  isRead?: boolean;
}

export interface MarkAsReadParams {
  notificationId: string;
}

export interface SendNotificationParams {
  userId: string;
  title: string;
  body: string;
  type?: string;
  data?: Record<string, unknown>;
  channel?: string;
  url?: string;
}

/**
 * Fetch notifications for a specific user
 */
export const getUserNotifications = async ({
  userId,
  page = 1,
  pageSize = 20,
  isRead,
}: GetNotificationsParams): Promise<NotificationsResponse> => {
  const params = new URLSearchParams({
    userId,
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  if (typeof isRead === "boolean") {
    params.append("isRead", isRead.toString());
  }

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/notifications?${params}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      `Failed to fetch notifications: ${
        errorData.message || response.statusText
      }`
    );
  }

  const data = await response.json();
  return {
    page: data.page,
    pageSize: data.pageSize,
    total: data.total,
    totalPages: data.totalPages,
    items: data.items,
  };
};

/**
 * Mark a notification as read
 */
export const markNotificationAsRead = async ({
  notificationId,
}: MarkAsReadParams): Promise<void> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/notifications/${notificationId}/read`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      `Failed to mark notification as read: ${
        errorData.message || response.statusText
      }`
    );
  }
};

/**
 * Send a notification to a user
 */
export const sendNotification = async ({
  userId,
  title,
  body,
  type,
  data,
  channel = "push",
  url = "/notifications",
}: SendNotificationParams): Promise<{
  successCount: number;
  failureCount: number;
  status: string;
  error?: string;
}> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/notifications/send`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        userId,
        title,
        body,
        type,
        data,
        channel,
        url,
      }),
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      `Failed to send notification: ${errorData.message || response.statusText}`
    );
  }

  const result = await response.json();
  return result.data;
};

/**
 * Get unread notifications count for a user
 */
export const getUnreadNotificationsCount = async (
  userId: string
): Promise<number> => {
  const response = await getUserNotifications({
    userId,
    page: 1,
    pageSize: 1,
    isRead: false,
  });

  return response.total;
};
