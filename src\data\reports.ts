import { supabase } from "@/integrations/supabase/client";
import { fetchWithToken } from "@/lib/fetchWithToken";
import { Media } from "@/lib/types";

export interface IssueReportInput {
  title: string;
  description: string;
  branchId: string;
  mediaIds?: string[];
  email: string; // user email (not userId)
}

type IssueReportUpdateInput = Partial<IssueReportInput>;

interface ReportMedia {
  id: string;
  name: string | null;
  url: string | null;
  checklistResponseFieldsId: string | null;
  issueReportId: string | null;
  createdAt: Date;
  updatedAt: Date;
}

interface IssueReport {
  report: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    branchId: string;
    title: string;
    description: string;
    storeName?: string | null;
    branchLocation?: string | null;
    userName?: string | null;
    userEmail?: string | null;
    media: ReportMedia[];
    // Assignment information
    assignmentStatus?: "pending" | "in_progress" | "resolved" | "closed" | null;
    assignedTo?: string | null; // Maintainer name
    assignedToEmail?: string | null; // Maintainer email
    assignmentComments?: string | null;
    assignmentId?: string | null; // For updating assignment
  };
}

// Create a new Issue Report
export async function createIssueReport(
  data: IssueReportInput
): Promise<IssueReport> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/report-issue`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to create issue report");
  }

  const json = await response.json();
  return json.data.report;
}

// Update an existing Issue Report by ID
export async function updateIssueReport(
  reportId: string,
  data: IssueReportUpdateInput
): Promise<IssueReport> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/report-issue/${reportId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to update issue report");
  }

  const json = await response.json();
  return json.data;
}

// Get all issue reports with optional filters and pagination
interface FetchIssueReportsParams {
  email?: string; // Optional email filter (to get user-related reports)
  branchId?: string;
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  search?: string;
}

interface IssueReportsPaginated {
  reports: Array<IssueReport["report"]>;
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export async function fetchIssueReports(
  params: FetchIssueReportsParams = {}
): Promise<IssueReportsPaginated> {
  const queryParams = new URLSearchParams();

  if (params.page) queryParams.append("page", params.page.toString());
  if (params.limit) queryParams.append("limit", params.limit.toString());
  if (params.branchId) queryParams.append("branchId", params.branchId);
  if (params.search) queryParams.append("search", params.search);
  if (params.startDate) queryParams.append("startDate", params.startDate);
  if (params.endDate) queryParams.append("endDate", params.endDate);
  // Note: The backend gets userId for filtering; if you need to query by email, you must adapt backend accordingly.

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/report-issue?${queryParams.toString()}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to fetch issue reports");
  }

  const json = await response.json();

  console.log(json);

  return {
    reports: json.data,
    total: json.pagination.total,
    page: json.pagination.page,
    limit: json.pagination.limit,
    totalPages: json.pagination.totalPages,
  };
}

// Get issue report by ID
export async function fetchIssueReportById(
  reportId: string
): Promise<IssueReport> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/report-issue/${reportId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to fetch issue report");
  }

  const json = await response.json();
  return json.data;
}

// Delete issue report by ID
export async function deleteIssueReport(reportId: string): Promise<void> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/report-issue/${reportId}`,
    {
      method: "DELETE",
      headers: {},
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to delete issue report");
  }
}

// Get media for issue report
export async function fetchIssueReportMedia(reportId: string): Promise<Media> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/report-issue/media/${reportId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to get issue report media");
  }

  const json = await response.json();
  return json.data.media;
}
