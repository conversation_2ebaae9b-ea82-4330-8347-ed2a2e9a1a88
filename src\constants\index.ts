import { Country } from "@/integrations/supabase/types";

const unsortedCountries: Country[] = [
  // Arab countries
  { code: "AE", flag: "🇦🇪", name: "United Arab Emirates" },
  { code: "SA", flag: "🇸🇦", name: "Saudi Arabia" },
  { code: "EG", flag: "🇪🇬", name: "Egypt" },
  { code: "DZ", flag: "🇩🇿", name: "Algeria" },
  { code: "IQ", flag: "🇮🇶", name: "Iraq" },
  { code: "MA", flag: "🇲🇦", name: "Morocco" },
  { code: "SD", flag: "🇸🇩", name: "Sudan" },
  { code: "YE", flag: "🇾🇪", name: "Yemen" },
  { code: "SY", flag: "🇸🇾", name: "Syria" },
  { code: "TN", flag: "🇹🇳", name: "Tunisia" },
  { code: "JO", flag: "🇯🇴", name: "Jordan" },
  { code: "LY", flag: "🇱🇾", name: "Libya" },
  { code: "LB", flag: "🇱🇧", name: "Lebanon" },
  { code: "PS", flag: "🇵🇸", name: "Palestine" },
  { code: "KW", flag: "🇰🇼", name: "Kuwait" },
  { code: "OM", flag: "🇴🇲", name: "Oman" },
  { code: "QA", flag: "🇶🇦", name: "Qatar" },
  { code: "BH", flag: "🇧🇭", name: "Bahrain" },
  { code: "MR", flag: "🇲🇷", name: "Mauritania" },
  { code: "DJ", flag: "🇩🇯", name: "Djibouti" },
  { code: "SO", flag: "🇸🇴", name: "Somalia" },
  { code: "KM", flag: "🇰🇲", name: "Comoros" },

  // Major world countries (selected)
  { code: "US", flag: "🇺🇸", name: "United States" },
  { code: "GB", flag: "🇬🇧", name: "United Kingdom" },
  { code: "FR", flag: "🇫🇷", name: "France" },
  { code: "DE", flag: "🇩🇪", name: "Germany" },
  { code: "IT", flag: "🇮🇹", name: "Italy" },
  { code: "ES", flag: "🇪🇸", name: "Spain" },
  { code: "CN", flag: "🇨🇳", name: "China" },
  { code: "JP", flag: "🇯🇵", name: "Japan" },
  { code: "KR", flag: "🇰🇷", name: "South Korea" },
  { code: "IN", flag: "🇮🇳", name: "India" },
  { code: "BR", flag: "🇧🇷", name: "Brazil" },
  { code: "CA", flag: "🇨🇦", name: "Canada" },
  { code: "AU", flag: "🇦🇺", name: "Australia" },
  { code: "MX", flag: "🇲🇽", name: "Mexico" },
  { code: "AR", flag: "🇦🇷", name: "Argentina" },
  { code: "TR", flag: "🇹🇷", name: "Turkey" },
  { code: "ZA", flag: "🇿🇦", name: "South Africa" },
  { code: "NG", flag: "🇳🇬", name: "Nigeria" },
  { code: "PK", flag: "🇵🇰", name: "Pakistan" },
  { code: "RU", flag: "🇷🇺", name: "Russia" },
  { code: "SE", flag: "🇸🇪", name: "Sweden" },
  { code: "CH", flag: "🇨🇭", name: "Switzerland" },
  { code: "NL", flag: "🇳🇱", name: "Netherlands" },
  { code: "BE", flag: "🇧🇪", name: "Belgium" },
];

export const countries = unsortedCountries.sort((a, b) =>
  a.name.localeCompare(b.name)
);
