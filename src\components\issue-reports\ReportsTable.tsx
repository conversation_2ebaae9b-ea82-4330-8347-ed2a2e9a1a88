import React, { useState, useEffect } from "react";
import { Card } from "../ui/card";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  fetchIssueReports,
  fetchIssueReportMedia,
  deleteIssueReport,
} from "@/data/reports";
import { fetchIssueAssignments } from "@/data/issueAssignments";
import { Skeleton } from "../ui/skeleton";
import { useTranslation } from "react-i18next";
import { Button } from "../ui/button";
import {
  Edit,
  Eye,
  Trash2,
  UserPlus,
  CheckCircle,
  MessageCircle,
} from "lucide-react";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { DeleteSectionModal } from "../DeleteSectionModal";
import { Link } from "react-router-dom";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import AssignReportModal from "./AssignReportModal";
import MaintainerActionsModal from "./MaintainerActionsModal";

const ReportsTable = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  // State for user role
  const [userRole, setUserRole] = useState<string | null>(null);
  const [currentUserEmail, setCurrentUserEmail] = useState<string | null>(null);

  // Modal states
  const [selectedReport, setSelectedReport] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [reportToDelete, setReportToDelete] = useState(null);
  const [assignModalOpen, setAssignModalOpen] = useState(false);
  const [reportToAssign, setReportToAssign] = useState(null);
  const [maintainerModalOpen, setMaintainerModalOpen] = useState(false);
  const [maintainerAction, setMaintainerAction] = useState<{
    type: "comments" | "complete";
    assignmentId: string;
    reportTitle: string;
    currentComments?: string;
  } | null>(null);

  // Get user role and ID
  useEffect(() => {
    const fetchUserData = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session?.user) {
        const role = data.session.user.user_metadata?.role;
        const email = data.session.user.email;
        setUserRole(role);
        setCurrentUserEmail(email);
      }
    };
    fetchUserData();
  }, []);
  // Fetch reports
  const {
    data: reportsResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["issue-reports"],
    queryFn: () => fetchIssueReports(),
    staleTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: 1000,
  });

  // Fetch assignments to get status information
  const { data: assignmentsResponse, isLoading: assignmentsLoading } = useQuery(
    {
      queryKey: ["issue-assignments"],
      queryFn: () => fetchIssueAssignments({ limit: 100 }), // Get all assignments
      staleTime: 5 * 60 * 1000,
      retry: 3,
      retryDelay: 1000,
    }
  );

  const reports = reportsResponse?.reports || [];
  const assignments = assignmentsResponse?.assignments || [];

  // Create a map of issue assignments by issueId for quick lookup
  const assignmentMap = assignments.reduce(
    (map, assignment) => {
      map[assignment.issueId] = assignment;
      return map;
    },
    {} as Record<
      string,
      {
        id: string;
        status: "pending" | "in_progress" | "resolved" | "closed";
        maintenanceEmployeeName: string | null;
        maintenanceEmployeeEmail: string | null;
        comments: string | null;
      }
    >
  );

  // Fetch media for selected report
  const {
    data: media,
    isLoading: mediaLoading,
    error: mediaError,
  } = useQuery({
    queryKey: ["issue-report-media", selectedReport?.id],
    queryFn: () => fetchIssueReportMedia(selectedReport.id),
    enabled: !!selectedReport?.id && viewDialogOpen, // only run if selected and dialog is open
    retry: (failureCount, error: Error | unknown) => {
      // Don't retry if it's a 404 (media not found)
      const errorStatus =
        (error as { status?: number; code?: number })?.status ||
        (error as { status?: number; code?: number })?.code;
      if (errorStatus === 404) {
        return false;
      }
      return failureCount < 1; // Only retry once
    },
    // Don't throw errors, handle them in the component
    throwOnError: false,
  });
  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (reportId: string) => deleteIssueReport(reportId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["issue-reports"] });
    },
    onError: (error) => {
      console.error("Delete failed:", error);
      toast.error(t("branchReport.table.deleteFailed") + error);
    },
  });

  // Helper functions for role-based actions
  const isBranchManager = () => userRole === "BRANCH_MANAGER";
  const isAdmin = () => userRole === "ADMIN";
  const isStoreManager = () => userRole === "STORE_MANAGER";
  const isMaintainer = () => userRole === "MAINTAINER";
  const canAssignReports = () =>
    isAdmin() || isStoreManager() || isBranchManager();

  const handleAssignReport = (report: { id: string; title: string }) => {
    setReportToAssign(report);
    setAssignModalOpen(true);
  };

  const handleViewReport = (report: (typeof reports)[0]) => {
    console.log("Opening view dialog for report:", report); // Debug log
    setSelectedReport(report);
    setViewDialogOpen(true);
  };

  const handleCloseViewDialog = () => {
    setViewDialogOpen(false);
    setSelectedReport(null);
  };

  const handleMaintainerAction = (
    type: "comments" | "complete",
    assignmentId: string,
    reportTitle: string,
    currentComments?: string
  ) => {
    setMaintainerAction({ type, assignmentId, reportTitle, currentComments });
    setMaintainerModalOpen(true);
  };

  const getStatusBadge = (status: string | null) => {
    if (!status) return null;

    const statusClasses = {
      pending: "bg-yellow-100 text-yellow-800",
      in_progress: "bg-blue-100 text-blue-800",
      resolved: "bg-green-100 text-green-800",
      closed: "bg-gray-100 text-gray-800",
    };

    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          statusClasses[status as keyof typeof statusClasses] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {status.replace("_", " ")}
      </span>
    );
  };

  return (
    <Card className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="font-semibold text-gray-900 text-lg">
          {t("branchReport.issueReports")}
        </h3>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-gray-200 border-b">
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchReport.table.title")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchReport.table.branchName")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchReport.table.date")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchReport.table.performedBy")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchReport.table.status")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchReport.table.assignedTo")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("branchReport.table.action")}
              </th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <tr key={index} className="border-gray-100 border-b">
                  {[...Array(7)].map((_, i) => (
                    <td key={i} className="px-4 py-3">
                      <Skeleton className="w-24 h-4" />
                    </td>
                  ))}
                </tr>
              ))
            ) : error ? (
              <tr>
                <td colSpan={7} className="px-4 py-8 text-red-600 text-center">
                  {t("branchReport.table.errorLoading")}: {String(error)}
                </td>
              </tr>
            ) : reports.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-4 py-8 text-gray-500 text-center">
                  {t("branchReport.table.noReports")}
                </td>
              </tr>
            ) : (
              reports.map((report) => {
                const assignment = assignmentMap[report.id];
                const canAssign = canAssignReports() && !assignment;
                const canViewMaintainerActions =
                  isMaintainer() &&
                  assignment &&
                  assignment.maintenanceEmployeeEmail === currentUserEmail;

                return (
                  <tr
                    key={report.id}
                    className="hover:bg-gray-50 border-gray-100 border-b"
                  >
                    <td className="px-4 py-3 text-gray-900 text-sm">
                      {report.title || t("common.notAvailable")}
                    </td>
                    <td className="px-4 py-3 text-gray-900 text-sm">
                      {`${report.storeName} - ${report.branchLocation}`}
                    </td>
                    <td className="px-4 py-3 text-gray-700 text-sm">
                      {new Date(report.updatedAt).toLocaleString() ||
                        t("common.notAvailable")}
                    </td>
                    <td className="px-4 py-3 text-gray-700 text-sm">
                      {report.userName || t("common.notAvailable")}
                    </td>
                    <td className="px-4 py-3 text-gray-700 text-sm">
                      {assignment ? (
                        getStatusBadge(assignment.status)
                      ) : (
                        <span className="text-gray-400">—</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-gray-700 text-sm">
                      {assignment?.maintenanceEmployeeName || (
                        <span className="text-gray-400">—</span>
                      )}
                    </td>
                    <td className="flex gap-2 px-4 py-3 text-gray-700 text-sm">
                      <TooltipProvider>
                        <Dialog
                          open={viewDialogOpen}
                          onOpenChange={(open) => {
                            if (!open) {
                              handleCloseViewDialog();
                            }
                          }}
                        >
                          <DialogTrigger asChild>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleViewReport(report)}
                                  className="p-2"
                                >
                                  <Eye className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{t("branchReport.table.viewDetails")}</p>
                              </TooltipContent>
                            </Tooltip>
                          </DialogTrigger>
                          <DialogContent className="max-w-lg">
                            <DialogHeader>
                              <DialogTitle>{selectedReport?.title}</DialogTitle>
                              <DialogDescription>
                                <p className="mb-4">
                                  {selectedReport?.description ||
                                    t("branchReport.table.noDescription")}
                                </p>

                                {/* Media section */}
                                {mediaLoading ? (
                                  <div className="mb-4">
                                    <Skeleton className="w-full h-48" />
                                  </div>
                                ) : mediaError ? (
                                  // Always show placeholder for any media error to prevent crashes
                                  <div className="flex justify-center items-center bg-gray-50 mb-4 border rounded-md w-full h-48">
                                    <div className="text-gray-500 text-center">
                                      <div className="flex justify-center items-center bg-gray-200 mx-auto mb-2 rounded-lg w-16 h-16">
                                        <svg
                                          className="w-8 h-8 text-gray-400"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                          />
                                        </svg>
                                      </div>
                                      <p className="text-sm">No media found</p>
                                    </div>
                                  </div>
                                ) : media?.url ? (
                                  <div className="mb-4">
                                    <img
                                      src={media.url}
                                      alt={t("branchReport.table.reportMedia")}
                                      className="border rounded-md w-full max-h-64 object-contain"
                                    />
                                  </div>
                                ) : (
                                  // Show placeholder when no media URL is returned
                                  <div className="flex justify-center items-center bg-gray-50 mb-4 border rounded-md w-full h-48">
                                    <div className="text-gray-500 text-center">
                                      <div className="flex justify-center items-center bg-gray-200 mx-auto mb-2 rounded-lg w-16 h-16">
                                        <svg
                                          className="w-8 h-8 text-gray-400"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                          />
                                        </svg>
                                      </div>
                                      <p className="text-sm">No media found</p>
                                    </div>
                                  </div>
                                )}

                                {/* Show assignment status if available */}
                                {selectedReport &&
                                  assignmentMap[selectedReport.id] && (
                                    <div className="bg-blue-50 mt-4 p-3 border border-blue-200 rounded-lg">
                                      <div className="flex justify-between items-center mb-2">
                                        <h4 className="font-medium text-blue-800 text-sm">
                                          {t("branchReport.assignmentStatus")}
                                        </h4>
                                        {getStatusBadge(
                                          assignmentMap[selectedReport.id]
                                            .status
                                        )}
                                      </div>
                                      {assignmentMap[selectedReport.id]
                                        .maintenanceEmployeeName && (
                                        <p className="text-blue-700 text-sm">
                                          <strong>
                                            {t("branchReport.assignedTo")}:
                                          </strong>{" "}
                                          {
                                            assignmentMap[selectedReport.id]
                                              .maintenanceEmployeeName
                                          }
                                        </p>
                                      )}
                                    </div>
                                  )}

                                {/* Show comments if issue is resolved and has comments */}
                                {selectedReport &&
                                  assignmentMap[selectedReport.id] &&
                                  assignmentMap[selectedReport.id].status ===
                                    "resolved" &&
                                  assignmentMap[selectedReport.id].comments && (
                                    <div className="bg-green-50 mt-4 p-3 border border-green-200 rounded-lg">
                                      <h4 className="mb-2 font-medium text-green-800 text-sm">
                                        {t("branchReport.resolutionComments")}
                                      </h4>
                                      <p className="text-green-700 text-sm">
                                        {
                                          assignmentMap[selectedReport.id]
                                            .comments
                                        }
                                      </p>
                                      {assignmentMap[selectedReport.id]
                                        .maintenanceEmployeeName && (
                                        <p className="mt-2 text-green-600 text-xs">
                                          —{" "}
                                          {
                                            assignmentMap[selectedReport.id]
                                              .maintenanceEmployeeName
                                          }
                                        </p>
                                      )}
                                    </div>
                                  )}
                              </DialogDescription>
                            </DialogHeader>
                          </DialogContent>
                        </Dialog>

                        {/* Branch Manager: Assign Report Action */}
                        {canAssign && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleAssignReport(report)}
                                className="p-2 text-blue-600 hover:text-blue-700"
                              >
                                <UserPlus className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{t("branchReport.assignReport")}</p>
                            </TooltipContent>
                          </Tooltip>
                        )}

                        {/* Maintainer: Add Comments Action */}
                        {canViewMaintainerActions &&
                          assignment.status !== "resolved" &&
                          assignment.status !== "closed" && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    handleMaintainerAction(
                                      "comments",
                                      assignment.id,
                                      report.title,
                                      assignment.comments
                                    )
                                  }
                                  className="p-2 text-blue-600 hover:text-blue-700"
                                >
                                  <MessageCircle className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{t("branchReport.addComments")}</p>
                              </TooltipContent>
                            </Tooltip>
                          )}

                        {/* Maintainer: Mark as Done Action */}
                        {canViewMaintainerActions &&
                          assignment.status !== "resolved" &&
                          assignment.status !== "closed" && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    handleMaintainerAction(
                                      "complete",
                                      assignment.id,
                                      report.title,
                                      assignment.comments
                                    )
                                  }
                                  className="p-2 text-green-600 hover:text-green-700"
                                >
                                  <CheckCircle className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{t("branchReport.markAsCompleted")}</p>
                              </TooltipContent>
                            </Tooltip>
                          )}

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="p-2 text-red-600 hover:text-red-700"
                              onClick={() => {
                                setReportToDelete(report);
                                setDeleteModalOpen(true);
                              }}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{t("common.delete")}</p>
                          </TooltipContent>
                        </Tooltip>

                        <Link to={`/report/edit/${report.id}`}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button variant="ghost" size="sm" className="p-2">
                                <Edit className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{t("common.edit")}</p>
                            </TooltipContent>
                          </Tooltip>
                        </Link>
                      </TooltipProvider>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
      <DeleteSectionModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setReportToDelete(null);
        }}
        onConfirm={() => {
          if (reportToDelete) {
            deleteMutation.mutate(reportToDelete.id);
          }
        }}
        labelToDelete={t("branchReport.table.report")}
        isPending={deleteMutation.isPending}
      />

      {/* Assignment Modal */}
      {reportToAssign && (
        <AssignReportModal
          isOpen={assignModalOpen}
          onClose={() => {
            setAssignModalOpen(false);
            setReportToAssign(null);
          }}
          reportId={reportToAssign.id}
          reportTitle={reportToAssign.title}
        />
      )}

      {/* Maintainer Actions Modal */}
      {maintainerAction && (
        <MaintainerActionsModal
          isOpen={maintainerModalOpen}
          onClose={() => {
            setMaintainerModalOpen(false);
            setMaintainerAction(null);
          }}
          assignmentId={maintainerAction.assignmentId}
          reportTitle={maintainerAction.reportTitle}
          actionType={maintainerAction.type}
          currentComments={maintainerAction.currentComments}
        />
      )}
    </Card>
  );
};

export default ReportsTable;
