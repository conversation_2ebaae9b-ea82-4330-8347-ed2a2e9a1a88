import { useParams, useSearchParams } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import {
  fetchResponseByAssignmentId,
  fetchResponseById,
} from "@/data/response";
import { Response } from "@/lib/types";
import { useTranslation } from "react-i18next";

const AssignmentResponseDetail = () => {
  const { t } = useTranslation();
  const { responseId } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const isAssignmentId = searchParams.get("isAssignmentId") === "true";

  const {
    data: response,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["response", responseId],
    queryFn: () => {
      if (isAssignmentId) {
        return fetchResponseByAssignmentId(responseId);
      } else {
        return fetchResponseById(responseId!);
      }
    },
    enabled: !!responseId,
  });

  if (isLoading) return <div>{t("assignmentResponseDetail.loading")}</div>;
  if (error)
    return (
      <div>
        {t("assignmentResponseDetail.error")}: {error.message}
      </div>
    );

  const data: Response = response.response;
  console.log("DATA: ", data);
  const sections = data.sections;
  const fields = sections.flatMap((section) => section.fields);

  return (
    <div className="bg-background p-6 min-h-screen">
      <div className="mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-6">
          <h1 className="mb-2 font-semibold text-foreground text-2xl">
            {data.assignmentTitle}
          </h1>
          <p className="text-muted-foreground">
            {t("assignmentResponseDetail.branch")} :{" "}
            <span className="text-foreground">{data.branchLocation}</span>
          </p>
        </div>

        {/* Questions and Answers */}
        <div className="space-y-6">
          {fields.map((item, index) => (
            <Card key={item.id} className="p-0">
              <CardContent className="p-0">
                {/* Question */}
                <div className="flex items-center bg-[#e7eaee] mb-4 px-[0.9375rem] py-2 rounded-t-[0.75rem]">
                  <h3 className="font-medium text-gray-900 text-lg">
                    {t("assignmentResponseDetail.question")} {index + 1}.{" "}
                    {item.question} <span className="text-destructive">*</span>
                  </h3>
                </div>

                {/* Answer */}
                <div className="flex flex-row gap-2 bg-muted/30 p-4 rounded-lg">
                  <div className="mb-2">
                    <span className="font-medium text-foreground text-sm">
                      {t("assignmentResponseDetail.answer")}.
                    </span>
                  </div>

                  {item.media.length > 0 ? (
                    <div className="space-y-3">
                      <div className="border border-border rounded-lg w-48 h-32 overflow-hidden">
                        <img
                          src={item.media[0].url}
                          alt={t("assignmentResponseDetail.auditResponse")}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <p className="text-muted-foreground text-sm">
                        {index + 1}. {item.answer}
                      </p>
                    </div>
                  ) : (
                    <p className="text-muted-foreground leading-relaxed">
                      {index + 1}. {item.answer}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-8 text-center">
          <p className="text-muted-foreground text-sm">
            {t("assignmentResponseDetail.responseId")}: {responseId || "1"}
          </p>
        </div>
      </div>
    </div>
  );
};

export default AssignmentResponseDetail;
