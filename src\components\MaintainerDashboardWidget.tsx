import React, { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  Clock,
  AlertTriangle,
  User,
  ArrowRight,
} from "lucide-react";
import { fetchIssueAssignments } from "@/data/issueAssignments";
import { supabase } from "@/integrations/supabase/client";
import { Link } from "react-router-dom";

const MaintainerDashboardWidget: React.FC = () => {
  const { t } = useTranslation();
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<string | null>(null);

  // Get current user data
  useEffect(() => {
    const fetchUserData = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session?.user) {
        const email = data.session.user.email;
        const role = data.session.user.user_metadata?.role;
        setUserEmail(email);
        setUserRole(role);
      }
    };
    fetchUserData();
  }, []);

  // Fetch assignments for current maintainer
  const {
    data: assignmentsResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["maintainer-dashboard", userEmail],
    queryFn: () => fetchIssueAssignments({ limit: 100 }),
    enabled: !!userEmail && userRole === "MAINTAINER",
    staleTime: 3 * 60 * 1000, // Refresh every 3 minutes
    retry: 3,
    retryDelay: 1000,
  });

  // Don't show for non-maintainer users
  if (userRole !== "MAINTAINER") {
    return null;
  }

  // Filter assignments for current user
  const myAssignments =
    assignmentsResponse?.assignments.filter(
      (assignment) => assignment.maintenanceEmployeeEmail === userEmail
    ) || [];

  // Calculate statistics
  const totalAssignments = myAssignments.length;
  const pendingAssignments = myAssignments.filter(
    (a) => a.status === "pending"
  ).length;
  const inProgressAssignments = myAssignments.filter(
    (a) => a.status === "in_progress"
  ).length;
  const completedAssignments = myAssignments.filter(
    (a) => a.status === "resolved"
  ).length;

  // Calculate urgent assignments (more than 24 hours old)
  const urgentAssignments = myAssignments.filter((assignment) => {
    const createdAt = new Date(assignment.createdAt);
    const now = new Date();
    const hoursDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
    return (
      hoursDiff > 24 &&
      assignment.status !== "resolved" &&
      assignment.status !== "closed"
    );
  }).length;

  if (isLoading) {
    return (
      <Card className="h-48">
        <CardContent className="flex justify-center items-center h-full">
          <div className="text-center">
            <div className="mx-auto mb-2 border-gray-800 border-b-2 rounded-full w-8 h-8 animate-spin"></div>
            <p className="text-gray-500 text-sm">{t("common.loading")}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="p-4">
          <div className="text-red-600 text-sm text-center">
            {t("branchReport.table.errorLoading")}: {String(error)}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <User className="w-5 h-5 text-blue-600" />
          {t("branchReport.myAssignments")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {totalAssignments === 0 ? (
          <div className="py-6 text-center">
            <CheckCircle className="mx-auto mb-2 w-8 h-8 text-gray-300" />
            <p className="text-gray-500 text-sm">
              {t("branchReport.noAssignments")}
            </p>
          </div>
        ) : (
          <>
            {/* Statistics Grid */}
            <div className="gap-3 grid grid-cols-2">
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-blue-900 text-xs">
                      {t("branchReport.table.status")}: Pending
                    </p>
                    <p className="font-bold text-blue-900 text-xl">
                      {pendingAssignments}
                    </p>
                  </div>
                  <Clock className="w-5 h-5 text-blue-600" />
                </div>
              </div>

              <div className="bg-yellow-50 p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-yellow-900 text-xs">
                      In Progress
                    </p>
                    <p className="font-bold text-yellow-900 text-xl">
                      {inProgressAssignments}
                    </p>
                  </div>
                  <AlertTriangle className="w-5 h-5 text-yellow-600" />
                </div>
              </div>

              <div className="bg-green-50 p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-green-900 text-xs">
                      Completed
                    </p>
                    <p className="font-bold text-green-900 text-xl">
                      {completedAssignments}
                    </p>
                  </div>
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
              </div>

              <div className="bg-red-50 p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-red-900 text-xs">Urgent</p>
                    <p className="font-bold text-red-900 text-xl">
                      {urgentAssignments}
                    </p>
                  </div>
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="pt-2">
              <Link to="/my-assignments">
                <Button variant="outline" className="w-full">
                  {t("common.view")} {t("branchReport.myAssignments")}
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
              </Link>
            </div>

            {/* Urgent Alert */}
            {urgentAssignments > 0 && (
              <div className="bg-red-50 p-3 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                  <span className="font-medium text-red-800 text-sm">
                    {urgentAssignments} urgent{" "}
                    {urgentAssignments === 1 ? "assignment" : "assignments"}{" "}
                    requiring attention
                  </span>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default MaintainerDashboardWidget;
