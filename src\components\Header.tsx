import { Link, useLocation, useResolvedPath } from "react-router-dom";
import { Button } from "./ui/button";
import { useTranslation } from "react-i18next";
import { languageOptions } from "@/lib/utils";
import ProfileDropdown from "./ProfileDropdown";
import { useQuery } from "@tanstack/react-query";
import { fetchCurrentUserBrandingSettings } from "@/data/settings";

const Header = () => {
  const { pathname } = useLocation();
  const paths = pathname.split("/");
  const firstPath = paths[1];
  const { i18n } = useTranslation();
  const language = i18n.language;

  // Fetch current user's branding settings
  const { data: brandingSettings } = useQuery({
    queryKey: ["currentUserBrandingSettings"],
    queryFn: fetchCurrentUserBrandingSettings,
  });

  const handleChangeLanguage = (value: string) => {
    i18n.changeLanguage(value);
    document.dir = value === "ar" ? "rtl" : "ltr";
  };

  // Dynamic Logo Component
  const DynamicLogo = () => {
    const logoUrl = brandingSettings?.logoUrl;

    return (
      <div className="flex justify-center items-center w-10 h-10">
        <img
          src={logoUrl || "/assets/images/y_logo.svg"}
          alt="Logo"
          className="w-full h-full object-contain"
        />
      </div>
    );
  };
  return (
    <div className="bg-secondary-bg px-6 py-4 border-b w-full">
      <div className="flex justify-between items-center w-full">
        <div className="flex items-center space-x-4">
          <DynamicLogo />
        </div>
        <div className="flex items-center space-x-4">
          <Link to="/notifications">
            <Button variant="ghost" size="sm">
              <span className="text-gray-600">🔔</span>
            </Button>
          </Link>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() =>
                handleChangeLanguage(language === "en" ? "ar" : "en")
              }
            >
              <span className="text-gray-600 text-sm">
                {languageOptions.find((o) => o.value === language)?.icon ??
                  "N/A"}
              </span>
            </Button>
            <ProfileDropdown />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
