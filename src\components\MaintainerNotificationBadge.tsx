import React from "react";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { Badge } from "@/components/ui/badge";
import { Bell } from "lucide-react";
import { fetchIssueAssignments } from "@/data/issueAssignments";
import { supabase } from "@/integrations/supabase/client";
import { useState, useEffect } from "react";

const MaintainerNotificationBadge: React.FC = () => {
  const { t } = useTranslation();
  const [userEmail, setUserEmail] = useState<string | null>(null);

  // Get current user email
  useEffect(() => {
    const fetchUserEmail = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session?.user?.email) {
        setUserEmail(data.session.user.email);
      }
    };
    fetchUserEmail();
  }, []);

  // Fetch assignments for current maintainer
  const { data: assignmentsResponse } = useQuery({
    queryKey: ["maintainer-notifications", userEmail],
    queryFn: () => fetchIssueAssignments({ limit: 100 }),
    enabled: !!userEmail,
    staleTime: 2 * 60 * 1000, // Refresh every 2 minutes
    retry: 3,
    retryDelay: 1000,
  });

  // Filter assignments for current user that are pending or urgent
  const myUrgentAssignments =
    assignmentsResponse?.assignments.filter((assignment) => {
      const isMyAssignment = assignment.maintenanceEmployeeEmail === userEmail;
      const isPending = assignment.status === "pending";

      // Calculate if assignment is urgent (more than 24 hours old)
      const createdAt = new Date(assignment.createdAt);
      const now = new Date();
      const hoursDiff =
        (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
      const isUrgent = hoursDiff > 24;

      return isMyAssignment && (isPending || isUrgent);
    }) || [];

  const urgentCount = myUrgentAssignments.length;

  if (!userEmail || urgentCount === 0) {
    return null;
  }

  return (
    <div className="relative">
      <Badge
        variant="destructive"
        className="-top-2 -right-2 absolute flex justify-center items-center p-0 w-5 h-5 text-xs animate-pulse"
      >
        {urgentCount}
      </Badge>
      <Bell className="w-4 h-4 text-red-500" />
    </div>
  );
};

export default MaintainerNotificationBadge;
