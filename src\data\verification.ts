const API_BASE_URL = process.env.API_ENDPOINT || "http://localhost:8000";

export interface VerificationResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    email: string;
    name: string;
    isVerified: boolean;
  };
  error?: string;
}

export interface SendVerificationRequest {
  email: string;
}

export interface ResendVerificationRequest {
  email: string;
}

// Send verification email
export const sendVerificationEmail = async (
  data: SendVerificationRequest
): Promise<VerificationResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/auth/send-verification`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to send verification email");
  }

  return response.json();
};

// Verify email with token
export const verifyEmailToken = async (
  token: string
): Promise<VerificationResponse> => {
  const response = await fetch(
    `${API_BASE_URL}/api/auth/verify?token=${token}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Email verification failed");
  }

  return response.json();
};

// Resend verification email
export const resendVerificationEmail = async (
  data: ResendVerificationRequest
): Promise<VerificationResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/auth/resend-verification`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to resend verification email");
  }

  return response.json();
};
