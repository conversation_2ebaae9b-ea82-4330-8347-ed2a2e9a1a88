import React from "react";
import { GoogleMap, Marker } from "@react-google-maps/api";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { GoogleMapsLoader } from "./GoogleMapsLoader";
import { useTranslation } from "react-i18next";

const mapContainerStyle = {
  width: "100%",
  height: "400px",
};

interface MapViewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  latitude: number;
  longitude: number;
  title?: string;
}

export function MapViewDialog({
  isOpen,
  onClose,
  latitude,
  longitude,
  title,
}: MapViewDialogProps) {
  const { t } = useTranslation();

  // Validate coordinates
  const validLat = !isNaN(latitude) && latitude >= -90 && latitude <= 90;
  const validLng = !isNaN(longitude) && longitude >= -180 && longitude <= 180;

  // For testing: use New York coordinates if invalid coordinates are provided
  const center = {
    lat: validLat ? latitude : 40.7128,
    lng: validLng ? longitude : -74.006,
  };

  console.log("MapViewDialog - Original coordinates:", { latitude, longitude });
  console.log("MapViewDialog - Processed center:", center);
  console.log("MapViewDialog - Valid coordinates:", { validLat, validLng });
  console.log("MapViewDialog - Is open:", isOpen);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {title || t("branchManagement.mapDialog.title")}
          </DialogTitle>
        </DialogHeader>
        {!validLat || !validLng ? (
          <div className="bg-yellow-50 mt-4 p-4 border border-yellow-200 rounded">
            <p className="font-medium text-yellow-800">
              Invalid coordinates detected
            </p>
            <p className="mt-1 text-yellow-700 text-sm">
              Original: Lat: {latitude}, Lng: {longitude}
            </p>
            <p className="text-yellow-700 text-sm">
              Showing default location (New York) for testing
            </p>
          </div>
        ) : null}
        <div className="bg-primary-bg mt-4">
          <GoogleMapsLoader>
            <GoogleMap
              mapContainerStyle={mapContainerStyle}
              center={center}
              zoom={15}
              onLoad={(map) => {
                console.log("Map loaded successfully:", map);
                // Force the map to resize after dialog opens
                setTimeout(() => {
                  if (window.google?.maps?.event) {
                    window.google.maps.event.trigger(map, "resize");
                    map.setCenter(center);
                  }
                }, 100);
              }}
              options={{
                disableDefaultUI: false,
                zoomControl: true,
                streetViewControl: true,
                mapTypeControl: true,
                fullscreenControl: true,
                gestureHandling: "greedy",
              }}
            >
              <Marker position={center} title={title} />
            </GoogleMap>
          </GoogleMapsLoader>
        </div>
      </DialogContent>
    </Dialog>
  );
}
