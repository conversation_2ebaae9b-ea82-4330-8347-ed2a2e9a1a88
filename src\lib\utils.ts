import { clsx, type ClassValue } from "clsx";
import {
  NavigateOptions,
  SetURLSearchParams,
  URLSearchParamsInit,
} from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { Filter, TemplateViewType } from "./types";
import { CalendarReturn } from "@/data/assignments";
import { format as formatDate } from "date-fns";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Generate pagination buttons
export const generatePaginationButtons = ({
  currentPage,
  totalPages,
}: {
  currentPage: number;
  totalPages: number;
}) => {
  const buttons = [];
  const maxVisiblePages = 5;

  let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
  const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }

  for (let i = startPage; i <= endPage; i++) {
    buttons.push(i);
  }

  return buttons;
};

export const updateUrlParams = (
  updates: Record<string, string | number>,
  searchParams: URLSearchParams,
  setSearchParams: (
    nextInit?:
      | URLSearchParamsInit
      | ((prev: URLSearchParams) => URLSearchParamsInit),
    navigateOpts?: NavigateOptions
  ) => void
) => {
  const newParams = new URLSearchParams(searchParams);

  Object.entries(updates).forEach(([key, value]) => {
    if (key === "page" && value === 1) {
      newParams.delete(key);
    } else {
      newParams.set(key, value.toString());
    }
  });

  setSearchParams(newParams);
};

export const addFilter = (
  filters: Filter[],
  setFilters: React.Dispatch<React.SetStateAction<Filter[]>>
) => {
  const newFilter: Filter = {
    id: Date.now().toString(),
    field: "store_admin_email",
    value: "",
  };
  setFilters([...filters, newFilter]);
};

export const updateFilter = (
  updatedFilter: Filter,
  filters: Filter[],
  setFilters: React.Dispatch<React.SetStateAction<Filter[]>>
) => {
  setFilters(
    filters.map((f) => (f.id === updatedFilter.id ? updatedFilter : f))
  );
};

export const removeFilter = (
  id: string,
  filters: Filter[],
  setFilters: React.Dispatch<React.SetStateAction<Filter[]>>
) => {
  setFilters(filters.filter((f) => f.id !== id));
};

// Parse filters from URL
export const parseFiltersFromUrl = (
  searchParams: URLSearchParams
): Filter[] => {
  const filtersParam = searchParams.get("filters");
  if (!filtersParam) return [];

  try {
    return JSON.parse(decodeURIComponent(filtersParam));
  } catch {
    return [];
  }
};

export function isUUID(id: string): boolean {
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
    id
  );
}
// Transform API data to form data format
export const transformTemplateData = (apiData: TemplateViewType) => {
  return {
    title: apiData.title || "",
    businessType: apiData.businessType || "",
    sections:
      apiData.sections?.map((section, sectionIndex: number) => ({
        id: section.id,
        name: section.name || "",
        order: section.order || sectionIndex + 1,
        fields:
          section.fields?.map((field, fieldIndex: number) => ({
            id: field.id || (Date.now() + fieldIndex).toString(),
            questionText: field.question || "",
            questionType: field.questionType || "Text",
            required: field.required || false,
            addHint: field.hint || "",
            requireEvidence: field.requiresEvidence || false,
            scoreWeight: Number(field.score) || 3,
            order: field.order || fieldIndex + 1,
          })) || [],
      })) || [],
  };
};

export const setParam = (
  key: string,
  value: string,
  searchParams: URLSearchParams,
  setSearchParams: SetURLSearchParams
) => {
  const newParams = new URLSearchParams(searchParams);
  newParams.set(key, value);
  if (key !== "page") newParams.set("page", "1");
  setSearchParams(newParams);
};

export const generateMockFieldId = () =>
  crypto.randomUUID
    ? crypto.randomUUID()
    : "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function debounce<T extends (...args: any[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

export function formatDuration(start: Date, end: Date): string {
  const ms = Math.abs(end.getTime() - start.getTime());

  const totalMinutes = Math.floor(ms / 60000);
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  const parts: string[] = [];
  if (hours > 0) parts.push(`${hours} hour${hours !== 1 ? "s" : ""}`);
  if (minutes > 0 || parts.length === 0)
    parts.push(`${minutes} minute${minutes !== 1 ? "s" : ""}`);

  return parts.join(" ");
}

// Quick date formatting utility - for use outside of React components
export function formatDateWithPattern(
  date: Date | string,
  pattern: string
): string {
  if (!date) return "";

  const dateObj = typeof date === "string" ? new Date(date) : date;

  try {
    return formatDate(dateObj, pattern);
  } catch (error) {
    console.error("Error formatting date:", error);
    // Fallback to basic formatting
    return dateObj.toLocaleDateString();
  }
}

export const languageOptions = [
  {
    value: "ar",
    label: "Arabic",
    icon: "🇸🇦",
  },
  { value: "en", label: "English", icon: "🇺🇸" },
];
export function getEventTypeColor(type: string) {
  switch (type) {
    case "scheduled":
      return "bg-success";
    case "completed":
      return "bg-blue-500";
    case "overdue":
      return "bg-warning";
    case "missed":
      return "bg-destructive";
    default:
      return "bg-gray-400";
  }
}

export const getEventType = (event: CalendarReturn) => {
  if (event.completedAt) {
    return "completed";
  }

  const now = new Date();
  const dueDate = new Date(event.dueDate);

  if (!dueDate) {
    return "scheduled";
  }

  if (dueDate > now) {
    return "scheduled";
  }

  // Calculate days passed since due date
  const daysPassed = Math.floor(
    (now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (daysPassed >= 7) {
    return "missed";
  }

  return "overdue";
};
