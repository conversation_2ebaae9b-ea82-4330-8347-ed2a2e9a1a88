import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { createStore, updateStore } from "@/data/stores";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner"; // or your preferred toast library
import { z } from "zod";
import { Store } from "./ClientSnapshot";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

// Zod schema for form validation
const newCompanySchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  businessType: z.string().min(1, "Business type is required"),
  numberOfBranches: z.coerce
    .number()
    .min(1, "Number of branches must be at least 1"),
  adminEmail: z.string().email("Please enter a valid email address"),
  status: z.boolean().default(true),
});

type NewCompanyFormData = z.infer<typeof newCompanySchema>;

export function NewCompanyForm({ store }: { store?: Store }) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  console.log(store);

  const form = useForm<NewCompanyFormData>({
    resolver: zodResolver(newCompanySchema),
    defaultValues: {
      companyName: "",
      businessType: "",
      numberOfBranches: 0,
      adminEmail: "",
      status: true,
    },
  });

  // Populate form with store data when passed
  useEffect(() => {
    if (store) {
      form.reset({
        companyName: store.name,
        businessType: store.businessType,
        numberOfBranches: store.numberOfBranches,
        adminEmail: store.adminEmail, // ← You may want to populate this from another source
        status: store.status === "active",
      });
    }
  }, [store, form]);

  const createStoreMutation = useMutation({
    mutationFn: async (formData: NewCompanyFormData) => {
      return await createStore({
        adminEmail: formData.adminEmail,
        businessType: formData.businessType,
        name: formData.companyName,
        numberOfBranches: formData.numberOfBranches,
        status: formData.status ? "active" : "inactive",
      });
    },
    onSuccess: () => {
      toast.success("Company created successfully!");
      queryClient.invalidateQueries({ queryKey: ["companies"] });
      navigate("/companies");
    },
    onError: (error) => {
      console.log(error);
      toast.error(error.message || "Failed to create company");
    },
  });
  const updateStoreMutation = useMutation({
    mutationFn: async (formData: NewCompanyFormData) => {
      return await updateStore({
        storeId: store?.id,
        adminEmail: formData.adminEmail,
        businessType: formData.businessType,
        name: formData.companyName,
        numberOfBranches: formData.numberOfBranches,
        status: formData.status ? "active" : "inactive",
      });
    },
    onSuccess: () => {
      toast.success("Company updated successfully!");
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === "companies" ||
          (query.queryKey[0] === "company" && query.queryKey[1] === store.id),
      });
      navigate("/companies");
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update company");
    },
  });

  const onSubmit = async (data: NewCompanyFormData) => {
    const result = store
      ? await updateStoreMutation.mutateAsync(data)
      : await createStoreMutation.mutateAsync(data);
    console.log(result);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
          {/* Company name */}
          <FormField
            control={form.control}
            name="companyName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-slate-700 text-sm">
                  {t("company.companyName")}
                </FormLabel>
                <FormControl>
                  <Input {...field} className="border-slate-200" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Business type */}
          <FormField
            control={form.control}
            name="businessType"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-slate-700 text-sm">
                  {t("company.businessType")}
                </FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    placeholder={t("company.businessType")}
                    className="border-slate-200"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="gap-6 grid grid-cols-1 md:grid-cols-2">
          {/* Assign admin email */}
          <FormField
            control={form.control}
            name="adminEmail"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-slate-700 text-sm">
                  {t("company.assignAdminEmail")}
                </FormLabel>
                <FormControl>
                  <Input {...field} type="email" className="border-slate-200" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Number of branches */}
          <FormField
            control={form.control}
            name="numberOfBranches"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-slate-700 text-sm">
                  {t("company.numberOfBranches")}
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    className="border-slate-200"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        {/* Form actions */}
        <div className="flex justify-end items-center space-x-4 pt-8">
          <Button
            type="button"
            variant="outline"
            className="px-8 py-2 border-slate-200 text-slate-600 hover:"
            onClick={() => navigate("/companies")}
          >
            {t("company.cancel")}
          </Button>
          <Button
            type="submit"
            disabled={
              createStoreMutation.isPending || updateStoreMutation.isPending
            }
            className="bg-slate-800 hover:bg-slate-900 px-8 py-2 text-white"
          >
            {createStoreMutation.isPending
              ? t("company.creating")
              : updateStoreMutation.isPending
              ? t("company.updating")
              : store
              ? t("company.update")
              : t("company.create")}
          </Button>
        </div>
      </form>
    </Form>
  );
}
