import React, { createContext, useContext, useState, ReactNode } from 'react';
import NotificationMessage, { NotificationData } from './NotificationMessage';

interface NotificationContextType {
  notifications: NotificationData[];
  addNotification: (notification: Omit<NotificationData, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
  maxNotifications?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

export const NotificationContextProvider: React.FC<NotificationProviderProps> = ({ 
  children, 
  maxNotifications = 5,
  position = 'top-right' 
}) => {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);

  const addNotification = (notification: Omit<NotificationData, 'id' | 'timestamp'>) => {
    const newNotification: NotificationData = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: Date.now()
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      // Keep only the most recent notifications
      return updated.slice(0, maxNotifications);
    });
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const value: NotificationContextType = {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      
      {/* Render notifications */}
      <div style={{ 
        position: 'fixed', 
        zIndex: 9999,
        pointerEvents: 'none' // Allow clicks to pass through the container
      }}>
        {notifications.map((notification, index) => (
          <div
            key={notification.id}
            style={{
              pointerEvents: 'auto', // Re-enable pointer events for individual notifications
              position: 'relative',
              marginBottom: index < notifications.length - 1 ? '12px' : '0',
              ...(position.includes('top') ? { 
                top: `${20 + (index * 85)}px` 
              } : { 
                bottom: `${20 + (index * 85)}px` 
              }),
              ...(position.includes('right') ? { right: '20px' } : { left: '20px' })
            }}
          >
            <NotificationMessage
              notification={notification}
              onClose={removeNotification}
              position={position}
            />
          </div>
        ))}
      </div>
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationContextProvider');
  }
  return context;
};

export default NotificationContext;