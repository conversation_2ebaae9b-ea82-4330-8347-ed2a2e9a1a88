import React, { useState, useEffect } from "react";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { Upload, FileText, X, FileImage } from "lucide-react";
import { fetchAllBranches } from "@/data/branches";
import {
  createIssueReport,
  IssueReportInput,
  fetchIssueReportById,
  updateIssueReport,
} from "@/data/reports";
import { uploadImageToSupabase } from "@/data/media";

const formSchema = z.object({
  branchId: z.string().min(1, "Branch is required"),
  title: z.string().min(1, "Title is required"),
  email: z.string().email("Invalid email"),
  description: z.string().min(1, "Description is required"),
});

type FormValues = z.infer<typeof formSchema>;

export default function ReportForm({ reportId }: { reportId?: string }) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [attachedFile, setAttachedFile] = useState<File | null>(null);
  // State for managing uploaded images per field
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);

  const {
    data: reportData,
    isLoading: reportLoading,
    isError: reportError,
  } = useQuery({
    enabled: !!reportId,
    queryKey: ["issue-report", reportId],
    queryFn: async () => {
      return fetchIssueReportById(reportId!);
    },
  });

  useEffect(() => {
    if (reportData) {
      form.reset({
        branchId: reportData.report.branchId ?? "",
        title: reportData.report.title ?? "",
        email: reportData.report.userId
          ? reportData.report.userEmail ?? ""
          : "",
        description: reportData.report.description ?? "",
      });

      // Optionally preload image previews from `reportData.media` here
      if (reportData.report.media) {
        setImagePreviewUrls(reportData.report.media.map((m) => m.url));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reportData]);

  const {
    data: branchesResult,
    isLoading: branchesLoading,
    isError: branchesError,
  } = useQuery({
    queryKey: ["branches"],
    queryFn: fetchAllBranches,
  });

  const branchesOptions =
    branchesResult?.map((branch) => ({
      label: `${branch.storeName ?? "Unknown Store"} - ${
        branch.location ?? "Unknown Location"
      }`,
      value: branch.id,
    })) ?? [];

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      branchId: "",
      title: "",
      email: "",
      description: "",
    },
  });

  const submitMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      if (!data) {
        throw new Error("Invalid form data");
      }
      const payload: IssueReportInput = {
        title: data.title,
        description: data.description,
        branchId: data.branchId,
        mediaIds: uploadedImages[data.branchId]?.map((file) =>
          URL.createObjectURL(file)
        ),
        email: data.email,
      };
      return createIssueReport(payload);
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Report submitted successfully",
        description: `Your report for ${
          branchesOptions.find((b) => b.value === variables.branchId)?.label ??
          variables.branchId
        } has been submitted.`,
      });
      queryClient.invalidateQueries({
        queryKey: ["issue-reports"],
      });
    },
    onError: (error) => {
      toast({
        title: "Submission failed",
        description: error?.message || "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (values: FormValues) => {
      return updateIssueReport(reportId!, values);
    },
    onSuccess: (data) => {
      toast({
        title: "Report updated",
        description: `Your report for ${
          branchesOptions.find((b) => b.value === data.report.branchId)
            ?.label ?? data.report.branchId
        } has been updated.`,
      });
      queryClient.invalidateQueries({
        queryKey: ["issue-reports"],
      });
    },
    onError: (error) => {
      toast({
        title: "Submission failed",
        description: error?.message || "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (values: FormValues) => {
    try {
      const result = reportId
        ? await updateMutation.mutateAsync(values)
        : await submitMutation.mutateAsync(values);
      await uploadImageToSupabase({
        file: uploadedImages[0],
        issueReportId: result.report.id,
      });
      toast({
        title: "Submission successful",
        description: "Your report has been submitted successfully.",
      });
      navigate("/report");
    } catch (error) {
      toast({
        title: "Submission failed",
        description: error?.message || "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  // Handle image upload
  const handleImageUpload = (files: FileList | null) => {
    if (!files) return;

    const newFiles = Array.from(files);
    const currentFiles = uploadedImages || [];

    // Limit to 1 images per field (you can adjust this)
    const maxImages = 1;
    const totalFiles = currentFiles.length + newFiles.length;

    if (totalFiles > maxImages) {
      toast({
        title: "Maximum images exceeded",
        description: `Maximum ${maxImages} images allowed per field`,
        variant: "destructive",
      });
      return;
    }

    // Create preview URLs for new files
    const newPreviewUrls = newFiles.map((file) => URL.createObjectURL(file));

    setUploadedImages(newFiles);

    setImagePreviewUrls(newPreviewUrls);
  };

  // Remove image
  const removeImage = (index: number) => {
    const currentFiles = uploadedImages || [];
    const currentUrls = imagePreviewUrls || [];

    // Revoke the object URL to free memory
    URL.revokeObjectURL(currentUrls[index]);

    const newFiles = currentFiles.filter((_, i) => i !== index);
    const newUrls = currentUrls.filter((_, i) => i !== index);

    setUploadedImages(newFiles);

    setImagePreviewUrls(newUrls);
  };

  if (reportId && reportLoading) {
    return (
      <div className="p-6 text-center">
        <p className="text-muted-foreground">Loading report data...</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mx-auto max-w-2xl">
        <div className="flex items-center gap-3 mb-6">
          <div className="flex items-center gap-3">
            <div className="flex justify-center items-center bg-primary rounded-lg w-10 h-10">
              <FileText className="w-5 h-5 text-primary-foreground" />
            </div>
            <h1 className="font-semibold text-xl">
              {reportId ? "Edit" : "Submit"} Report
            </h1>
          </div>
        </div>

        <Card className="shadow-sm border-border">
          <CardHeader>
            <CardTitle className="font-medium text-lg">
              Create new report
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className="space-y-6"
              >
                {/* Branch Selection */}
                <FormField
                  control={form.control}
                  name="branchId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Branch</FormLabel>
                      <FormControl>
                        <select
                          {...field}
                          className="block p-2 border rounded-md w-full text-sm"
                          disabled={branchesLoading || branchesError}
                        >
                          <option value="">
                            {branchesLoading
                              ? "Loading..."
                              : branchesError
                              ? "Failed to load branches"
                              : "Select branch"}
                          </option>
                          {branchesOptions.map((branch) => (
                            <option key={branch.value} value={branch.value}>
                              {branch.label}
                            </option>
                          ))}
                        </select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Report Title */}
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Report Title</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter report title"
                          disabled={
                            submitMutation.isPending || updateMutation.isPending
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="Enter your email address"
                          disabled={
                            submitMutation.isPending || updateMutation.isPending
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          rows={4}
                          placeholder="Provide a detailed description..."
                          disabled={
                            submitMutation.isPending || updateMutation.isPending
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Attachment Upload */}
                <div className="space-y-2">
                  <FormLabel>Attachment (Optional)</FormLabel>
                  {!attachedFile ? (
                    <div className="relative p-6 border-2 hover:border-primary/50 border-border border-dashed rounded-lg text-center transition-colors cursor-pointer">
                      <Upload className="mx-auto mb-2 w-8 h-8 text-muted-foreground" />
                      <p className="mb-2 text-muted-foreground text-sm">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-muted-foreground text-xs">
                        PNG, JPG, PDF up to 10MB
                      </p>
                      <input
                        type="file"
                        accept="image/*,application/pdf"
                        onChange={(e) => handleImageUpload(e.target.files)}
                        className="absolute inset-0 opacity-0 w-full h-full cursor-pointer"
                        disabled={
                          submitMutation.isPending || updateMutation.isPending
                        }
                      />
                    </div>
                  ) : (
                    <div className="flex justify-between items-center bg-muted p-3 rounded-lg">
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">{attachedFile.name}</span>
                        <span className="text-muted-foreground text-xs">
                          ({(attachedFile.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => setAttachedFile(null)}
                        disabled={
                          submitMutation.isPending || updateMutation.isPending
                        }
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  )}

                  {/* Image Preview Grid */}
                  {imagePreviewUrls && imagePreviewUrls.length > 0 && (
                    <div className="gap-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 mt-4">
                      {imagePreviewUrls.map((url, imgIndex) => (
                        <div key={imgIndex} className="group relative">
                          <img
                            src={url}
                            alt={`Evidence ${imgIndex + 1}`}
                            className="border border-gray-200 rounded-lg w-full h-24 object-cover"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="top-1 right-1 absolute opacity-0 group-hover:opacity-100 p-0 w-6 h-6 transition-opacity"
                            onClick={() => removeImage(imgIndex)}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                          <div className="bottom-1 left-1 absolute bg-black bg-opacity-50 px-1 py-0.5 rounded text-white text-xs">
                            {uploadedImages[imgIndex]?.name.slice(0, 10)}
                            ...
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Upload Status */}
                  {uploadedImages && uploadedImages.length > 0 && (
                    <div className="flex items-center space-x-2 text-gray-600 text-sm">
                      <FileImage className="w-4 h-4" />
                      <span>{uploadedImages.length} image(s) uploaded</span>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate("/")}
                    className="flex-1"
                    disabled={
                      submitMutation.isPending || updateMutation.isPending
                    }
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground"
                    disabled={
                      submitMutation.isPending || updateMutation.isPending
                    }
                  >
                    {submitMutation.isPending || updateMutation.isPending
                      ? "Submitting..."
                      : "Submit Report"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
