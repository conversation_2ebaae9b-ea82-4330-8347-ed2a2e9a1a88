import React, { useState, useRef } from "react";
import { <PERSON><PERSON><PERSON>, Marker, Autocomplete } from "@react-google-maps/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Star } from "lucide-react";
import { useDateFormatter } from "@/hooks/useLocalizationSettings";

const containerStyle = {
  width: "100%",
  height: "400px",
};

const defaultCenter = {
  lat: 40.7128,
  lng: -74.006, // Default to New York City
};

interface MapLocationPickerProps {
  onLocationSelect: (location: {
    lat: number;
    lng: number;
    address?: string;
    placeDetails?: google.maps.places.PlaceResult;
  }) => void;
  onCancel: () => void;
  initialLocation?: { lat: number; lng: number };
}

export function MapLocationPicker({
  onLocationSelect,
  onCancel,
  initialLocation,
}: MapLocationPickerProps) {
  const { formatDate } = useDateFormatter();
  const [selectedLocation, setSelectedLocation] = useState<{
    lat: number;
    lng: number;
  } | null>(initialLocation || null);
  const [isLoadingAddress, setIsLoadingAddress] = useState(false);
  const [mapCenter, setMapCenter] = useState(initialLocation || defaultCenter);
  const [mapZoom, setMapZoom] = useState(13);
  const [placeDetails, setPlaceDetails] =
    useState<google.maps.places.PlaceResult | null>(null);
  const [isLoadingPlaceDetails, setIsLoadingPlaceDetails] = useState(false);

  const geocoderRef = useRef<google.maps.Geocoder | null>(null);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const mapRef = useRef<google.maps.Map | null>(null);
  const placesServiceRef = useRef<google.maps.places.PlacesService | null>(
    null
  );

  // Initialize services
  React.useEffect(() => {
    if (!geocoderRef.current) {
      geocoderRef.current = new google.maps.Geocoder();
    }
  }, []);

  const initializePlacesService = () => {
    if (mapRef.current && !placesServiceRef.current) {
      placesServiceRef.current = new google.maps.places.PlacesService(
        mapRef.current
      );
    }
  };

  const fetchPlaceDetails = (placeId: string) => {
    if (!placesServiceRef.current) return;

    setIsLoadingPlaceDetails(true);
    placesServiceRef.current.getDetails(
      {
        placeId,
        fields: [
          "name",
          "rating",
          "reviews",
          "formatted_address",
          "geometry",
          "price_level",
          "opening_hours",
          "user_ratings_total",
        ],
      },
      (place, status) => {
        setIsLoadingPlaceDetails(false);
        if (status === google.maps.places.PlacesServiceStatus.OK && place) {
          setPlaceDetails(place);
        } else {
          setPlaceDetails(null);
        }
      }
    );
  };

  const handleMapClick = (event: google.maps.MapMouseEvent) => {
    if (event.latLng) {
      const lat = event.latLng.lat();
      const lng = event.latLng.lng();
      setSelectedLocation({ lat, lng });
      // Clear place details when manually selecting a location
      setPlaceDetails(null);
    }
  };

  const handlePlaceChanged = () => {
    if (autocompleteRef.current) {
      const place = autocompleteRef.current.getPlace();
      console.log("Place from autocomplete:", place);
      console.log("Place ID:", place.place_id);
      console.log("Initial rating:", place.rating);
      console.log("Initial reviews:", place.reviews);

      if (place.geometry && place.geometry.location) {
        const lat = place.geometry.location.lat();
        const lng = place.geometry.location.lng();
        const newLocation = { lat, lng };

        // Update map center and zoom
        setMapCenter(newLocation);
        setMapZoom(15);

        // Set the selected location
        setSelectedLocation(newLocation);

        // Set basic place details from autocomplete
        setPlaceDetails(place);

        // Fetch more detailed information if place_id is available
        if (place.place_id) {
          fetchPlaceDetails(place.place_id);
        }

        // Pan the map to the new location
        if (mapRef.current) {
          mapRef.current.panTo(newLocation);
          mapRef.current.setZoom(15);
        }
      }
    }
  };

  const reverseGeocode = async (lat: number, lng: number): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (!geocoderRef.current) {
        reject("Geocoder not initialized");
        return;
      }

      geocoderRef.current.geocode(
        { location: { lat, lng } },
        (results, status) => {
          if (status === "OK" && results && results[0]) {
            resolve(results[0].formatted_address);
          } else {
            reject("Address not found");
          }
        }
      );
    });
  };

  const handleConfirm = async () => {
    if (!selectedLocation) return;

    setIsLoadingAddress(true);
    try {
      let address = placeDetails?.formatted_address;
      if (!address) {
        address = await reverseGeocode(
          selectedLocation.lat,
          selectedLocation.lng
        );
      }

      onLocationSelect({
        ...selectedLocation,
        address,
        placeDetails: placeDetails || undefined,
      });
    } catch (error) {
      // If reverse geocoding fails, still return the coordinates
      onLocationSelect({
        ...selectedLocation,
        placeDetails: placeDetails || undefined,
      });
    } finally {
      setIsLoadingAddress(false);
    }
  };

  const onMapLoad = (map: google.maps.Map) => {
    mapRef.current = map;
    initializePlacesService();
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="fill-yellow-400 w-4 h-4 text-yellow-400" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Star
          key="half"
          className="fill-yellow-400/50 w-4 h-4 text-yellow-400"
        />
      );
    }

    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-4 h-4 text-gray-300" />);
    }

    return stars;
  };

  return (
    <div className="space-y-4 bg-primary-bg">
      {/* Search Input */}
      <div className="space-y-2">
        <label className="font-medium text-slate-700 text-sm">
          Search for a location
        </label>
        <Autocomplete
          onLoad={(autocomplete) => {
            autocompleteRef.current = autocomplete;
          }}
          onPlaceChanged={handlePlaceChanged}
          options={{
            componentRestrictions: { country: [] },
            fields: [
              "geometry",
              "formatted_address",
              "name",
              "place_id",
              "rating",
              "reviews",
            ],
          }}
        >
          <div className="relative">
            <Search className="top-1/2 left-3 absolute w-4 h-4 text-gray-400 -translate-y-1/2 transform" />
            <Input
              type="text"
              placeholder="Type an address or place name..."
              className="pl-10 border-slate-200"
            />
          </div>
        </Autocomplete>
      </div>

      <div className="text-slate-600 text-sm">
        Search for a location above, then click on the map to fine-tune the
        position
      </div>

      <GoogleMap
        mapContainerStyle={containerStyle}
        center={mapCenter}
        zoom={mapZoom}
        onClick={handleMapClick}
        onLoad={onMapLoad}
        options={{
          streetViewControl: false,
          mapTypeControl: true,
          fullscreenControl: false,
          zoomControl: true,
        }}
      >
        {selectedLocation && (
          <Marker
            position={selectedLocation}
            draggable={true}
            onDragEnd={(event) => {
              if (event.latLng) {
                const lat = event.latLng.lat();
                const lng = event.latLng.lng();
                setSelectedLocation({ lat, lng });
                // Clear place details when dragging marker to new location
                setPlaceDetails(null);
              }
            }}
            title="Selected Location (drag to adjust)"
          />
        )}
      </GoogleMap>

      {/* Place Details Section */}
      {(placeDetails || isLoadingPlaceDetails) && (
        <div className="space-y-3 p-4 border rounded-lg">
          <h3 className="font-semibold text-slate-800">Place Information</h3>

          {isLoadingPlaceDetails ? (
            <div className="text-slate-600 text-sm">
              Loading place details...
            </div>
          ) : placeDetails ? (
            <div className="space-y-3">
              {placeDetails.name && (
                <div>
                  <span className="font-medium text-slate-700">Name: </span>
                  <span className="text-slate-600">{placeDetails.name}</span>
                </div>
              )}

              {placeDetails.rating && (
                <div className="flex items-center gap-2">
                  <span className="font-medium text-slate-700">Rating: </span>
                  <div className="flex items-center gap-1">
                    {renderStars(placeDetails.rating)}
                    <span className="ml-1 text-slate-600">
                      {placeDetails.rating} (
                      {placeDetails.user_ratings_total || 0} reviews)
                    </span>
                  </div>
                </div>
              )}

              {placeDetails.formatted_address && (
                <div>
                  <span className="font-medium text-slate-700">Address: </span>
                  <span className="text-slate-600">
                    {placeDetails.formatted_address}
                  </span>
                </div>
              )}

              {placeDetails.opening_hours && (
                <div>
                  <span className="font-medium text-slate-700">Status: </span>
                  <span
                    className={`text-sm ${
                      placeDetails.opening_hours.open_now
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {placeDetails.opening_hours.open_now
                      ? "Open now"
                      : "Closed"}
                  </span>
                </div>
              )}

              {placeDetails.price_level !== undefined && (
                <div>
                  <span className="font-medium text-slate-700">
                    Price Level:{" "}
                  </span>
                  <span className="text-slate-600">
                    {"$".repeat(placeDetails.price_level || 1)} (
                    {placeDetails.price_level === 0
                      ? "Free"
                      : placeDetails.price_level === 1
                      ? "Inexpensive"
                      : placeDetails.price_level === 2
                      ? "Moderate"
                      : placeDetails.price_level === 3
                      ? "Expensive"
                      : "Very Expensive"}
                    )
                  </span>
                </div>
              )}

              {placeDetails.reviews && placeDetails.reviews.length > 0 && (
                <div>
                  <span className="font-medium text-slate-700">
                    Recent Reviews:
                  </span>
                  <div className="space-y-3 mt-2 max-h-60 overflow-y-auto">
                    {placeDetails.reviews.slice(0, 3).map((review, index) => (
                      <div
                        key={index}
                        className="p-3 border border-slate-200 rounded"
                      >
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium text-slate-800 text-sm">
                            {review.author_name}
                          </span>
                          <div className="flex items-center gap-1">
                            {renderStars(review.rating)}
                            <span className="ml-1 text-slate-500 text-sm">
                              {review.rating}
                            </span>
                          </div>
                        </div>
                        <p className="text-slate-600 text-sm leading-relaxed">
                          {review.text}
                        </p>
                        {review.time && (
                          <p className="mt-1 text-slate-400 text-xs">
                            {formatDate(new Date(review.time * 1000))}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-slate-600 text-sm">
              No place details available for this location.
            </div>
          )}
        </div>
      )}

      <div className="flex justify-between items-center space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          className="px-6"
        >
          Cancel
        </Button>

        <Button
          type="button"
          onClick={handleConfirm}
          disabled={!selectedLocation || isLoadingAddress}
          className="px-6"
        >
          {isLoadingAddress ? "Getting Address..." : "Confirm Location"}
        </Button>
      </div>

      {selectedLocation && (
        <div className="p-3 rounded text-slate-600 text-sm">
          <strong>Selected coordinates:</strong>{" "}
          {selectedLocation.lat.toFixed(6)}, {selectedLocation.lng.toFixed(6)}
        </div>
      )}
    </div>
  );
}
