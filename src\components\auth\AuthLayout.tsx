import React from "react";

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout = ({ children }: AuthLayoutProps) => {
  return (
    <div className="flex flex-col justify-center sm:px-6 lg:px-8 py-12 min-h-screen">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="flex items-center space-x-2">
            <img
              src="/assets/images/logo.png"
              alt="logo"
              className="w-auto h-10"
            />
          </div>
        </div>
      </div>

      <div className="sm:mx-auto mt-8 sm:w-full sm:max-w-md">
        <div className="shadow-sm px-4 sm:px-10 py-8 border border-gray-100 rounded-lg bg-white">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
