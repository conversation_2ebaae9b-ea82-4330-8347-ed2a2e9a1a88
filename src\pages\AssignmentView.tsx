import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { fetchAssignmentById } from "@/data/assignments";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { ChevronLeft } from "lucide-react";
import { Link, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Logo from "@/components/Logo";

const AssignmentView = () => {
  const { t } = useTranslation();
  const { assignmentId } = useParams<{ assignmentId: string }>();
  const queryClient = useQueryClient();
  // const [isPublished, setIsPublished] = useState(false);

  // Query to fetch assignment data
  const {
    data: assignment,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["assignment", assignmentId],
    queryFn: () => fetchAssignmentById(assignmentId!),
    enabled: !!assignmentId,
  });
  if (isLoading) {
    return (
      <div className="w-full min-h-screen">
        {/* Header */}
        <div className="bg-secondary-bg px-6 py-4 border-b">
          <div className="flex justify-between items-center mx-auto animate-pulse">
            <div className="flex items-center space-x-4">
              <div className="bg-slate-200 rounded-full w-8 h-8" />
              <div className="bg-slate-200 rounded w-40 h-4" />
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-slate-200 rounded w-6 h-4" />
              <div className="bg-slate-200 rounded-full w-8 h-8" />
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="mx-auto p-6 animate-pulse">
          <div className="flex items-center space-x-4 mb-6">
            <div className="bg-slate-200 rounded w-8 h-8" />
          </div>

          {/* Skeleton Card Header */}
          <Card className="space-y-4 mb-6 p-6">
            <div className="gap-6 grid grid-cols-1 md:grid-cols-3">
              <div>
                <div className="bg-slate-300 mb-2 rounded w-32 h-4" />
                <div className="bg-slate-200 rounded w-full h-5" />
              </div>
              <div>
                <div className="bg-slate-300 mb-2 rounded w-32 h-4" />
                <div className="bg-slate-200 rounded w-full h-5" />
              </div>
            </div>
          </Card>

          {/* Skeleton Sections */}
          <div className="space-y-6">
            {Array.from({ length: 2 }).map((_, i) => (
              <Card key={i} className="space-y-4 p-6">
                <div className="flex justify-between items-center mb-4">
                  <div className="bg-slate-200 rounded w-1/3 h-5" />
                  <div className="bg-slate-200 rounded-full w-4 h-4" />
                </div>
                <ul className="space-y-2">
                  <li className="bg-slate-200 rounded w-3/4 h-4" />
                  <li className="bg-slate-200 rounded w-2/3 h-4" />
                </ul>
                <div className="bg-slate-100 rounded w-full h-24" />
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="w-full min-h-screen">
        <div className="bg-secondary-bg px-6 py-4 border-b">
          <div className="flex justify-between items-center mx-auto">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-gray-600 text-sm">
                <Link to="/assignments" className="hover:text-gray-900">
                  {t("global.assignments")}
                </Link>
                <span>{">"}</span>
                <span>{t("assignmentView.assignmentView")}</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <span className="text-gray-600">🔔</span>
              </Button>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600 text-sm">🇺🇸</span>
                <div className="flex justify-center items-center bg-orange-400 rounded-full w-8 h-8">
                  <span className="font-medium text-white text-sm">A</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <p className="mb-2 text-red-600">
              {t("assignmentView.errorLoading")}
            </p>
            <p className="text-gray-600 text-sm">
              {error instanceof Error
                ? error.message
                : t("global.unknownError")}
            </p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() =>
                queryClient.refetchQueries({
                  queryKey: ["assignment", assignmentId],
                })
              }
            >
              {t("global.retry")}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!assignment) {
    return (
      <div className="w-full min-h-screen">
        <div className="bg-secondary-bg px-6 py-4 border-b">
          <div className="flex justify-between items-center mx-auto">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-gray-600 text-sm">
                <Link to="/assignments" className="hover:text-gray-900">
                  {t("global.assignments")}
                </Link>
                <span>{">"}</span>
                <span>{t("assignmentView.assignmentView")}</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <span className="text-gray-600">🔔</span>
              </Button>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600 text-sm">🇺🇸</span>
                <div className="flex justify-center items-center bg-orange-400 rounded-full w-8 h-8">
                  <span className="font-medium text-white text-sm">A</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-center items-center min-h-96">
          <p className="text-gray-600">
            {t("assignmentView.assignmentNotFound")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen">
      {/* Main Content */}
      <div className="mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <Link to="/assignments">
            <Button variant="ghost" size="sm">
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </Link>
          <Link to={`/assignments/${assignment.id}/edit`}>
            <Button variant="outline">{t("global.edit")}</Button>
          </Link>
        </div>

        {/* Assignment Header */}
        <Card className="mb-6 p-6">
          <div className="gap-6 grid grid-cols-1 md:grid-cols-3">
            <div>
              <label className="block mb-1 font-medium text-gray-700 text-sm">
                {t("assignment.templateName")}
              </label>
              <p className="text-gray-900">{assignment.templateTitle}</p>
            </div>
            <div>
              <label className="block mb-1 font-medium text-gray-700 text-sm">
                {t("templateView.industryType")}
              </label>
              <p className="text-gray-900">{assignment.businessType}</p>
            </div>
            {/* <div>
              <div className="flex justify-between items-center">
                <label className="font-medium text-gray-700 text-sm">
                  Status:
                </label>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-600 text-sm">Draft</span>
                  <Switch
                    checked={isPublished}
                    onCheckedChange={handleStatusChange}
                    disabled={updateAssignmentMutation.isPending}
                  />
                  <span className="font-medium text-gray-900 text-sm">
                    Published
                  </span>
                  {updateAssignmentMutation.isPending && (
                    <Loader2 className="ml-2 w-4 h-4 animate-spin" />
                  )}
                </div>
              </div>
            </div> */}
          </div>
        </Card>

        {/* Assignment Sections */}
        <div className="space-y-6">
          {assignment.sections.map((section) => (
            <Card key={section.id} className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-medium text-gray-900 text-lg">
                  {section.name}
                </h3>
              </div>

              {/* List Type Questions */}
              {section.fields && (
                <ol className="space-y-2 mb-4">
                  {section.fields.map((field, index) => (
                    <li key={index} className="flex flex-col items-start gap-3">
                      <span className="text-gray-600">
                        {index + 1}.{" "}
                        <span className="text-gray-900">
                          {field.question}{" "}
                          {field.preferredAnswer && (
                            <span className="text-gray-600">
                              {t("templateView.hasPreferredAnswer")}
                            </span>
                          )}
                        </span>
                      </span>
                      <span></span>
                      {/* Textarea Type */}
                      {field.questionType === "Text" && (
                        <Textarea
                          className="w-full h-32 resize-none pointer-events-none"
                          value={field.preferredAnswer || ""}
                        />
                      )}

                      {/* YES/NO Type */}
                      {field.questionType === "YES/NO" && (
                        <div className="flex items-center gap-6">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              checked={field.preferredAnswer === "YES"}
                              className="pointer-events-none"
                            />
                            <label className="text-sm">{t("global.yes")}</label>
                          </div>
                          <div className="flex items-center gap-2">
                            <Checkbox
                              checked={field.preferredAnswer === "NO"}
                              className="pointer-events-none"
                            />
                            <label className="text-sm">{t("global.no")}</label>
                          </div>
                        </div>
                      )}
                    </li>
                  ))}
                </ol>
              )}
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AssignmentView;
