import React, { useEffect, useState } from "react";
import { Navigate, useLocation, useNavigate } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import { canUserAccess, type Feature } from "@/data/permissions";
import { Roles } from "@/lib/types";
import { getRequiredPermission } from "@/lib/permissions";
import { useCurrentUser } from "@/hooks/useCurrentUser";

interface PermissionWrapperProps {
  children: React.ReactNode;
  requiredPermission: Feature;
  fallbackPath?: string;
  showForbidden?: boolean;
}

// Forbidden component
const ForbiddenPage: React.FC<{ requiredPermission: Feature }> = ({
  requiredPermission,
}) => (
  <div className="flex justify-center items-center min-h-screen">
    <div className="shadow-lg p-8 rounded-lg w-full max-w-md text-center">
      <div className="mb-6">
        <div className="flex justify-center items-center bg-red-100 mx-auto mb-4 rounded-full w-16 h-16">
          <svg
            className="w-8 h-8 text-red-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <h1 className="mb-2 font-bold text-gray-900 text-2xl">
          Access Forbidden
        </h1>
        <p className="mb-4 text-gray-600">
          You don't have permission to access this page.
        </p>
        <p className="mb-6 text-gray-500 text-sm">
          Required permission:{" "}
          <span className="bg-gray-100 px-2 py-1 rounded font-mono">
            {requiredPermission}
          </span>
        </p>
      </div>
      <div className="space-y-3">
        <button
          onClick={() => window.history.back()}
          className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg w-full text-white transition-colors"
        >
          Go Back
        </button>
        <button
          onClick={() => (window.location.href = "/assignments")}
          className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg w-full text-white transition-colors"
        >
          Go to Assignments
        </button>
      </div>
    </div>
  </div>
);

export const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  requiredPermission,
  fallbackPath = "/assignments",
  showForbidden = true,
}) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userRole, setUserRole] = useState<Roles | null>(null);

  useEffect(() => {
    const checkPermission = async () => {
      try {
        // Get user session
        const { data: sessionData } = await supabase.auth.getSession();
        const role = sessionData.session?.user.user_metadata?.role as Roles;

        if (!role) {
          setHasPermission(false);
          setIsLoading(false);
          return;
        }

        setUserRole(role);

        // Check if user has the required permission
        const canAccess = await canUserAccess(requiredPermission);
        setHasPermission(canAccess);
      } catch (error) {
        console.error("Error checking permission:", error);
        setHasPermission(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkPermission();
  }, [requiredPermission]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="mx-auto mb-4 border-b-2 border-blue-600 rounded-full w-8 h-8 animate-spin"></div>
          <p className="text-gray-600">Checking permissions...</p>
        </div>
      </div>
    );
  }

  // No permission
  if (hasPermission === false) {
    if (showForbidden) {
      return <ForbiddenPage requiredPermission={requiredPermission} />;
    } else {
      return <Navigate to={fallbackPath} replace />;
    }
  }

  // Has permission
  return <>{children}</>;
};

// Route-based permission wrapper
export const RoutePermissionWrapper: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const requiredPermission = getRequiredPermission(location.pathname);

  // Import the hook at the top of the file, then use it here
  const { data: currentUser, isLoading: userLoading } = useCurrentUser();

  // Paths that don't require email verification
  const emailVerificationExemptPaths = [
    "/auth",
    "/email-verification-pending",
    "/verify",
  ];

  // Check if current path is exempt from email verification
  const isExemptFromVerification = emailVerificationExemptPaths.some((path) =>
    location.pathname.startsWith(path)
  );

  // If we're loading user data, show loading state
  if (userLoading && !isExemptFromVerification) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="border-primary border-b-2 rounded-full w-32 h-32 animate-spin"></div>
      </div>
    );
  }

  // If user is not verified and not on an exempt path, redirect to verification pending
  if (currentUser && !currentUser.isVerified && !isExemptFromVerification) {
    return <Navigate to="/email-verification-pending" replace />;
  }

  // If no permission required for this route, render normally
  if (!requiredPermission) {
    return <>{children}</>;
  }

  return (
    <PermissionWrapper
      requiredPermission={requiredPermission}
      showForbidden={false}
      fallbackPath={location.pathname === "/" ? "/assignments" : "/assignments"}
    >
      {children}
    </PermissionWrapper>
  );
};
