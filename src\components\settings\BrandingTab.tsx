import React, { useRef, useState, useEffect } from "react";
import { Upload, RotateCcw, X, Image as ImageIcon } from "lucide-react";
import { ColorPicker } from "../ui/color-picker";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  fetchDefaultBrandingSettings,
  fetchCurrentUserBrandingSettings,
  updateCurrentUserBrandingSettings,
  applyDefaultBrandingSettings,
} from "@/data/settings";
import { uploadImageToSupabase } from "@/data/media";
import { BrandingSettings } from "@/lib/types";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

export const BrandingTab: React.FC = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const debounceRef = useRef<NodeJS.Timeout>();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Local state for textarea to provide immediate feedback
  const [localLoginMessage, setLocalLoginMessage] = useState("");
  const [logoUploading, setLogoUploading] = useState(false);

  // Fetch user's current branding settings with fallback to defaults
  const {
    data: currentSettings,
    isLoading,
    error: settingsError,
  } = useQuery({
    queryKey: ["currentUserBrandingSettings"],
    queryFn: fetchCurrentUserBrandingSettings,
  });

  // Sync local state with settings when they load
  useEffect(() => {
    if (currentSettings?.loginMessage !== undefined) {
      setLocalLoginMessage(currentSettings.loginMessage);
    }
  }, [currentSettings?.loginMessage]);

  // Mutation to update branding settings
  const updateMutation = useMutation({
    mutationFn: updateCurrentUserBrandingSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["currentUserBrandingSettings"],
      });
      toast.success(t("settings.branding.settingsUpdated"));
      // Inform user about refresh requirement
      toast.info(
        t(
          "settings.branding.refreshRequired",
          "Please refresh the page to see branding changes"
        )
      );
    },
    onError: (error) => {
      toast.error(t("settings.branding.updateFailed") + ": " + error);
    },
  });

  // Mutation to apply default settings
  const applyDefaultsMutation = useMutation({
    mutationFn: applyDefaultBrandingSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["currentUserBrandingSettings"],
      });
      toast.success(t("settings.branding.defaultsApplied"));
      toast.info(
        t(
          "settings.branding.refreshRequired",
          "Please refresh the page to see branding changes"
        )
      );
    },
    onError: (error) => {
      toast.error(t("settings.branding.defaultsFailed") + ": " + error);
    },
  });

  // Handle save settings
  const handleSaveSettings = (settings: BrandingSettings) => {
    updateMutation.mutate(settings);
  };

  // Handle reset to defaults
  const handleResetToDefaults = () => {
    applyDefaultsMutation.mutate();
  };

  // Handle logo upload
  const handleLogoUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLogoUploading(true);
    try {
      const uploadResult = await uploadImageToSupabase({
        file,
        folder: "logos",
        isLogo: true,
      });

      if (uploadResult.success && uploadResult.url) {
        const newSettings = {
          ...currentSettings,
          logoUrl: uploadResult.url,
        } as BrandingSettings;
        handleSaveSettings(newSettings);
        toast.success(
          t("settings.branding.logoUploaded", "Logo uploaded successfully")
        );
      } else {
        toast.error(
          uploadResult.error ||
            t("settings.branding.logoUploadFailed", "Failed to upload logo")
        );
      }
    } catch (error) {
      toast.error(
        t("settings.branding.logoUploadFailed", "Failed to upload logo")
      );
    } finally {
      setLogoUploading(false);
      // Reset file input
      if (event.target) {
        event.target.value = "";
      }
    }
  };

  // Handle remove logo
  const handleRemoveLogo = () => {
    if (!currentSettings) return;
    const newSettings = { ...currentSettings, logoUrl: "" };
    handleSaveSettings(newSettings);
  };

  // Handle field changes with debouncing (for non-text inputs)
  const handleFieldChange = (field: keyof BrandingSettings, value: string) => {
    if (!currentSettings) return; // Don't save if no settings loaded yet

    const newSettings = { ...currentSettings, [field]: value };

    // Debounce the save operation
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    debounceRef.current = setTimeout(() => {
      handleSaveSettings(newSettings);
    }, 1000);
  };

  // Handle text field changes without debouncing (save on blur)
  const handleTextFieldBlur = (
    field: keyof BrandingSettings,
    value: string
  ) => {
    if (!currentSettings) return;

    const newSettings = { ...currentSettings, [field]: value };
    handleSaveSettings(newSettings);
  };

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
            {t("settings.branding.title")}
          </h2>
        </div>
        <div className="flex justify-center items-center py-12">
          <div className="border-primary border-b-2 rounded-full w-8 h-8 animate-spin"></div>
          <span className="ml-3 text-gray-600">
            {t("settings.branding.loadingSettings")}
          </span>
        </div>
      </div>
    );
  }

  if (!currentSettings) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
            {t("settings.branding.title")}
          </h2>
        </div>
        <div className="flex justify-center items-center py-12">
          <span className="text-gray-600">
            {t("settings.branding.noSettingsAvailable")}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header with Reset Button */}
      <div className="flex justify-between items-center">
        <h2 className="mb-2 font-satoshi font-bold text-black-text text-xl">
          {t("settings.branding.title")}
        </h2>
        <button
          onClick={handleResetToDefaults}
          disabled={applyDefaultsMutation.isPending}
          className="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 px-4 py-2 border border-gray-300 rounded-lg transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          <span className="font-medium text-sm">
            {applyDefaultsMutation.isPending
              ? t("settings.branding.resetting")
              : t("settings.branding.resetToDefaults")}
          </span>
        </button>
      </div>

      {/* Logo Upload */}
      <div className="space-y-4">
        <div>
          <h3 className="mb-1 font-satoshi font-bold text-black-text text-lg">
            {t("settings.branding.logo")}
          </h3>
        </div>

        {/* Current Logo Preview */}
        {currentSettings.logoUrl && (
          <div className="flex items-center gap-4 p-4 rounded-xl">
            <img
              src={currentSettings.logoUrl}
              alt="Current logo"
              className="w-auto h-16 object-contain"
            />
            <div className="flex-1">
              <p className="font-medium text-black-text text-sm">
                {t("settings.branding.currentLogo", "Current Logo")}
              </p>
            </div>
            <button
              onClick={handleRemoveLogo}
              className="p-2 text-gray-500 hover:text-red-500 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleLogoUpload}
          accept="image/*"
          className="hidden"
        />
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={logoUploading}
          className="flex items-center gap-2 disabled:opacity-50 px-4 py-2.5 border border-field-stroke rounded-full transition-colors disabled:cursor-not-allowed hover:"
        >
          <Upload className="w-5 h-5 text-black-text" />
          <span className="font-medium text-black-text">
            {logoUploading
              ? t("settings.branding.uploading", "Uploading...")
              : currentSettings.logoUrl
              ? t("settings.branding.changeLogo", "Change Logo")
              : t("settings.branding.uploadLogo", "Upload Logo")}
          </span>
        </button>
      </div>

      {/* Primary Color */}
      <div className="space-y-4">
        <div>
          <h3 className="mb-1 font-satoshi font-bold text-black-text text-lg">
            {t("settings.branding.primaryColor")}
          </h3>
          <p className="text-gray-text text-sm">
            {t("settings.branding.primaryColorDescription")}
          </p>
        </div>
        <ColorPicker
          value={currentSettings.primaryColor}
          onChange={(color) => {
            const newSettings = {
              ...currentSettings,
              primaryColor: color,
            } as BrandingSettings;
            handleSaveSettings(newSettings);
          }}
        />
      </div>

      {/* Secondary Color */}
      <div className="space-y-4">
        <div>
          <h3 className="mb-1 font-satoshi font-bold text-black-text text-lg">
            {t("settings.branding.secondaryColor")}
          </h3>
          <p className="text-gray-text text-sm">
            {t("settings.branding.secondaryColorDescription")}
          </p>
        </div>
        <ColorPicker
          value={currentSettings.secondaryColor}
          onChange={(value) => handleFieldChange("secondaryColor", value)}
        />
      </div>

      {/* Login Screen Message */}
      <div className="space-y-4">
        <div>
          <h3 className="font-satoshi font-bold text-black-text text-lg">
            {t("settings.branding.loginScreenMessage")}
          </h3>
        </div>
        <textarea
          value={localLoginMessage}
          onChange={(e) => setLocalLoginMessage(e.target.value)}
          onBlur={(e) => handleTextFieldBlur("loginMessage", e.target.value)}
          placeholder={t("settings.branding.loginMessagePlaceholder")}
          className="px-4 py-3 border border-field-stroke rounded-xl focus:outline-none focus:ring-2 focus:ring-ring w-full h-20 text-black-text placeholder:text-gray-text resize-none"
        />
      </div>
    </div>
  );
};
