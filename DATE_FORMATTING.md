# Date Formatting Implementation

This implementation ensures that all dates displayed in the frontend respect the user's localization settings, specifically the `dateFormat` setting.

## How it works

1. **User Settings**: Users can set their preferred date format in Settings > Localization > Date Format
2. **Available Formats**:

   - `dd-mm-yyyy` (e.g., 31-12-2023)
   - `mm-dd-yyyy` (e.g., 12-31-2023)
   - `yyyy-mm-dd` (e.g., 2023-12-31)

3. **Implementation**: The system automatically applies the user's chosen format to all dates throughout the application.

## Key Files Modified

### 1. `/frontend/src/hooks/useLocalizationSettings.ts` (NEW)

- Central hook for accessing user's localization settings
- Provides `useDateFormatter()` hook for components
- Maps backend date formats to date-fns formats
- Handles fallbacks when settings are not available

### 2. Components Updated

- `AssignChecklistForm.tsx` - Date picker displays
- `EditChecklistForm.tsx` - Date picker displays
- `EditAssignment.tsx` - Start and due date displays
- `EventModal.tsx` - Event date displays
- `Calendar.tsx` - Calendar month/year header
- `NotificationsPage.tsx` - Notification timestamps
- `AssignmentResponseHistory.tsx` - Submission dates
- `NewBranchForm.tsx` - Review timestamps

## Usage Examples

### In React Components

```tsx
import { useDateFormatter } from "@/hooks/useLocalizationSettings";

function MyComponent() {
  const formatDate = useDateFormatter();

  return (
    <div>
      <p>Date: {formatDate(new Date())}</p>
      <p>String Date: {formatDate("2023-12-31")}</p>
    </div>
  );
}
```

### For Settings Access

```tsx
import { useLocalizationSettings } from "@/hooks/useLocalizationSettings";

function SettingsComponent() {
  const { settings, isLoading } = useLocalizationSettings();

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      <p>Date Format: {settings?.dateFormat}</p>
      <p>Language: {settings?.language}</p>
      <p>Time Zone: {settings?.timeZone}</p>
    </div>
  );
}
```

### Outside React Components

```tsx
import { formatDateWithPattern } from "@/lib/utils";

// Use when you need to format dates outside of React components
const formattedDate = formatDateWithPattern(new Date(), "MM/dd/yyyy");
```

## Backend Integration

The system fetches user settings from these API endpoints:

- `GET /api/settings/localization/{userId}` - User's current settings
- `GET /api/settings/defaults/localization` - Default fallback settings
- `POST /api/settings/localization` - Create user settings
- `PATCH /api/settings/localization` - Update user settings

## Testing the Implementation

1. Go to Settings > Localization
2. Change the Date Format setting (e.g., from MM-DD-YYYY to DD-MM-YYYY)
3. Navigate to any page with dates (Calendar, Assignments, etc.)
4. Verify dates display in the new format
5. Test with different formats to ensure consistency

## Fallback Behavior

- If user has no custom settings: Uses default system settings
- If settings fail to load: Uses MM/dd/yyyy format
- If date parsing fails: Falls back to browser's default formatting
- Settings are cached via React Query for performance

## Benefits

- ✅ Consistent date formatting across the entire application
- ✅ Respects user preferences and localization
- ✅ Centralized configuration and easy maintenance
- ✅ Proper fallbacks for error scenarios
- ✅ Performance optimized with caching
- ✅ TypeScript support with proper typing
