import { supabase } from "@/integrations/supabase/client";
import { fetchWithToken } from "@/lib/fetchWithToken";
import { CreateChecklistResponseInput } from "@/lib/types";

export async function createChecklistAssignmentResponse(
  formData: CreateChecklistResponseInput
) {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/responses`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(formData),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to create assignment");
  }

  const responseData = await response.json();
  return responseData.data;
}

export async function updateChecklistAssignmentResponse(
  responseId: string,
  formData: CreateChecklistResponseInput
) {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/responses/${responseId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(formData),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to update assignment response");
  }

  const responseData = await response.json();
  return responseData.data;
}

// Add this function to your response.ts file
export async function updateResponseFieldWithMedia(
  responseFieldId: string,
  mediaId: string
) {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/responses/fields/${responseFieldId}/media`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        mediaId,
      }),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to update response field with media");
  }

  return response.json();
}

// Get response by ID
export async function fetchResponseById(responseId: string) {
  console.log("Fetching response by ID:", responseId);
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/responses/${responseId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to get response by ID");
  }

  const responseData = await response.json();
  console.log("Response Data:", responseData);
  return responseData.data;
}
// Get response by Assignment ID
export async function fetchResponseByAssignmentId(assignmentId: string) {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/responses/assignment/${assignmentId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
  console.log(response);

  if (!response.ok) {
    throw new Error("Failed to get response by ID");
  }

  const responseData = await response.json();
  console.log("Response Data:", responseData);
  return responseData.data;
}

// Get status of a response submission
export async function fetchResponseSubmissionStatus(responseId: string) {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/response-submission/status/${responseId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to get response submission status");
  }

  const responseData = await response.json();
  console.log("Response Data:", responseData);
  return responseData.data;
}

interface ScoreSummary {
  passed: boolean;
  percentage: number;
  totalPossibleScore: number;
  totalEarnedScore: number;
  fieldBreakdown: ({
    fieldId: string;
    score: number;
    status: string;
  } | null)[];
}
// Get score summary of a template
export async function fetchScoreSummary(
  responseId: string
): Promise<ScoreSummary> {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/response-submission/score-summary/${responseId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to get score summary");
  }

  const responseData = await response.json();
  console.log("Response Data:", responseData);
  return responseData.data;
}

interface FetchAllResponsesParams {
  status?: string;
  page?: number;
  limit?: number;
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
  branchId?: string;
  templateName?: string;
}

interface ResponsesPaginated {
  result: {
    userId: string;
    id: string;
    createdAt: string;
    updatedAt: string;
    latitude: string | null;
    longitude: string | null;
    branchId: string;
    checklistAssignmentsId: string;
    score: string | null;
    completedAt: string | null;
    submittedAt?: string | null;
    assignmentTitle?: string | null;
    branchLocation?: string | null;
    storeName?: string | null;
    userName?: string | null;
    status?: string;
    scoreSummary?: ScoreSummary;
  }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Get all responses
export async function fetchAllResponses({
  status,
  page = 1,
  limit = 10,
  dateRange,
  branchId,
  templateName,
}: FetchAllResponsesParams): Promise<ResponsesPaginated> {
  const params = new URLSearchParams();
  params.append("page", page.toString());
  params.append("limit", limit.toString());
  if (dateRange && dateRange.startDate && dateRange.endDate) {
    params.append("startDate", dateRange.startDate.toISOString());
    params.append("endDate", dateRange.endDate.toISOString());
  }
  if (branchId) {
    params.append("branchId", branchId);
  }
  if (templateName) {
    params.append("templateName", templateName);
  }
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/responses?${params.toString()}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to get all responses");
  }

  const responseJson = await response.json();
  const responseData = responseJson.data;
  console.log("Response Data:", responseData);

  const result = await Promise.all(
    responseData.map(async (response) => {
      const fetchedStatus = await fetchResponseSubmissionStatus(response.id);
      console.log("Status:", fetchedStatus);
      if (!fetchedStatus) {
        return response;
      }
      if (status && fetchedStatus !== status) {
        return null;
      }
      const fetchedScoreSummary = await fetchScoreSummary(response.id);
      if (!fetchedScoreSummary) {
        return { ...response, status: fetchedStatus };
      }
      return {
        ...response,
        status: fetchedStatus.status,
        scoreSummary: fetchedScoreSummary,
      };
    })
  );

  return {
    result: result,
    pagination: responseJson.pagination,
  };
}
