import React from "react";
import { cn } from "@/lib/utils";
import { ChevronUp, ChevronDown } from "lucide-react";

interface SpinnerProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  className?: string;
}

export const Spinner: React.FC<SpinnerProps> = ({
  value,
  onChange,
  min = 0,
  max = 100,
  step = 1,
  className,
}) => {
  const handleIncrement = () => {
    const newValue = Math.min(value + step, max);
    onChange(newValue);
  };

  const handleDecrement = () => {
    const newValue = Math.max(value - step, min);
    onChange(newValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value) || min;
    onChange(Math.min(Math.max(newValue, min), max));
  };

  return (
    <div className={cn("flex items-center", className)}>
      <input
        type="number"
        value={value}
        onChange={handleInputChange}
        min={min}
        max={max}
        className="bg-transparent px-3 py-2 border border-field-stroke rounded-l-full outline-none focus:ring-2 focus:ring-ring w-16 text-black-text text-sm"
      />
      <div className="flex flex-col border-field-stroke border-t border-r border-b rounded-r-full">
        <button
          type="button"
          onClick={handleIncrement}
          className="hover:bg-muted px-2 py-1 rounded-tr-full transition-colors"
        >
          <ChevronUp className="w-3 h-3" />
        </button>
        <button
          type="button"
          onClick={handleDecrement}
          className="hover:bg-muted px-2 py-1 border-field-stroke border-t rounded-br-full transition-colors"
        >
          <ChevronDown className="w-3 h-3" />
        </button>
      </div>
    </div>
  );
};
