import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { fetchClientsChartData } from "@/data/dashboard";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Bar, BarChart, ResponsiveContainer, XAxis, YAxis } from "recharts";
import { useTranslation } from "react-i18next";

export interface Client {
  id: string;
  name: string;
  businessType: string;
  numberOfBranches: number;
  storeAdmin: string;
  status: boolean;
  createdAt: string;
}

export function ClientsChart() {
  const [timePeriod, setTimePeriod] = useState<string>("6M");
  const { t } = useTranslation();
  const locale = t("locale");
  // React query hooks
  const {
    data: clients = [],
    isLoading: clientsLoading,
    error: clientsError,
  } = useQuery({
    queryKey: ["clients-total", timePeriod],
    queryFn: async () => await fetchClientsChartData(timePeriod),
    staleTime: 5 * 60 * 1000, // 5 minutes,
  });

  const map = new Map<number, number>();
  if (!clientsLoading && !clientsError) {
    for (const client of clients) {
      const month = new Date(client.createdAt).getMonth();
      const year = new Date(client.createdAt).getFullYear();
      const dateKey = new Date(year, month).getTime();
      if (map.has(dateKey)) {
        map.set(dateKey, map.get(dateKey)! + 1);
      } else {
        map.set(dateKey, 1);
      }
    }
  }

  const data = Array.from(map.entries()).map(([dateKey, clients]) => {
    const date = new Date(dateKey).getTime();
    const monthName = new Date(dateKey).toLocaleString(
      locale === "ar" ? "ar-EG" : "en-US",
      {
        month: "short",
      }
    );
    return {
      month: monthName,
      date,
      clients,
    };
  });

  data.sort((a, b) => a.date - b.date);

  return (
    <Card className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="font-semibold text-gray-900 text-lg">
          {t("dashboard.clientsOverTime.title")}
        </h3>
        <div className="flex items-center gap-4">
          <Select
            defaultValue="6M"
            value={timePeriod}
            onValueChange={(value) => setTimePeriod(value)}
          >
            <SelectTrigger className="">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="6M">
                {t("dashboard.clientsOverTime.timePeriods.6M")}
              </SelectItem>
              <SelectItem value="1Y">
                {t("dashboard.clientsOverTime.timePeriods.1Y")}
              </SelectItem>
              <SelectItem value="2Y">
                {t("dashboard.clientsOverTime.timePeriods.2Y")}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <XAxis
              dataKey="month"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6B7280" }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6B7280" }}
            />
            <Bar
              dataKey="clients"
              fill="#60A5FA"
              radius={[4, 4, 0, 0]}
              maxBarSize={40}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
}
