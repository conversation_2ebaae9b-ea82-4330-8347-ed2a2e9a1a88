import { Card } from "@/components/ui/card";
import { fetchActivityLog } from "@/data/dashboard";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "./ui/skeleton";
import { useTranslation } from "react-i18next";

export function ActivityLog() {
  const { t } = useTranslation();
  const {
    data: activities = [],
    isLoading: activityLogLoading,
    error: activityLogError,
  } = useQuery({
    queryKey: ["activity-log"],
    queryFn: fetchActivityLog,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: 1000,
  });

  const direction = t("locale") === "ar" ? "rtl" : "ltr";

  return (
    <Card className="flex-1 p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="font-semibold text-gray-900 text-lg">
          {t("dashboard.activityLog")}
        </h3>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full" dir={direction}>
          <thead>
            <tr className="border-gray-200 border-b">
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("dashboard.companyName")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("dashboard.activityType")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("dashboard.date")}
              </th>
              <th className="px-4 py-3 font-medium text-gray-600 text-sm text-left">
                {t("dashboard.performedBy")}
              </th>
            </tr>
          </thead>
          <tbody>
            {activityLogLoading ? (
              // Better loading state with multiple skeleton rows
              Array.from({ length: 3 }).map((_, index) => (
                <tr key={index} className="border-gray-100 border-b">
                  <td className="px-4 py-3">
                    <Skeleton className="w-24 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-32 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-20 h-4" />
                  </td>
                  <td className="px-4 py-3">
                    <Skeleton className="w-28 h-4" />
                  </td>
                </tr>
              ))
            ) : activityLogError ? (
              <tr>
                <td colSpan={4} className="px-4 py-8 text-red-600 text-center">
                  Error loading activity log: {String(activityLogError)}
                </td>
              </tr>
            ) : activities.length === 0 ? (
              <tr>
                <td colSpan={4} className="px-4 py-8 text-gray-500 text-center">
                  No activity logs found
                </td>
              </tr>
            ) : (
              activities.map((activity, index) => (
                <tr
                  key={index}
                  className="hover:bg-secondary-bg/70 border-gray-100 border-b"
                >
                  <td className="px-4 py-3 text-gray-900 text-sm">
                    {activity.company || "N/A"}
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    {activity.activity}
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    {activity.date || "N/A"}
                  </td>
                  <td className="px-4 py-3 text-gray-700 text-sm">
                    {activity.performerBy || "N/A"}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </Card>
  );
}
