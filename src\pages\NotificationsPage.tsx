import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Ch<PERSON><PERSON>Down,
  Download,
  Filter,
  Paperclip,
  X,
  Loader2,
} from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery } from "@tanstack/react-query";
import {
  getUserNotifications,
  markNotificationAsRead,
  type Notification,
} from "@/data/notifications";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import { formatDistanceToNow } from "date-fns";
import { useDateFormatter } from "@/hooks/useLocalizationSettings";

export default function NotificationsPage() {
  const { t } = useTranslation();
  const { data: currentUser } = useCurrentUser();
  const [currentPage, setCurrentPage] = useState(1);
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  const { formatDate } = useDateFormatter();

  // Fetch notifications using useQuery
  const {
    data: notificationsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["notifications", currentUser?.id, currentPage, showUnreadOnly],
    queryFn: () =>
      getUserNotifications({
        userId: currentUser?.id || "",
        page: currentPage,
        pageSize: 20,
        isRead: showUnreadOnly ? false : undefined,
      }),
    enabled: !!currentUser?.id,
    staleTime: 30 * 1000, // 30 seconds
  });

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markNotificationAsRead({ notificationId });
      // Refetch to update the UI
      refetch();
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  const formatRelativeTime = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch {
      return formatDate(timestamp);
    }
  };

  const getNotificationIcon = (type?: string | null) => {
    switch (type) {
      case "reminder":
        return (
          <Bell
            className="w-[18px] h-[18px] text-primary-dark"
            strokeWidth={1.33}
          />
        );
      case "alert":
        return (
          <AlertTriangle
            className="w-[18px] h-[18px] text-primary-dark"
            strokeWidth={1.5}
          />
        );
      case "escalated":
        return (
          <Paperclip
            className="w-[18px] h-[18px] text-primary-dark"
            strokeWidth={1.5}
          />
        );
      case "submitted":
        return (
          <Download
            className="w-[18px] h-[18px] text-primary-dark"
            strokeWidth={1.5}
          />
        );
      case "cancelled":
        return (
          <X
            className="w-[18px] h-[18px] text-primary-dark"
            strokeWidth={1.5}
          />
        );
      default:
        return (
          <Bell
            className="w-[18px] h-[18px] text-primary-dark"
            strokeWidth={1.33}
          />
        );
    }
  };

  if (!currentUser) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Loader2 className="mx-auto mb-4 w-8 h-8 animate-spin" />
          <p className="text-gray-text">Loading user information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Main Container */}
      <div className="flex">
        {/* Main Content */}
        <div className="flex-1 mt-[20px] mr-[20px] lg:mr-[40px] ml-[110px] lg:ml-[130px] px-2 sm:px-0">
          {/* Main Content Area */}
          <div className="p-5 rounded-[15px] min-h-[calc(100vh-110px)]">
            {/* Page Header */}
            <div className="flex justify-between items-center mb-8">
              <h1 className="font-bold text-[22px] text-black-text">
                {t("notifications.title", "Notification centre")}
              </h1>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowUnreadOnly(!showUnreadOnly)}
                  className={`flex items-center gap-2 px-4 py-2.5 border rounded-full transition-colors ${
                    showUnreadOnly
                      ? "bg-primary-dark text-white border-primary-dark"
                      : "bg-secondary-bg text-black-text border-field-stroke"
                  }`}
                >
                  <Filter className="w-5 h-5" strokeWidth={1.5} />
                  <span className="font-medium">
                    {showUnreadOnly
                      ? t("notifications.showAll", "Show All")
                      : t("notifications.unreadOnly", "Unread Only")}
                  </span>
                </button>
              </div>
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="flex justify-center items-center py-12">
                <div className="text-center">
                  <Loader2 className="mx-auto mb-4 w-8 h-8 animate-spin" />
                  <p className="text-gray-text">
                    {t("notifications.loading", "Loading notifications...")}
                  </p>
                </div>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="bg-red-50 p-4 border border-red-200 rounded-lg text-center">
                <p className="text-red-600">
                  {t(
                    "notifications.error",
                    "Failed to load notifications. Please try again."
                  )}
                </p>
                <button
                  onClick={() => refetch()}
                  className="bg-red-600 hover:bg-red-700 mt-2 px-4 py-2 rounded-lg text-white"
                >
                  {t("notifications.retry", "Retry")}
                </button>
              </div>
            )}

            {/* Notifications Cards Section */}
            {!isLoading && !error && notificationsData && (
              <div className="bg-secondary-bg shadow-sm mb-8 p-[18px] rounded-xl">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="font-bold text-black-text text-xl">
                    {t("notifications.cardTitle", "Notifications")}
                  </h2>
                  <div className="text-gray-text text-sm">
                    {t("notifications.total", "Total: {{total}}", {
                      total: notificationsData.total,
                    })}
                  </div>
                </div>

                {notificationsData.items.length === 0 ? (
                  <div className="py-12 text-center">
                    <Bell className="mx-auto mb-4 w-12 h-12 text-gray-300" />
                    <p className="text-gray-text">
                      {showUnreadOnly
                        ? t("notifications.noUnread", "No unread notifications")
                        : t(
                            "notifications.noNotifications",
                            "No notifications found"
                          )}
                    </p>
                  </div>
                ) : (
                  <div className="gap-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                    {notificationsData.items.map(
                      (notification: Notification) => (
                        <div
                          key={notification.id}
                          className={`bg-main-bg p-2.5 rounded-lg border-l-4 ${
                            notification.isRead
                              ? "border-gray-300"
                              : "border-primary-dark"
                          }`}
                        >
                          <div className="flex justify-between items-center mb-2.5 px-2 py-1">
                            <div className="flex items-center gap-2">
                              {getNotificationIcon(notification.type)}
                              <h3 className="font-bold text-black-text text-lg">
                                {notification.title}
                              </h3>
                            </div>
                            {!notification.isRead && (
                              <div className="bg-primary-dark rounded-full w-2 h-2"></div>
                            )}
                          </div>
                          <div className="bg-secondary-bg p-2.5 rounded-lg">
                            <p className="mb-3 text-gray-text text-sm leading-relaxed">
                              {notification.body}
                            </p>
                            <div className="flex justify-between items-center">
                              <span className="font-medium text-primary-dark text-sm">
                                {formatRelativeTime(notification.sentAt)}
                              </span>
                              <div className="flex items-center gap-2">
                                {!notification.isRead && (
                                  <button
                                    onClick={() =>
                                      handleMarkAsRead(notification.id)
                                    }
                                    className="bg-gray-600 hover:bg-gray-700 px-3 py-1 rounded-full font-bold text-white text-xs transition-colors"
                                  >
                                    {t("notifications.markRead", "Mark Read")}
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    )}
                  </div>
                )}

                {/* Pagination */}
                {notificationsData && notificationsData.totalPages > 1 && (
                  <div className="flex justify-center items-center gap-4 mt-6">
                    <button
                      onClick={() =>
                        setCurrentPage((prev) => Math.max(1, prev - 1))
                      }
                      disabled={currentPage === 1}
                      className="disabled:opacity-50 px-4 py-2 border border-gray-300 rounded-lg disabled:cursor-not-allowed hover:"
                    >
                      {t("common.previous", "Previous")}
                    </button>
                    <span className="text-gray-text text-sm">
                      {t("common.pageInfo", "Page {{current}} of {{total}}", {
                        current: currentPage,
                        total: notificationsData.totalPages,
                      })}
                    </span>
                    <button
                      onClick={() =>
                        setCurrentPage((prev) =>
                          Math.min(notificationsData.totalPages, prev + 1)
                        )
                      }
                      disabled={currentPage === notificationsData.totalPages}
                      className="disabled:opacity-50 px-4 py-2 border border-gray-300 rounded-lg disabled:cursor-not-allowed hover:"
                    >
                      {t("common.next", "Next")}
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
