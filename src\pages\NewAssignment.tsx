import { AssignChecklistForm } from "@/components/AssignChecklistForm";
import TemplateForm from "@/components/TemplateForm";
import { TemplateSelection } from "@/components/TemplateSelection";
import { useState } from "react";

export type AssignmentStep = "selection" | "assign" | "create";

const NewAssignment = () => {
  const [currentStep, setCurrentStep] = useState<AssignmentStep>("selection");
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(
    null
  );

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplateId(templateId);
    setCurrentStep("assign");
  };

  const handleCreateCustom = () => {
    setCurrentStep("create");
  };

  const handleBack = () => {
    setCurrentStep("selection");
  };

  const handleSuccess = () => {
    setCurrentStep("selection");
  };

  const handleCancel = () => {
    setCurrentStep("selection");
  };

  return (
    <div className="min-h-screen">
      {currentStep === "selection" && (
        <TemplateSelection
          onTemplateSelect={handleTemplateSelect}
          onCreateCustom={handleCreateCustom}
          selectedTemplateId={selectedTemplateId}
          setSelectedTemplateId={setSelectedTemplateId}
        />
      )}

      {currentStep === "assign" && (
        <AssignChecklistForm
          selectedTemplateId={selectedTemplateId}
          onBack={handleBack}
        />
      )}

      {currentStep === "create" && (
        <TemplateForm
          isEdit={false}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      )}
    </div>
  );
};

export default NewAssignment;
