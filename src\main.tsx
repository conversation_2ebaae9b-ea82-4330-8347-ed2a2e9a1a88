import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./i18n";
import "./index.css";
import { AuthProvider } from "./contexts/AuthProvider.tsx";
import { GoogleMapsLoader } from "./components/GoogleMapsLoader.tsx";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

// Create a query client instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 2,
    },
  },
});

// Enhanced service worker registration
if ("serviceWorker" in navigator) {
  window.addEventListener("load", async () => {
    try {
      // Unregister all conflicting service workers
      const registrations = await navigator.serviceWorker.getRegistrations();
      for (const registration of registrations) {
        await registration.unregister();
        console.log("Unregistered service worker:", registration.scope);
      }

      // Register the combined service worker
      const registration = await navigator.serviceWorker.register(
        "/firebase-messaging-sw.js",
        { scope: "/" }
      );

      console.log("Service Worker registered:", registration);

      // Force immediate activation
      if (registration.waiting) {
        registration.waiting.postMessage({ type: "SKIP_WAITING" });
      }

      // Check for update daily
      setInterval(() => {
        registration
          .update()
          .catch((err) => console.warn("Service Worker update failed:", err));
      }, 24 * 60 * 60 * 1000);
    } catch (error) {
      console.error("Service Worker registration failed:", error);
    }
  });
}

createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
          <App />
      </AuthProvider>
    </QueryClientProvider>
  </React.StrictMode>
);
