import { roles } from "./constants";

export interface Branch {
  company: string;
  location: string;
  latitude: string;
  longitude: string;
  managerEmail: string;
  id?: string;
}
// Define filter field types
export type FilterField =
  | "name"
  | "business_type"
  | "number_of_branches"
  | "store_admin_email"
  | "created_at"
  | "status";
export type FilterType = "string" | "number" | "enum" | "date";
export interface FilterConfig {
  field: FilterField;
  type: FilterType;
  label: string;
  options?: string[];
}
export interface Filter {
  id: string;
  field: FilterField;
  value: string | number;
  operator?: "eq" | "ilike" | "gte" | "lte";
} // Configuration for each filterable field

export type FilterConfigRecord = Partial<Record<FilterField, FilterConfig>>;
// Types for the template data

export interface TemplateSection {
  id: number;
  name: string;
  type?: "textarea" | "radio" | "image";
  questions?: string[];
  placeholder?: string;
  options?: string[];
  hasImage?: boolean;
  imageUrl?: string;
}
export interface TemplateSectionFields {
  id: string;
  order: number;
  question: string;
  questionType: "Text" | "YES/NO";
  required: boolean;
  requiresEvidence: boolean;
  score: string;
  hint: string;
  preferredAnswer?: string; // Optional field for preferred answer
}
export interface AssignmentSectionFields {
  id: string;
  order: number;
  question: string;
  questionType: "Text" | "YES/NO";
  required: boolean;
  requiresEvidence: boolean;
  score: string;
  hint: string;
  preferredAnswer?: string; // Optional field for preferred answer
}

export interface Template {
  id: string;
  title: string;
  description?: string;
  businessType: string;
  //   status: "Draft" | "Published";
  sections: TemplateSection[];
  sectionsCount?: number;
}

export interface TemplateViewSection {
  checklistTemplateId: string;
  fields: TemplateSectionFields[];
  id: string;
  name: string;
  businessType?: string;
  order?: number;
}
export interface AssignmentViewSection {
  checklistAssignmentId: string;
  fields: AssignmentSectionFields[];
  id: string;
  name: string;
  description?: string;
  order?: number;
}

export interface TemplateViewType {
  id: string;
  title: string;
  description?: string;
  businessType: string;
  //   status: "Draft" | "Published";
  sections: TemplateViewSection[];
  sectionsCount?: number;
  passRate?: string;
}

export interface ChecklistAssignment {
  id: string;
  templateName: string;
  branchId: string;
  branchLocation?: string | null;
  storeName: string;
  assignTo: string;
  frequency: string;
  status: string;
  startDate: string;
  dueDate: string;
  assignedToName?: string;
}
export interface ClientSnapshot {
  id: string;
  name: string;
  numberOfBranches: number;
}
export interface Activity {
  company: string;
  activity: string;
  date: string;
  performerBy: string;
}
export interface ActivityLog {
  id: string;
  companyName: string;
  activityType: string;
  date: Date;
  performerBy: string;
  tableName: string;
  createdAt: Date;
  oldData: unknown;
  newData: unknown;
  recordId: string;
  userName: string;
}

export enum ChecklistAssignmentFrequency {
  DAILY = "daily",
  WEEKLY = "weekly",
  MONTHLY = "monthly",
  YEARLY = "yearly",
}
export interface CreateAssignmentProps {
  templateId: string;
  branchId: string;
  assignedTo?: string;
  assignedBy: string;
  dueDate?: string;
  startDate?: string;
  status?: "Active" | "Scheduled" | "Expired";
  frequency?: "daily" | "weekly" | "monthly" | "yearly";
  notes?: string;
  sections?: AssignmentViewSection[];
  passRate?: string;
}

// Interface for updating assignment hierarchy
export interface UpdateChecklistHierarchyInput {
  assignment?: {
    templateId?: string;
    branchId?: string;
    assignedTo?: string;
    assignedBy?: string;
    dueDate?: string;
    startDate?: string;
    status?: "Active" | "Scheduled" | "Expired";
    frequency?: "daily" | "weekly" | "monthly" | "yearly";
    notes?: string;
    passRate?: string;
  };
  sections?: Array<{
    id?: string;
    name?: string;
    description?: string;
    order?: number;
    fields?: Array<{
      id?: string;
      question?: string;
      questionType?: "YES/NO" | "Text";
      required?: boolean;
      requiresEvidence?: boolean;
      score?: string;
      hint?: string;
      order?: number;
      preferredAnswer?: string;
      templateFieldId?: string;
    }>;
  }>;
}

export interface CreateChecklistResponseInput {
  assignmentId: string;
  answers: {
    sectionId: string;
    fieldId: string;
    answer: string;
    description?: string;
    score?: number | string;
    status?: "not_yet" | "in_progress" | "success";
    mediaIds?: string[];
  }[];
}

// Types for the upload function
export interface UploadResult {
  success: boolean;
  url?: string;
  path?: string;
  error?: string;
  id?: string;
}

export interface UploadProgress {
  fileName: string;
  progress: number;
  success?: boolean;
  error?: string;
}

export interface Media {
  id: string;
  name: string;
  url: string;
}

export interface ResponseField {
  id: string;
  fieldId: string;
  question: string;
  questionType: "YES/NO" | "Text";
  required: boolean;
  requiresEvidence: boolean;
  fieldScore: string;
  hint: string;
  order: number;
  answer: string;
  description: "string";
  status: "not_yet" | "completed" | "in_progress";
  score: string | null;
  media: Media[];
  createdAt: string;
  updatedAt: string;
}
export interface Response {
  id: string;
  checklistAssignmentsId: string;
  userId: string;
  branchId: string;
  score: string | null;
  latitude: string | null;
  longitude: string | null;
  completedAt: string;
  submittedAt: string | null;
  createdAt: string;
  updatedAt: string;
  assignmentTitle: string;
  branchLocation: string;
  userName: string;
  sections: [
    {
      id: string;
      name: string;
      description: string;
      order: number;
      fields: ResponseField[];
    }
  ];
  totalFields: number;
  answeredFields: number;
}

export type GranularityType = "daily" | "weekly" | "monthly" | "yearly";
export interface Assignment {
  id: string;
  templateId: string;
  branchId: string;
  assignedTo: string | null;
  assignedBy: string;
  dueDate: string | null;
  startDate: string | null;
  frequency: string;
  notes: string | null;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  templateTitle: string | null;
  templateDescription: string | null;
  businessType: string | null;
  sections: AssignmentViewSection[];
}

export interface FetchedBranch {
  id: string;
  storeId: string;
  location: string | null;
  latitude: string | null;
  longitude: string | null;
  status: "active" | "inactive" | "archived";
  createdAt: Date;
  updatedAt: Date;
  branchManagerId: string | null;
  managerEmail: string | null;
  storeName: string | null;
}

export interface BrandingSettings {
  logoUrl: string;
  primaryColor: string;
  secondaryColor: string;
  loginMessage: string;
}
export interface LocalizationSettings {
  language: string;
  dateFormat: string;
  timeZone: string;
  firstDayOfWeek: string;
}
export interface DefaultsSettings {
  defaultChecklistFrequency: "daily" | "weekly" | "monthly" | "yearly";
  requireEvidenceByDefault: boolean;
  allowEditAfterSubmission: boolean;
  autoArchiveOldChecklists: boolean;
  archiveThresholdDays?: number;
}
export interface UserSettings {
  enable2FA: boolean;
  sessionTimeout: number;
  auditTrailVisibility: boolean;
}

export type Roles = (typeof roles)[number];
