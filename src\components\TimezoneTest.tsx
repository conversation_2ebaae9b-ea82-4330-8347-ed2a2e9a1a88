import React from "react";
import {
  useDateFormatter,
  useLocalizationSettings,
} from "@/hooks/useLocalizationSettings";
import {
  formatDateWithTimezone,
  formatDateTimeWithTimezone,
  getUserBrowserTimezone,
} from "@/lib/timezone";

/**
 * Test component to verify timezone functionality
 * This can be temporarily added to any page to test timezone support
 */
export const TimezoneTest: React.FC = () => {
  const { formatDate, formatDateTime, formatTime, timezone } =
    useDateFormatter();
  const { settings } = useLocalizationSettings();

  // Test dates
  const now = new Date();
  const utcDate = "2024-01-15T14:30:00Z"; // UTC date string

  return (
    <div className="bg-gray-50 p-4 border rounded-lg max-w-2xl">
      <h3 className="mb-4 font-semibold text-lg">
        Timezone & Date Formatting Test
      </h3>

      <div className="space-y-2 mb-4">
        <div>
          <strong>User's Browser Timezone:</strong> {getUserBrowserTimezone()}
        </div>
        <div>
          <strong>User's Selected Timezone:</strong> {timezone}
        </div>
        <div>
          <strong>User's Date Format:</strong>{" "}
          {settings?.dateFormat || "MM/dd/yyyy"}
        </div>
      </div>

      <div className="space-y-3">
        <div className="pt-3 border-t">
          <h4 className="mb-2 font-medium">Current Date/Time Formatting:</h4>
          <div className="space-y-1 ml-4">
            <div>
              <strong>Date only:</strong> {formatDate(now)}
            </div>
            <div>
              <strong>Date + Time:</strong> {formatDateTime(now, true)}
            </div>
            <div>
              <strong>Time only:</strong> {formatTime(now)}
            </div>
          </div>
        </div>

        <div className="pt-3 border-t">
          <h4 className="mb-2 font-medium">
            UTC Date String Conversion ({utcDate}):
          </h4>
          <div className="space-y-1 ml-4">
            <div>
              <strong>Date only:</strong> {formatDate(utcDate)}
            </div>
            <div>
              <strong>Date + Time:</strong> {formatDateTime(utcDate, true)}
            </div>
            <div>
              <strong>Time only:</strong> {formatTime(utcDate)}
            </div>
          </div>
        </div>

        <div className="pt-3 border-t">
          <h4 className="mb-2 font-medium">Direct Timezone Functions:</h4>
          <div className="space-y-1 ml-4">
            <div>
              <strong>Saudi Arabia (Asia/Riyadh):</strong>{" "}
              {formatDateWithTimezone(
                utcDate,
                "MM/dd/yyyy HH:mm",
                "Asia/Riyadh"
              )}
            </div>
            <div>
              <strong>UAE (Asia/Dubai):</strong>{" "}
              {formatDateTimeWithTimezone(
                utcDate,
                "dd/MM/yyyy HH:mm",
                "Asia/Dubai"
              )}
            </div>
            <div>
              <strong>UTC:</strong>{" "}
              {formatDateWithTimezone(utcDate, "yyyy-MM-dd HH:mm", "UTC")}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
