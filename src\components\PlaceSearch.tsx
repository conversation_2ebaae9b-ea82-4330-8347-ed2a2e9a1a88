import React, { useRef } from 'react';
import { Autocomplete } from '@react-google-maps/api';

interface PlaceSearchProps {
  onSelect: (place: google.maps.places.PlaceResult) => void;
}

export function PlaceSearch({ onSelect }: PlaceSearchProps) {
  const autoRef = useRef<google.maps.places.Autocomplete>();

  return (
    <Autocomplete
      onLoad={auto => (autoRef.current = auto)}
      onPlaceChanged={() => {
        const place = autoRef.current!.getPlace();
        onSelect(place);
      }}
    >
      <input
        type="text"
        placeholder="Search a place..."
        style={{ width: '100%', padding: '8px', fontSize: '16px' }}
      />
    </Autocomplete>
  );
}