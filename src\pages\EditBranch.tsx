import { Header } from "@/components/CompanyHeader";
import { NewBranchForm } from "@/components/NewBranchForm";
import { fetchBranchById } from "@/data/branches";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

const EditBranch = () => {
  const { t } = useTranslation();
  const { branchId } = useParams<{ branchId: string }>();

  const { data, isLoading, error } = useQuery({
    queryKey: ["branch", branchId],
    queryFn: () => fetchBranchById(branchId),
    staleTime: 5 * 60 * 1000,
    enabled: !!branchId,
  });
  if (isLoading) {
    return (
      <div className="flex justify-center items-center w-full min-h-screen">
        {t("common.loading")}
      </div>
    );
  }
  if (error) {
    return (
      <div className="flex justify-center items-center w-full min-h-screen">
        {t("branch.errorLoading")}: {error?.message || t("branch.noDataFound")}
      </div>
    );
  }
  const branch = data?.branch;
  return (
    <div className="flex min-h-screen">
      <div className="flex-1">
        <div className="min-h-screen">
          <main className="p-6">
            <div className="mb-8">
              <h1 className="mb-2 font-semibold text-slate-900 text-2xl">
                {t("branch.editBranchTitle")}
              </h1>
            </div>

            <NewBranchForm branch={branch} />
          </main>
        </div>
      </div>
    </div>
  );
};

export default EditBranch;
