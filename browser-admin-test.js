// Browser console script to create ADMIN user
// Copy and paste this into your browser console while on your app

async function createAdminUser() {
  // Get the supabase client from your app's global scope
  const supabase = window.supabase || 
                   window.__SUPABASE_CLIENT__ || 
                   (await import('./src/integrations/supabase/client.js')).supabase;

  if (!supabase) {
    console.error('❌ Supabase client not found. Make sure you are on your app page.');
    return;
  }

  console.log('🚀 Creating ADMIN user...');

  try {
    const { data, error } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'testpassword123',
      options: {
        emailRedirectTo: `${window.location.origin}/`,
        data: {
          name: 'Test Admin User',
          phone: '+1234567890',
          role: 'ADMIN'  // This is the key part - setting ADMIN role
        },
      },
    });

    if (error) {
      console.error('❌ Error:', error.message);
      return;
    }

    console.log('✅ ADMIN user created successfully!');
    console.log('📧 User details:', {
      id: data.user?.id,
      email: data.user?.email,
      role: data.user?.user_metadata?.role,
      name: data.user?.user_metadata?.name,
      confirmed: data.user?.email_confirmed_at ? 'Yes' : 'No - Check email'
    });

    if (!data.user?.email_confirmed_at) {
      console.log('📬 Check email for verification link');
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Alternative: Sign in as existing admin
async function signInAsAdmin() {
  const supabase = window.supabase || 
                   window.__SUPABASE_CLIENT__ || 
                   (await import('./src/integrations/supabase/client.js')).supabase;

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });

    if (error) {
      console.error('❌ Sign in failed:', error.message);
      return;
    }

    console.log('✅ Signed in as ADMIN!');
    console.log('👤 User:', {
      email: data.user?.email,
      role: data.user?.user_metadata?.role,
      name: data.user?.user_metadata?.name
    });

  } catch (error) {
    console.error('💥 Error:', error);
  }
}

// Make functions available globally
window.createAdminUser = createAdminUser;
window.signInAsAdmin = signInAsAdmin;

console.log('🎯 ADMIN User Test Functions Loaded!');
console.log('📝 Available commands:');
console.log('  createAdminUser() - Create new admin user');
console.log('  signInAsAdmin() - Sign in as admin user');
console.log('');
console.log('💡 To create admin user, run: createAdminUser()');
