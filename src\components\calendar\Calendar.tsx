import { CalendarReturn } from "@/data/assignments";
import { CalendarGrid } from "./CalendarGrid";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarIcon } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as DatePicker } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";
import {
  useLocalizationSettings,
  useDateFormatter,
} from "@/hooks/useLocalizationSettings";

interface CalendarProps {
  events: CalendarReturn[];
  onEventClick: (event: CalendarReturn) => void;
  viewMode: "weekly" | "monthly";
  onViewModeChange: (mode: "weekly" | "monthly") => void;
  selectedDate: Date;
  onDateChange: (date: Date) => void;
}

export function Calendar({
  events,
  onEventClick,
  selectedDate,
  onDateChange,
}: CalendarProps) {
  const { t } = useTranslation();
  const { formatDateTime } = useDateFormatter();
  const { settings } = useLocalizationSettings();

  // Convert first day of week setting to number for DatePicker
  // DatePicker expects: 0 = Sunday, 1 = Monday, 2 = Tuesday, etc.
  const getWeekStartsOn = (
    firstDayOfWeek: string | undefined
  ): 0 | 1 | 2 | 3 | 4 | 5 | 6 => {
    switch (firstDayOfWeek) {
      case "monday":
        return 1;
      case "tuesday":
        return 2;
      case "wednesday":
        return 3;
      case "thursday":
        return 4;
      case "friday":
        return 5;
      case "saturday":
        return 6;
      case "sunday":
      default:
        return 0;
    }
  };

  const weekStartsOn = getWeekStartsOn(settings?.firstDayOfWeek);

  return (
    <div className="mx-auto px-4 lg:px-0 max-w-[1250px]">
      {/* Calendar Header */}
      <div className="flex sm:flex-row flex-col justify-between sm:items-center gap-4 mb-6">
        <h1 className="font-bold text-foreground sm:text-[22px] text-xl">
          {t("calendarComponent.assignmentCalendar")}{" "}
          {format(selectedDate, "MMMM yyyy")}
        </h1>

        {/* Month/Year Picker */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "justify-start w-[240px] font-normal text-left",
                !selectedDate && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 w-4 h-4" />
              {selectedDate ? (
                format(selectedDate, "MMMM yyyy")
              ) : (
                <span>{t("calendarComponent.pickMonth")}</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="p-0 w-auto" align="start">
            <DatePicker
              mode="single"
              selected={selectedDate}
              onSelect={(date) => date && onDateChange(date)}
              weekStartsOn={weekStartsOn}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* Calendar Grid */}
      <div className="shadow-sm border border-gray-100 rounded-xl">
        <CalendarGrid
          events={events}
          onEventClick={onEventClick}
          selectedDate={selectedDate}
        />
      </div>
    </div>
  );
}
