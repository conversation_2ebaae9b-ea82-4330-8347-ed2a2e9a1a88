import { useQueryClient } from "@tanstack/react-query";
import { RefreshCw, Calendar, Bell, ChevronDown } from "lucide-react";
import { Button } from "../ui/button";

const DashboardHeader = ({ refetch }: { refetch: () => void }) => {
  const queryClient = useQueryClient();
  return (
    <div className="flex lg:flex-row flex-col lg:justify-between lg:items-center gap-4">
      <h1 className="font-bold text-dashboard-primary text-2xl">
        Reporting dashboard
      </h1>

      <div className="flex sm:flex-row flex-col items-start sm:items-center gap-4">
        {/* Refresh Button */}
        <Button
          className="flex items-center gap-2 bg-dashboard-primary hover:bg-dashboard-primary/90 px-6 py-2 rounded-full text-white transition-colors"
          onClick={refetch}
        >
          <RefreshCw className="w-5 h-5" />
          <span className="font-bold">Refresh</span>
        </Button>
      </div>
    </div>
  );
};

export default DashboardHeader;
