import { BranchCompletionTable } from "@/components/reporting-dashboard/BranchCompletionTable";
import { CompletionChart } from "@/components/reporting-dashboard/CompletionChart";
import DashboardHeader from "@/components/reporting-dashboard/DashboardHeader";
import { FailedQuestionsChart } from "@/components/reporting-dashboard/FailedQuestionsChart";
import { MetricsCards } from "@/components/reporting-dashboard/MetricsCards";
import { StoreEmployeeCards } from "@/components/reporting-dashboard/StoreEmployeeCards";
import { SubmissionDistributionChart } from "@/components/reporting-dashboard/SubmissionDistributionChart";
import fetchStoreOverviewDashboard from "@/data/analytics";
import { fetchStore } from "@/data/stores";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

export default function ReportingDashboard() {
  const { t } = useTranslation();
  const { storeId } = useParams<{ storeId: string }>();
  const { data, isLoading, error, refetch, isFetching } = useQuery({
    queryKey: ["store-overview", storeId],
    queryFn: () => fetchStoreOverviewDashboard(storeId),
    staleTime: 5 * 60 * 1000,
  });
  const {
    data: storeData,
    isLoading: isStoreLoading,
    error: storeError,
  } = useQuery({
    queryKey: ["store", storeId],
    queryFn: () => fetchStore(storeId),
    staleTime: 5 * 60 * 1000,
  });

  const storeName = storeData?.store?.name;

  console.log("Store Overview Data:", data);
  if (isLoading || isStoreLoading || isFetching) {
    return (
      <div className="flex justify-center items-center w-full min-h-screen">
        {t("reportingDashboard.loading")}
      </div>
    );
  }
  if (error || storeError || !data) {
    return (
      <div className="flex justify-center items-center w-full min-h-screen">
        {t("reportingDashboard.error")}:{" "}
        {error?.message ||
          storeError?.message ||
          t("reportingDashboard.noDataFound")}
      </div>
    );
  }
  return (
    <div className="min-h-screen">
      {/* Main Content */}
      <div className="flex lg:flex-row flex-col gap-5 bg-dashboard-bg p-5 min-h-[calc(100vh-70px)]">
        {/* Dashboard Content */}
        <div className="flex-1 space-y-5">
          {/* Dashboard Header */}
          <DashboardHeader refetch={refetch} />
          {/* Content Area */}
          <div className="space-y-5 bg-dashboard-bg p-5 rounded-2xl">
            {/* Metrics Cards */}
            <MetricsCards data={data} />
            {/* Store & Employee Cards */}
            <StoreEmployeeCards data={data} storeName={storeName} />
            {/* Charts Section */}
            <div className="space-y-5">
              {/* Line Chart */}
              <CompletionChart storeId={storeId} branchId={null} />
              {/* Bottom Charts Row */}
              <div className="flex xl:flex-row flex-col gap-5">
                <FailedQuestionsChart storeId={storeId} />
                <SubmissionDistributionChart storeId={storeId} />
              </div>
            </div>
            {/* Data Table */}
            <BranchCompletionTable storeId={storeId} />
          </div>
        </div>
      </div>
    </div>
  );
}
