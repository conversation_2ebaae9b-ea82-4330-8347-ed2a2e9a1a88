import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { fetchAssignmentById } from "@/data/assignments";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ChevronLeft, Upload, X, FileImage } from "lucide-react";
import { Link, Navigate, useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMemo, useState, useEffect } from "react";
import { CreateChecklistResponseInput } from "@/lib/types";
import {
  createChecklistAssignmentResponse,
  updateResponseFieldWithMedia,
} from "@/data/response";
import { toast } from "sonner";
import { uploadImageToSupabase } from "@/data/media";
import { useTranslation } from "react-i18next";
import Logo from "@/components/Logo";

const AssignmentResponse = () => {
  const { t } = useTranslation();
  const { assignmentId } = useParams<{ assignmentId: string }>();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // State for managing uploaded images per field
  const [uploadedImages, setUploadedImages] = useState<Record<string, File[]>>(
    {}
  );
  const [imagePreviewUrls, setImagePreviewUrls] = useState<
    Record<string, string[]>
  >({});

  // Query to fetch assignment data
  const {
    data: assignment,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["assignment", assignmentId],
    queryFn: () => fetchAssignmentById(assignmentId!),
    enabled: !!assignmentId,
  });

  const mutation = useMutation({
    mutationFn: async (formData: CreateChecklistResponseInput) => {
      return await createChecklistAssignmentResponse(formData);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ["assignment", assignmentId],
      });
      queryClient.invalidateQueries({
        queryKey: ["assignments"],
      });
      toast.success(t("assignmentResponse.assignmentCreatedSuccess"));
      navigate(`/assignments`);
    },
    onError: (error) => {
      toast.error(
        t("assignmentResponse.failedToCreateAssignment") + ": " + error
      );
      console.error("Error creating assignment:", error);
    },
  });

  // Handle image upload
  const handleImageUpload = (fieldId: string, files: FileList | null) => {
    if (!files) return;

    const newFiles = Array.from(files);
    const currentFiles = uploadedImages[fieldId] || [];

    // Limit to 5 images per field (you can adjust this)
    const maxImages = 5;
    const totalFiles = currentFiles.length + newFiles.length;

    if (totalFiles > maxImages) {
      toast.error(
        t("assignmentResponse.maximumImagesAllowed", { count: maxImages })
      );
      return;
    }

    // Create preview URLs for new files
    const newPreviewUrls = newFiles.map((file) => URL.createObjectURL(file));

    setUploadedImages((prev) => ({
      ...prev,
      [fieldId]: [...currentFiles, ...newFiles],
    }));

    setImagePreviewUrls((prev) => ({
      ...prev,
      [fieldId]: [...(prev[fieldId] || []), ...newPreviewUrls],
    }));
  };

  // Remove image
  const removeImage = (fieldId: string, index: number) => {
    const currentFiles = uploadedImages[fieldId] || [];
    const currentUrls = imagePreviewUrls[fieldId] || [];

    // Revoke the object URL to free memory
    URL.revokeObjectURL(currentUrls[index]);

    const newFiles = currentFiles.filter((_, i) => i !== index);
    const newUrls = currentUrls.filter((_, i) => i !== index);

    setUploadedImages((prev) => ({
      ...prev,
      [fieldId]: newFiles,
    }));

    setImagePreviewUrls((prev) => ({
      ...prev,
      [fieldId]: newUrls,
    }));
  };

  // Create dynamic schema based on assignment data
  const formSchema = useMemo(() => {
    if (!assignment) return z.object({});

    const schemaFields: Record<string, z.ZodTypeAny> = {};

    assignment.sections.forEach((section) => {
      if (section.fields) {
        section.fields.forEach((field, index) => {
          const fieldKey = field.id;

          if (field.questionType === "Text") {
            schemaFields[fieldKey] = z
              .string()
              .min(1, t("assignmentResponse.fieldRequired"));
          } else if (field.questionType === "YES/NO") {
            schemaFields[fieldKey] = z.enum(["yes", "no"], {
              required_error: t("assignmentResponse.pleaseSelectOption"),
            });
          }
        });
      }
    });

    return z.object(schemaFields);
  }, [assignment, t]);

  type FormData = z.infer<typeof formSchema>;

  // Initialize form with dynamic schema
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {},
  });

  const onSubmit = async (values: FormData) => {
    if (!assignment || !assignmentId) return;

    // Transform the form data to match CreateChecklistResponseInput
    const answers: CreateChecklistResponseInput["answers"] = [];

    assignment.sections.forEach((section) => {
      if (section.fields) {
        section.fields.forEach((field) => {
          const fieldKey = field.id;
          const answer = values[fieldKey as keyof FormData];
          if (answer) {
            const answerData: CreateChecklistResponseInput["answers"][number] =
              {
                sectionId: section.id,
                fieldId: field.id,
                answer: String(answer),
                mediaIds: [], // Will be populated after media upload
              };
            answers.push(answerData);
          }
        });
      }
    });

    const formData: CreateChecklistResponseInput = {
      assignmentId: assignmentId,
      answers: answers,
    };

    try {
      // Step 1: Create the checklist response first
      const result = await mutation.mutateAsync(formData);
      console.log("Assignment submitted successfully:", result);

      // Step 2: Upload media for fields that require evidence
      const mediaUploadPromises: Promise<unknown>[] = [];

      assignment.sections.forEach((section) => {
        if (section.fields) {
          section.fields.forEach(async (field) => {
            const fieldKey = field.id;

            // Check if this field requires evidence and has uploaded images
            if (field.requiresEvidence && uploadedImages[fieldKey]) {
              const answers: Record<string, string> = result.response.answers;
              // Find the corresponding response field ID from the result
              const responseFieldId = answers[field.id];

              console.log("Response Field Match:", responseFieldId);
              if (responseFieldId) {
                const uploadPromise = uploadImageToSupabase({
                  file: uploadedImages[fieldKey][0],
                  checklistResponseFieldsId: responseFieldId,
                });

                mediaUploadPromises.push(uploadPromise);
              }
            }
          });
        }
      });

      // Wait for all media uploads to complete
      await Promise.all(mediaUploadPromises);

      toast(t("assignmentResponse.assignmentSubmittedSuccess"));
      navigate("/response-successful");
    } catch (error) {
      console.error("Error submitting assignment:", error);
      toast(t("assignmentResponse.errorSubmittingAssignment"));
    } finally {
      queryClient.invalidateQueries({
        queryKey: ["responses"],
      });
    }
  };

  if (isLoading) {
    return (
      <div className="w-full min-h-screen">
        {/* Header */}
        <div className="px-6 py-4 border-b">
          <div className="flex justify-between items-center mx-auto animate-pulse">
            <div className="flex items-center space-x-4">
              <div className="bg-slate-200 rounded-full w-8 h-8" />
              <div className="bg-slate-200 rounded w-40 h-4" />
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-slate-200 rounded w-6 h-4" />
              <div className="bg-slate-200 rounded-full w-8 h-8" />
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="mx-auto p-6 animate-pulse">
          <div className="flex items-center space-x-4 mb-6">
            <div className="bg-slate-200 rounded w-8 h-8" />
          </div>

          {/* Skeleton Card Header */}
          <Card className="space-y-4 mb-6 p-6">
            <div className="gap-6 grid grid-cols-1 md:grid-cols-3">
              <div>
                <div className="bg-slate-300 mb-2 rounded w-32 h-4" />
                <div className="bg-slate-200 rounded w-full h-5" />
              </div>
              <div>
                <div className="bg-slate-300 mb-2 rounded w-32 h-4" />
                <div className="bg-slate-200 rounded w-full h-5" />
              </div>
            </div>
          </Card>

          {/* Skeleton Sections */}
          <div className="space-y-6">
            {Array.from({ length: 2 }).map((_, i) => (
              <Card key={i} className="space-y-4 p-6">
                <div className="flex justify-between items-center mb-4">
                  <div className="bg-slate-200 rounded w-1/3 h-5" />
                  <div className="bg-slate-200 rounded-full w-4 h-4" />
                </div>
                <ul className="space-y-2">
                  <li className="bg-slate-200 rounded w-3/4 h-4" />
                  <li className="bg-slate-200 rounded w-2/3 h-4" />
                </ul>
                <div className="bg-slate-100 rounded w-full h-24" />
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="w-full min-h-screen">
        <div className="px-6 py-4 border-b">
          <div className="flex justify-between items-center mx-auto">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-gray-600 text-sm">
                <Link to="/assignments" className="hover:text-gray-900">
                  {t("global.assignments")}
                </Link>
                <span>{">"}</span>
                <span>{t("assignmentResponse.assignmentView")}</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <span className="text-gray-600">🔔</span>
              </Button>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600 text-sm">🇺🇸</span>
                <div className="flex justify-center items-center bg-orange-400 rounded-full w-8 h-8">
                  <span className="font-medium text-white text-sm">A</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <p className="mb-2 text-red-600">
              {t("assignmentResponse.errorLoadingAssignment")}
            </p>
            <p className="text-gray-600 text-sm">
              {error instanceof Error
                ? error.message
                : t("assignmentResponse.unknownError")}
            </p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() =>
                queryClient.refetchQueries({
                  queryKey: ["assignment", assignmentId],
                })
              }
            >
              {t("global.retry")}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!assignment) {
    return (
      <div className="w-full min-h-screen">
        <div className="px-6 py-4 border-b">
          <div className="flex justify-between items-center mx-auto">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-gray-600 text-sm">
                <Link to="/assignments" className="hover:text-gray-900">
                  {t("global.assignments")}
                </Link>
                <span>{">"}</span>
                <span>{t("assignmentResponse.assignmentView")}</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <span className="text-gray-600">🔔</span>
              </Button>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600 text-sm">🇺🇸</span>
                <div className="flex justify-center items-center bg-orange-400 rounded-full w-8 h-8">
                  <span className="font-medium text-white text-sm">A</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-center items-center min-h-96">
          <p className="text-gray-600">
            {t("assignmentResponse.assignmentNotFound")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen">
      {/* Main Content */}
      <div className="bg-gray-100 mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <Link to="/assignments">
            <Button variant="ghost" size="sm">
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </Link>
        </div>

        {/* Assignment Header */}
        <div className="mb-[1.25rem] font-bold text-[1.375rem] text-black">
          {t("assignmentResponse.assignChecklistForm")}
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Assignment Sections */}
            {assignment.sections.map((section) => (
              <div key={section.id} className="pb-2 rounded-lg">
                <div className="flex items-center bg-[#e7eaee] mb-4 px-[0.9375rem] py-2 rounded-t-[0.75rem]">
                  <h3 className="font-medium text-gray-900 text-lg">
                    {section.name}
                  </h3>
                </div>

                {/* List Type Questions */}
                {section.fields && (
                  <ol className="space-y-6 mb-4 px-4">
                    {section.fields.map((field, index) => {
                      const fieldKey = field.id;

                      return (
                        <li
                          key={index}
                          className="flex flex-col items-start gap-3"
                        >
                          <span className="font-bold text-gray-600">
                            {index + 1}.{" "}
                            <span className="text-gray-900">
                              {field.question}
                            </span>
                            {field.requiresEvidence && (
                              <span className="ml-2 text-red-500 text-sm">
                                *{t("assignmentResponse.evidenceRequired")}
                              </span>
                            )}
                          </span>

                          {/* Textarea Type */}
                          {field.questionType === "Text" && (
                            <FormField
                              control={form.control}
                              name={fieldKey as keyof FormData}
                              render={({ field: formField }) => (
                                <FormItem className="w-full">
                                  <FormControl>
                                    <Textarea
                                      {...formField}
                                      className="w-full h-32 resize-none"
                                      placeholder={t(
                                        "assignmentResponse.enterResponse"
                                      )}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          )}

                          {/* Radio Button Type */}
                          {field.questionType === "YES/NO" && (
                            <FormField
                              control={form.control}
                              name={fieldKey as keyof FormData}
                              render={({ field: formField }) => (
                                <FormItem className="space-y-3">
                                  <FormControl>
                                    <RadioGroup
                                      onValueChange={formField.onChange}
                                      value={formField.value}
                                      className="flex flex-col space-y-1"
                                    >
                                      <div className="flex items-center space-x-2">
                                        <RadioGroupItem
                                          value="yes"
                                          id={`${fieldKey}_yes`}
                                        />
                                        <Label htmlFor={`${fieldKey}_yes`}>
                                          {t("global.yes")}
                                        </Label>
                                      </div>
                                      <div className="flex items-center space-x-2">
                                        <RadioGroupItem
                                          value="no"
                                          id={`${fieldKey}_no`}
                                        />
                                        <Label htmlFor={`${fieldKey}_no`}>
                                          {t("assignmentResponse.no")}
                                        </Label>
                                      </div>
                                    </RadioGroup>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          )}

                          {/* Image Upload Section - Show only if requiresEvidence is true */}
                          {field.requiresEvidence && (
                            <div className="space-y-3 w-full">
                              <Label className="font-medium text-gray-700 text-sm">
                                {t("assignmentResponse.uploadEvidenceImages")}
                              </Label>

                              {/* File Input */}
                              <div className="relative">
                                <Input
                                  type="file"
                                  accept="image/*"
                                  multiple
                                  onChange={(e) =>
                                    handleImageUpload(fieldKey, e.target.files)
                                  }
                                  className="hidden"
                                  id={`upload-${fieldKey}`}
                                />
                                <Label
                                  htmlFor={`upload-${fieldKey}`}
                                  className="flex justify-center items-center border-2 border-gray-300 hover:border-gray-400 border-dashed rounded-lg w-full h-32 transition-colors cursor-pointer"
                                >
                                  <div className="flex flex-col items-center space-y-2">
                                    <Upload className="w-8 h-8 text-gray-400" />
                                    <span className="text-gray-600 text-sm">
                                      {t("assignmentResponse.clickToUpload")}
                                    </span>
                                    <span className="text-gray-500 text-xs">
                                      {t("assignmentResponse.fileFormats")}
                                    </span>
                                  </div>
                                </Label>
                              </div>

                              {/* Image Preview Grid */}
                              {imagePreviewUrls[fieldKey] &&
                                imagePreviewUrls[fieldKey].length > 0 && (
                                  <div className="gap-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 mt-4">
                                    {imagePreviewUrls[fieldKey].map(
                                      (url, imgIndex) => (
                                        <div
                                          key={imgIndex}
                                          className="group relative"
                                        >
                                          <img
                                            src={url}
                                            alt={t(
                                              "assignmentResponse.evidenceAlt",
                                              { index: imgIndex + 1 }
                                            )}
                                            className="border border-gray-200 rounded-lg w-full h-24 object-cover"
                                          />
                                          <Button
                                            type="button"
                                            variant="destructive"
                                            size="sm"
                                            className="top-1 right-1 absolute opacity-0 group-hover:opacity-100 p-0 w-6 h-6 transition-opacity"
                                            onClick={() =>
                                              removeImage(fieldKey, imgIndex)
                                            }
                                          >
                                            <X className="w-4 h-4" />
                                          </Button>
                                          <div className="bottom-1 left-1 absolute bg-black bg-opacity-50 px-1 py-0.5 rounded text-white text-xs">
                                            {uploadedImages[fieldKey]?.[
                                              imgIndex
                                            ]?.name.slice(0, 10)}
                                            ...
                                          </div>
                                        </div>
                                      )
                                    )}
                                  </div>
                                )}

                              {/* Upload Status */}
                              {uploadedImages[fieldKey] &&
                                uploadedImages[fieldKey].length > 0 && (
                                  <div className="flex items-center space-x-2 text-gray-600 text-sm">
                                    <FileImage className="w-4 h-4" />
                                    <span>
                                      {t("assignmentResponse.imagesUploaded", {
                                        count: uploadedImages[fieldKey].length,
                                      })}
                                    </span>
                                  </div>
                                )}
                            </div>
                          )}
                        </li>
                      );
                    })}
                  </ol>
                )}
              </div>
            ))}

            {/* Submit Button */}
            <div className="flex justify-end pt-6">
              <Button
                type="submit"
                className="bg-slate-800 hover:bg-slate-700"
                disabled={mutation.isPending}
              >
                {mutation.isPending
                  ? t("assignmentResponse.submitting")
                  : t("assignmentResponse.submitAssignment")}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default AssignmentResponse;
