import React from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Smartphone,
  Wifi,
  Bell,
  Download,
  Share,
  RotateCcw,
  Database,
  Zap,
  Shield,
  Monitor,
} from "lucide-react";
import PWAStatus from "@/components/PWAStatus";
import { usePWA } from "@/hooks/usePWA";
import { toast } from "sonner";

const PWATestPage: React.FC = () => {
  const { t } = useTranslation();
  const { isOnline, canShare, shareContent } = usePWA();

  const testOfflineMode = () => {
    toast.info(
      "To test offline mode: Open Developer Tools → Network tab → Check 'Offline' → Refresh page"
    );
  };

  const testNotifications = async () => {
    if (!("Notification" in window)) {
      toast.error("This browser doesn't support notifications");
      return;
    }

    let permission = Notification.permission;

    if (permission === "default") {
      permission = await Notification.requestPermission();
    }

    if (permission === "granted") {
      new Notification("Y-Verify PWA Test", {
        body: "Push notifications are working! 🎉",
        icon: "/notification-icon.png",
        badge: "/notification-badge.png",
      });
      toast.success("Test notification sent!");
    } else {
      toast.error("Notification permission denied");
    }
  };

  const testShare = async () => {
    const success = await shareContent({
      title: "Y-Verify PWA Test",
      text: "Testing the PWA share functionality",
      url: window.location.href,
    });

    if (!success) {
      toast.info("Share API not supported, but URL copy fallback works");
    }
  };

  const testCaching = async () => {
    try {
      // Test if service worker and caching is working
      if ("serviceWorker" in navigator) {
        const registration = await navigator.serviceWorker.ready;
        if (registration) {
          toast.success("Service Worker is active and caching is enabled");
        }
      } else {
        toast.error("Service Worker not supported");
      }
    } catch (error) {
      toast.error("Caching test failed: " + String(error));
    }
  };

  const pwaFeatures = [
    {
      icon: Smartphone,
      title: "App-like Experience",
      description: "Native app feel with standalone display mode",
      status: window.matchMedia("(display-mode: standalone)").matches,
      color: "blue",
    },
    {
      icon: Wifi,
      title: "Offline Support",
      description: "Works without internet connection using cached data",
      status: true,
      color: "green",
    },
    {
      icon: Bell,
      title: "Push Notifications",
      description: "Receive notifications for important updates",
      status: "Notification" in window,
      color: "purple",
    },
    {
      icon: Database,
      title: "Smart Caching",
      description: "Intelligent caching for fast loading",
      status: "serviceWorker" in navigator,
      color: "orange",
    },
    {
      icon: Share,
      title: "Native Sharing",
      description: "Share content using device's native share menu",
      status: canShare,
      color: "pink",
    },
    {
      icon: Monitor,
      title: "Responsive Design",
      description: "Adapts to any screen size and orientation",
      status: true,
      color: "teal",
    },
  ];

  return (
    <div className="space-y-6 p-6">
      <div className="text-center">
        <h1 className="mb-2 font-bold text-3xl">PWA Testing Center</h1>
        <p className="text-gray-600">
          Test and verify Progressive Web App functionality
        </p>
      </div>

      {/* PWA Status Component */}
      <div>
        <h2 className="mb-4 font-semibold text-xl">Current PWA Status</h2>
        <PWAStatus />
      </div>

      {/* Feature Tests */}
      <div>
        <h2 className="mb-4 font-semibold text-xl">Feature Tests</h2>
        <div className="gap-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <Wifi className="mx-auto mb-2 w-8 h-8 text-blue-600" />
                <h3 className="mb-2 font-medium">Offline Mode Test</h3>
                <p className="mb-3 text-gray-600 text-sm">
                  Test how the app behaves when offline
                </p>
                <Button variant="outline" size="sm" onClick={testOfflineMode}>
                  Test Offline
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <Bell className="mx-auto mb-2 w-8 h-8 text-purple-600" />
                <h3 className="mb-2 font-medium">Push Notifications</h3>
                <p className="mb-3 text-gray-600 text-sm">
                  Test push notification functionality
                </p>
                <Button variant="outline" size="sm" onClick={testNotifications}>
                  Test Notifications
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <Share className="mx-auto mb-2 w-8 h-8 text-green-600" />
                <h3 className="mb-2 font-medium">Native Sharing</h3>
                <p className="mb-3 text-gray-600 text-sm">
                  Test device's native share functionality
                </p>
                <Button variant="outline" size="sm" onClick={testShare}>
                  Test Share
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <Database className="mx-auto mb-2 w-8 h-8 text-orange-600" />
                <h3 className="mb-2 font-medium">Caching System</h3>
                <p className="mb-3 text-gray-600 text-sm">
                  Test service worker and caching
                </p>
                <Button variant="outline" size="sm" onClick={testCaching}>
                  Test Cache
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <Monitor className="mx-auto mb-2 w-8 h-8 text-teal-600" />
                <h3 className="mb-2 font-medium">Responsive Test</h3>
                <p className="mb-3 text-gray-600 text-sm">
                  Resize window to test responsiveness
                </p>
                <Badge variant="secondary">Auto-tested</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <Zap className="mx-auto mb-2 w-8 h-8 text-yellow-600" />
                <h3 className="mb-2 font-medium">Performance</h3>
                <p className="mb-3 text-gray-600 text-sm">
                  Check app performance metrics
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    toast.info("Open Developer Tools → Lighthouse → Run audit");
                  }}
                >
                  Test Performance
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* PWA Features Overview */}
      <div>
        <h2 className="mb-4 font-semibold text-xl">PWA Features Status</h2>
        <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
          {pwaFeatures.map((feature, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-lg bg-${feature.color}-100`}>
                    <feature.icon
                      className={`h-6 w-6 text-${feature.color}-600`}
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-1">
                      <h3 className="font-medium">{feature.title}</h3>
                      <Badge variant={feature.status ? "default" : "secondary"}>
                        {feature.status ? "✓ Active" : "✗ Inactive"}
                      </Badge>
                    </div>
                    <p className="text-gray-600 text-sm">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Installation Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Installation Guide
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
              <div>
                <h4 className="mb-2 font-medium">Desktop Browsers:</h4>
                <ul className="space-y-1 text-gray-600 text-sm">
                  <li>• Chrome/Edge: Click install button in address bar</li>
                  <li>• Firefox: Menu → "Install this site as an app"</li>
                  <li>• Safari: File → "Add to Dock"</li>
                </ul>
              </div>
              <div>
                <h4 className="mb-2 font-medium">Mobile Devices:</h4>
                <ul className="space-y-1 text-gray-600 text-sm">
                  <li>• iOS Safari: Share → "Add to Home Screen"</li>
                  <li>• Android Chrome: Menu → "Add to Home screen"</li>
                  <li>
                    • Samsung Internet: Menu → "Add page to" → "Home screen"
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle>System Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="gap-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 text-sm">
            <div>
              <div className="font-medium text-gray-600">Connection</div>
              <div className="flex items-center gap-2">
                {isOnline ? (
                  <Badge variant="default">Online</Badge>
                ) : (
                  <Badge variant="destructive">Offline</Badge>
                )}
              </div>
            </div>
            <div>
              <div className="font-medium text-gray-600">Service Worker</div>
              <div>
                <Badge
                  variant={
                    "serviceWorker" in navigator ? "default" : "secondary"
                  }
                >
                  {"serviceWorker" in navigator ? "Supported" : "Not Supported"}
                </Badge>
              </div>
            </div>
            <div>
              <div className="font-medium text-gray-600">Display Mode</div>
              <div>
                <Badge
                  variant={
                    window.matchMedia("(display-mode: standalone)").matches
                      ? "default"
                      : "secondary"
                  }
                >
                  {window.matchMedia("(display-mode: standalone)").matches
                    ? "Standalone"
                    : "Browser"}
                </Badge>
              </div>
            </div>
            <div>
              <div className="font-medium text-gray-600">Notifications</div>
              <div>
                <Badge
                  variant={"Notification" in window ? "default" : "secondary"}
                >
                  {"Notification" in window ? "Supported" : "Not Supported"}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PWATestPage;
