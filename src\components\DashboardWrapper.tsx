import React, { useEffect, useState } from "react";
import { Navigate } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import { canUserAccess } from "@/data/permissions";
import { Roles } from "@/lib/types";

interface DashboardWrapperProps {
  children: React.ReactNode;
}

export const DashboardWrapper: React.FC<DashboardWrapperProps> = ({
  children,
}) => {
  const [canAccessDashboard, setCanAccessDashboard] = useState<boolean | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkDashboardAccess = async () => {
      try {
        // Get user session
        const { data: sessionData } = await supabase.auth.getSession();
        const role = sessionData.session?.user.user_metadata?.role as Roles;

        if (!role) {
          // No role means no access, redirect to assignments
          setCanAccessDashboard(false);
          setIsLoading(false);
          return;
        }

        // Check if user can access dashboard
        const canAccess = await canUserAccess("access_dashboard");
        setCanAccessDashboard(canAccess);
      } catch (error) {
        console.error("Error checking dashboard access:", error);
        setCanAccessDashboard(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkDashboardAccess();
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="mx-auto mb-4 border-b-2 border-blue-600 rounded-full w-8 h-8 animate-spin"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Redirect if no dashboard access
  if (canAccessDashboard === false) {
    return <Navigate to="/assignments" replace />;
  }

  // Has dashboard access
  return <>{children}</>;
};
