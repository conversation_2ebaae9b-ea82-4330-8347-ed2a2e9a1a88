import { ITEMS_PER_PAGE } from "@/components/CompanyTable";
import { fetchWithToken } from "@/lib/fetchWithToken";
import { supabase } from "@/lib/supabase";
import { Filter } from "@/lib/types";

// API functions
export async function createStore(data: {
  name: string;
  businessType: string;
  numberOfBranches: number;
  adminEmail: string;
  status: "active" | "inactive";
}) {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/stores`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    throw new Error(
      "Failed to create store: " + (await response.json()).message
    );
  }

  return response.json();
}
export async function updateStore(data: {
  storeId: string;
  name: string;
  businessType: string;
  numberOfBranches: number;
  adminEmail: string;
  status: "active" | "inactive";
}) {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/stores/${data.storeId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to create store");
  }

  return response.json();
}
export const fetchCompanies = async (filters: Filter[], page: number) => {
  let queryParameter = `?page=${page}&limit=${ITEMS_PER_PAGE}`;
  // Apply filters
  filters.forEach((filter) => {
    switch (filter.field) {
      case "name":
        queryParameter += `&name=${filter.value}`;
        break;
      case "business_type":
        queryParameter += "&business_type=" + filter.value;
        break;
      case "number_of_branches":
        queryParameter += `&number_of_branches=${filter.value}`;
        break;
      case "created_at": {
        // Assuming the filter value is a date string, we'll filter by date
        queryParameter += `&created_at=${filter.value}`;
        break;
      }
      case "status": {
        queryParameter += `&status=${filter.value}`;
        break;
      }
    }
  });

  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/stores/companies-management${queryParameter}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch data");
  }

  const data = await response.json();

  let result =
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data?.data.stores.map((row: any) => ({
      id: row.id,
      name: row.name,
      business_type: row.businessType,
      number_of_branches: row.numberOfBranches,
      store_admin: row.email,
      status: row.status === "active",
      created_at: row.createdAt,
    })) || [];

  // Apply client-side filtering for store_admin_email
  const emailFilter = filters.find((f) => f.field === "store_admin_email");
  if (emailFilter) {
    result = result.filter((company) =>
      company.store_admin
        ?.toLowerCase()
        .includes((emailFilter.value as string).toLowerCase())
    );
  }

  const total = Number(data.pagination.total) || 0;
  const totalPages = Math.ceil(total / ITEMS_PER_PAGE);

  return {
    data: result,
    total,
    totalPages,
  };
};
export const mutateStatus = async ({
  id,
  value,
}: {
  id: string;
  value: boolean;
}) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/stores/${id}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        status: value ? "active" : "inactive",
      }),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to update store status");
  }

  return;
};

export const fetchStore = async (storeId: string) => {
  console.log(storeId);
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/stores/${storeId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch store data");
  }

  const data = await response.json();

  return data.data;
};

export const deleteCompany = async (id: string) => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/stores/${id}`,
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error((await response.json()).message);
  }

  return response.json();
};

export const fetchStoresWithSupabase = async () => {
  const { data, error } = await supabase.from("stores").select("*");

  if (error) {
    throw new Error("Failed to fetch stores: " + error.message);
  }
  return data;
};
