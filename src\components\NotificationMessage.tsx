import React, { useEffect } from 'react';

export interface NotificationData {
  id: string;
  title: string;
  body: string;
  image?: string;
  timestamp?: number;
  data?: any;
}

interface NotificationMessageProps {
  notification: NotificationData;
  onClose: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const NotificationMessage: React.FC<NotificationMessageProps> = ({ 
  notification, 
  onClose, 
  position = 'top-right' 
}) => {
  useEffect(() => {
    // Auto-close after 5 seconds
    const timer = setTimeout(() => {
      onClose(notification.id);
    }, 5000);

    return () => clearTimeout(timer);
  }, [notification.id, onClose]);

  const getPositionStyles = () => {
    switch (position) {
      case 'top-left':
        return { top: '20px', left: '20px' };
      case 'bottom-right':
        return { bottom: '20px', right: '20px' };
      case 'bottom-left':
        return { bottom: '20px', left: '20px' };
      default: // top-right
        return { top: '20px', right: '20px' };
    }
  };

  const formatTime = (timestamp?: number) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div 
      style={{
        position: 'fixed',
        ...getPositionStyles(),
        backgroundColor: 'white',
        border: '1px solid #e2e8f0',
        borderLeft: '4px solid #3b82f6',
        borderRadius: '8px',
        padding: '16px',
        minWidth: '320px',
        maxWidth: '400px',
        boxShadow: '0 10px 25px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05)',
        zIndex: 9999,
        animation: position.includes('right') ? 'slideInRight 0.3s ease-out' : 'slideInLeft 0.3s ease-out',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}
    >
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: '8px'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          flex: 1
        }}>
          {notification.image && (
            <img 
              src={notification.image} 
              alt="notification" 
              style={{ 
                width: '40px', 
                height: '40px', 
                borderRadius: '6px',
                objectFit: 'cover'
              }}
            />
          )}
          <div style={{ flex: 1 }}>
            <h4 style={{ 
              margin: 0, 
              fontSize: '16px', 
              fontWeight: '600',
              color: '#1f2937',
              lineHeight: '1.2'
            }}>
              {notification.title}
            </h4>
            {notification.timestamp && (
              <span style={{
                fontSize: '12px',
                color: '#9ca3af',
                marginTop: '2px',
                display: 'block'
              }}>
                {formatTime(notification.timestamp)}
              </span>
            )}
          </div>
        </div>
        <button 
          onClick={() => onClose(notification.id)}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '20px',
            cursor: 'pointer',
            padding: '4px',
            borderRadius: '4px',
            color: '#9ca3af',
            lineHeight: '1',
            marginLeft: '8px',
            transition: 'color 0.2s'
          }}
        //   onMouseEnter={(e) => e.target.style.color = '#ef4444'}
        //   onMouseLeave={(e) => e.target.style.color = '#9ca3af'}
        >
          ×
        </button>
      </div>

      {/* Body */}
      <div style={{ 
        fontSize: '14px', 
        color: '#4b5563',
        lineHeight: '1.5',
        marginBottom: '8px'
      }}>
        {notification.body}
      </div>

      {/* Optional action button */}
      {notification.data?.actionUrl && (
        <button
          onClick={() => {
            window.open(notification.data.actionUrl, '_blank');
            onClose(notification.id);
          }}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '6px 12px',
            fontSize: '12px',
            cursor: 'pointer',
            marginTop: '8px'
          }}
        >
          View Details
        </button>
      )}

      <style>{`
        @keyframes slideInRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        @keyframes slideInLeft {
          from {
            transform: translateX(-100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};

export default NotificationMessage;