import { use<PERSON><PERSON> } from "@/hooks/usePWA";
import { useAuth } from "@/contexts/AuthContext";
import { useTranslation } from "react-i18next";
import Auth from "./Auth";
import Dashboard from "@/components/Dashboard";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Download, Smartphone, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

const Index = () => {
  const { isInstallable, installPWA } = usePWA();
  const { user, loading } = useAuth();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [installPromptDismissed, setInstallPromptDismissed] = useState(false);

  const handleInstall = async () => {
    const success = await installPWA();
    if (success) {
      toast.success(t("pwa.installSuccess"));
      setInstallPromptDismissed(true);
    } else {
      toast.error(t("pwa.installFailed"));
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="mx-auto border-gray-800 border-b-2 rounded-full w-12 h-12 animate-spin"></div>
          <p className="mt-4 text-gray-600">{t("common.loading")}</p>
        </div>
      </div>
    );
  }

  if (!user) {
    navigate("/signin");
    return null; // Redirecting to SignIn page
  }

  return (
    <>
      {/* Enhanced PWA Install Prompt */}
      {isInstallable && !installPromptDismissed && (
        <div className="top-4 right-4 z-50 fixed max-w-sm">
          <div className="relative bg-white shadow-xl p-4 border rounded-lg">
            <button
              onClick={() => setInstallPromptDismissed(true)}
              className="top-2 right-2 absolute text-gray-400 hover:text-gray-600"
            >
              <X className="w-4 h-4" />
            </button>

            <div className="flex items-start gap-3">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Smartphone className="w-6 h-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="mb-1 font-semibold text-gray-900 text-sm">
                  {t("pwa.install")}
                </h3>
                <p className="mb-3 text-gray-600 text-xs">
                  {t("pwa.installDescription")}
                </p>
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleInstall} className="text-xs">
                    <Download className="mr-1 w-3 h-3" />
                    {t("pwa.install")}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setInstallPromptDismissed(true)}
                    className="text-xs"
                  >
                    Later
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      <Dashboard />
    </>
  );
};

export default Index;
