import { z } from "zod";
import { isValidPhoneNumber } from "libphonenumber-js";

// We'll need to pass the translation function to get localized error messages
export const createSignUpSchema = (t: (key: string) => string) =>
  z
    .object({
      name: z.string().min(2, t("validation.nameMinLength")),
      email: z.string().email(t("validation.invalidEmail")),
      password: z.string().min(6, t("validation.passwordMinLength")),
      confirmPassword: z.string().min(6, t("validation.confirmPassword")),
      phone: z
        .string()
        .min(1, t("validation.phoneRequired"))
        .refine(
          (phone) => {
            try {
              return isValidPhoneNumber(phone);
            } catch {
              return false;
            }
          },
          { message: t("validation.invalidPhone") }
        ),
      role: z.enum(
        [
          "ADMIN",
          "STORE_MANAGER",
          "BRANCH_MANAGER",
          "AUDITOR",
          "CHECKER",
          "MAINTAIN<PERSON>",
          "VIEWER",
        ],
        {
          required_error: t("validation.roleRequired"),
        }
      ),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t("validation.passwordMismatch"),
      path: ["confirmPassword"],
    });

export const signInSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  rememberMe: z.boolean().optional(),
});

// Keep the original schema for backward compatibility, but it's better to use createSignUpSchema
export const signUpSchema = z
  .object({
    name: z.string().min(2, "Name must be at least 2 characters"),
    email: z.string().email("Please enter a valid email address"),
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z.string().min(6, "Please confirm your password"),
    phone: z
      .string()
      .min(1, "Phone number is required")
      .refine(
        (phone) => {
          try {
            return isValidPhoneNumber(phone);
          } catch {
            return false;
          }
        },
        { message: "Please enter a valid phone number" }
      ),
    role: z.enum(
      [
        "ADMIN",
        "STORE_MANAGER",
        "BRANCH_MANAGER",
        "AUDITOR",
        "CHECKER",
        "MAINTAINER",
        "VIEWER",
      ],
      {
        required_error: "Please select a role",
      }
    ),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export type SignInFormData = z.infer<typeof signInSchema>;
export type SignUpFormData = z.infer<typeof signUpSchema>;
