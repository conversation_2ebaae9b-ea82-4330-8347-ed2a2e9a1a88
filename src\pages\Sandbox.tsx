import React, { useState } from "react";
import {
  uploadImageToSupabase,
  deleteImageFromSupabase,
  getSignedUrl,
} from "@/data/media";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { generateMockFieldId } from "@/lib/utils";
import { fetchResponseById } from "@/data/response";
import TestIssueAssignments from "@/components/TestIssueAssignments";

const Sandbox = () => {
  const responseId = "01f2be48-49ad-4c1e-81a6-99bfc11514d6";
  const { data, isLoading, error } = useQuery({
    queryKey: ["response", responseId],
    queryFn: () => fetchResponseById(responseId!),
    enabled: !!responseId,
  });

  console.log(data);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div style={{ padding: "2rem", fontFamily: "sans-serif" }}>
      <TestIssueAssignments />
      <hr className="my-8" />
      <div>
        <h3>Response Data:</h3>
        {JSON.stringify(data)}
      </div>
    </div>
  );
};

export default Sandbox;
