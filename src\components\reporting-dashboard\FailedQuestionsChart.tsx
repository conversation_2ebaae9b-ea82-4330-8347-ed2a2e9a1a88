import { fetchFailedQuestionsByTemplate } from "@/data/analytics";
import { fetchTemplates } from "@/data/templates";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

export function FailedQuestionsChart({ storeId }: { storeId: string }) {
  const [templateId, setTemplateId] = useState<string | undefined>(undefined);

  const {
    data: templates,
    isLoading: isTemplatesLoading,
    error: templateError,
  } = useQuery({
    queryKey: ["templates", storeId],
    queryFn: () => fetchTemplates({ page: 1, limit: 100 }),
    staleTime: 5 * 60 * 1000,
  });

  const { data, isLoading, isFetching, error } = useQuery({
    queryKey: ["failed-questions", storeId, templateId],
    queryFn: () => fetchFailedQuestionsByTemplate(storeId, templateId),
    enabled: !!storeId && !!templateId,
    staleTime: 5 * 60 * 1000,
  });

  if (isLoading || isFetching) {
    return (
      <div className="flex justify-center items-center min-h-40">
        Loading...
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-40 text-red-500">
        Error: {error.message}
      </div>
    );
  }

  const chartData =
    data?.slice(0, 10).map((item) => ({
      question: item.question.slice(0, 15) + "...",
      failure_rate: Number(item.failure_rate),
    })) ?? [];

  return (
    <Card className="p-6 w-full">
      <div className="flex justify-between items-center mb-6">
        <h2 className="font-bold text-lg">Failed questions by frequency</h2>
        <Select onValueChange={setTemplateId} value={templateId}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Select template" />
          </SelectTrigger>
          <SelectContent>
            {isTemplatesLoading ? (
              <div className="p-2 text-gray-500 text-sm">Loading...</div>
            ) : templateError ? (
              <div className="p-2 text-gray-500 text-sm">
                Error: {templateError.message}
              </div>
            ) : (
              templates?.data.map((template) => (
                <SelectItem key={template.id} value={template.id}>
                  {template.title}
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      </div>

      {chartData.length === 0 ? (
        <div className="text-gray-500 text-sm text-center">No data found.</div>
      ) : (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="question" tick={{ fontSize: 12 }} />
            <YAxis
              domain={[0, 100]}
              tickFormatter={(val) => `${val}%`}
              tick={{ fontSize: 12 }}
            />
            <Tooltip formatter={(val: number) => `${val}%`} />
            <Bar
              dataKey="failure_rate"
              fill="#6CA0DC"
              radius={[4, 4, 0, 0]}
              maxBarSize={40}
            />
          </BarChart>
        </ResponsiveContainer>
      )}
    </Card>
  );
}
