import { StoreOverviewDashboard } from "@/data/analytics";
import { ArrowUpRight, User } from "lucide-react";

interface InfoCardProps {
  title: string;
  locationName: string;
  performance: string;
  indicatorColor: string;
  icon?: React.ReactNode;
}

function InfoCard({
  title,
  locationName,
  performance,
  indicatorColor,
  icon,
}: InfoCardProps) {
  return (
    <div className="flex justify-between items-end shadow-sm p-4 rounded-2xl w-full">
      <div className="flex flex-col gap-5">
        <div className="flex items-center gap-1.5">
          {icon ? (
            <User className="w-5 h-5 text-dashboard-primary" />
          ) : (
            <div className={`w-5 h-5 rounded-full ${indicatorColor}`}></div>
          )}
          <h3 className="font-bold text-dashboard-primary text-lg">{title}</h3>
        </div>

        <div className="flex flex-col gap-2">
          <div className="font-bold text-dashboard-primary">{locationName}</div>
          <div className="text-dashboard-gray text-sm">{performance}</div>
        </div>
      </div>

      <ArrowUpRight className="p-1.5 w-7 h-7 text-dashboard-primary" />
    </div>
  );
}

export function StoreEmployeeCards({
  data,
  storeName,
}: {
  data: StoreOverviewDashboard;
  storeName: string;
}) {
  return (
    <div className="gap-4 grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
      <InfoCard
        title="Best store by completion"
        locationName={`${storeName} - ${data.branch_rankings.best_branch.branch_name}`}
        performance={`(${data.branch_rankings.best_branch.completion_rate}% on-time)`}
        indicatorColor="bg-dashboard-green"
      />

      <InfoCard
        title="Worst store by completion"
        locationName={`${storeName} - ${data.branch_rankings.worst_branch.branch_name}`}
        performance={`(${data.branch_rankings.worst_branch.completion_rate}% on-time)`}
        indicatorColor="bg-dashboard-red"
      />

      <InfoCard
        title="Top employee by completion"
        locationName={data.top_employees.employee_name}
        performance={`(${data.top_employees.completion_rate}% on-time)`}
        indicatorColor=""
        icon={<User />}
      />
    </div>
  );
}
