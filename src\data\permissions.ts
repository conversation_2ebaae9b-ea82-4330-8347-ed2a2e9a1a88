import { fetchWithToken } from "@/lib/fetchWithToken";
import { Roles } from "@/lib/types";

// Types for permissions
export type Feature =
  | "view_reports"
  | "create_edit_checklist"
  | "assign_template"
  | "submit_checklist"
  | "access_calendar_view"
  | "access_dashboard"
  // New view permissions
  | "view_checklist_assignments"
  | "view_assignment_responses"
  | "view_templates"
  | "view_stores"
  | "view_branches"
  // New CRUD permissions
  | "crud_checklist_assignments"
  | "crud_assignment_responses"
  | "crud_templates"
  | "crud_stores"
  | "crud_branches"
  // Admin-only permissions
  | "view_modify_settings"
  | "role_permission_matrix"
  | "user_invite";

export interface PermissionResponse {
  hasPermission: boolean;
  userRole?: Roles;
}

export interface PermissionCheckResult {
  status: string;
  data: PermissionResponse;
}

/**
 * Check if a specific role has permission for a feature
 * @param role - The user role to check
 * @param feature - The feature to check access for
 * @returns Promise with permission check result
 */
export const checkRolePermission = async (
  role: Roles,
  feature: Feature
): Promise<PermissionResponse> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/permissions/check-role?role=${role}&feature=${feature}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      `Failed to check role permission: ${
        errorData.message || response.statusText
      }`
    );
  }

  const data: PermissionCheckResult = await response.json();
  return data.data;
};

/**
 * Check if the authenticated user has permission for a feature
 * @param feature - The feature to check access for
 * @returns Promise with permission check result including user role
 */
export const checkUserPermission = async (
  feature: Feature
): Promise<PermissionResponse> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/permissions/check-user?feature=${feature}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      `Failed to check user permission: ${
        errorData.message || response.statusText
      }`
    );
  }

  const data: PermissionCheckResult = await response.json();
  return data.data;
};

/**
 * Helper function to check if authenticated user can access a specific feature
 * Returns boolean for easier conditional rendering
 * @param feature - The feature to check access for
 * @returns Promise<boolean>
 */
export const canUserAccess = async (feature: Feature): Promise<boolean> => {
  try {
    const result = await checkUserPermission(feature);
    return result.hasPermission;
  } catch (error) {
    console.error(`Permission check failed for feature ${feature}:`, error);
    return false;
  }
};

/**
 * Helper function to check if a specific role can access a feature
 * Returns boolean for easier conditional logic
 * @param role - The user role to check
 * @param feature - The feature to check access for
 * @returns Promise<boolean>
 */
export const canRoleAccess = async (
  role: Roles,
  feature: Feature
): Promise<boolean> => {
  try {
    const result = await checkRolePermission(role, feature);
    return result.hasPermission;
  } catch (error) {
    console.error(
      `Permission check failed for role ${role} and feature ${feature}:`,
      error
    );
    return false;
  }
};

/**
 * Check multiple permissions for the authenticated user at once
 * @param features - Array of features to check
 * @returns Promise with object containing permission results for each feature
 */
export const checkMultipleUserPermissions = async (
  features: Feature[]
): Promise<Record<Feature, boolean>> => {
  const results = await Promise.allSettled(
    features.map(async (feature) => ({
      feature,
      hasPermission: await canUserAccess(feature),
    }))
  );

  const permissions: Record<string, boolean> = {};

  results.forEach((result, index) => {
    const feature = features[index];
    if (result.status === "fulfilled") {
      permissions[feature] = result.value.hasPermission;
    } else {
      console.error(
        `Failed to check permission for ${feature}:`,
        result.reason
      );
      permissions[feature] = false;
    }
  });

  return permissions as Record<Feature, boolean>;
};

/**
 * Check multiple permissions for a specific role at once
 * @param role - The user role to check
 * @param features - Array of features to check
 * @returns Promise with object containing permission results for each feature
 */
export const checkMultipleRolePermissions = async (
  role: Roles,
  features: Feature[]
): Promise<Record<Feature, boolean>> => {
  const results = await Promise.allSettled(
    features.map(async (feature) => ({
      feature,
      hasPermission: await canRoleAccess(role, feature),
    }))
  );

  const permissions: Record<string, boolean> = {};

  results.forEach((result, index) => {
    const feature = features[index];
    if (result.status === "fulfilled") {
      permissions[feature] = result.value.hasPermission;
    } else {
      console.error(
        `Failed to check permission for role ${role} and feature ${feature}:`,
        result.reason
      );
      permissions[feature] = false;
    }
  });

  return permissions as Record<Feature, boolean>;
};

// Types for update operations
export interface PermissionUpdate {
  role: Roles;
  feature: Feature;
  value: boolean;
}

export interface UpdatePermissionResponse {
  status: string;
  message: string;
  data: {
    success: boolean;
  };
}

export interface UpdateMultiplePermissionsResponse {
  status: string;
  message: string;
  data: {
    success: boolean;
    results: Array<{
      role: Roles;
      feature: Feature;
      success: boolean;
    }>;
  };
}

/**
 * Update a single permission for a role and feature
 * @param role - The user role to update
 * @param feature - The feature to update access for
 * @param value - Whether to grant or deny access
 * @returns Promise with update result
 */
export const updatePermission = async (
  role: Roles,
  feature: Feature,
  value: boolean
): Promise<UpdatePermissionResponse> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/permissions/update`,
    {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ role, feature, value }),
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      `Failed to update permission: ${errorData.message || response.statusText}`
    );
  }

  const data: UpdatePermissionResponse = await response.json();
  return data;
};

/**
 * Update multiple permissions at once
 * @param permissions - Array of permission updates
 * @returns Promise with bulk update result
 */
export const updateMultiplePermissions = async (
  permissions: PermissionUpdate[]
): Promise<UpdateMultiplePermissionsResponse> => {
  const response = await fetchWithToken(
    `${process.env.API_ENDPOINT}/api/permissions/update-multiple`,
    {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ permissions }),
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      `Failed to update permissions: ${
        errorData.message || response.statusText
      }`
    );
  }

  const data: UpdateMultiplePermissionsResponse = await response.json();
  return data;
};

/**
 * Helper function to toggle a permission (flip its current value)
 * @param role - The user role to toggle
 * @param feature - The feature to toggle access for
 * @returns Promise with update result
 */
export const togglePermission = async (
  role: Roles,
  feature: Feature
): Promise<UpdatePermissionResponse> => {
  try {
    // First get the current permission state
    const currentPermission = await checkRolePermission(role, feature);

    // Toggle the value
    const newValue = !currentPermission.hasPermission;

    // Update with the new value
    return await updatePermission(role, feature, newValue);
  } catch (error) {
    console.error(
      `Failed to toggle permission for role ${role} and feature ${feature}:`,
      error
    );
    throw error;
  }
};

/**
 * Helper function to grant all permissions for a role
 * @param role - The user role to grant all permissions to
 * @returns Promise with bulk update result
 */
export const grantAllPermissions = async (
  role: Roles
): Promise<UpdateMultiplePermissionsResponse> => {
  const allFeatures: Feature[] = [
    "view_reports",
    "create_edit_checklist",
    "assign_template",
    "submit_checklist",
    "access_calendar_view",
    "access_dashboard",
  ];

  const permissions: PermissionUpdate[] = allFeatures.map((feature) => ({
    role,
    feature,
    value: true,
  }));

  return await updateMultiplePermissions(permissions);
};

/**
 * Helper function to revoke all permissions for a role
 * @param role - The user role to revoke all permissions from
 * @returns Promise with bulk update result
 */
export const revokeAllPermissions = async (
  role: Roles
): Promise<UpdateMultiplePermissionsResponse> => {
  const allFeatures: Feature[] = [
    "view_reports",
    "create_edit_checklist",
    "assign_template",
    "submit_checklist",
    "access_calendar_view",
    "access_dashboard",
  ];

  const permissions: PermissionUpdate[] = allFeatures.map((feature) => ({
    role,
    feature,
    value: false,
  }));

  return await updateMultiplePermissions(permissions);
};

/**
 * Helper function to copy permissions from one role to another
 * @param fromRole - The source role to copy permissions from
 * @param toRole - The target role to copy permissions to
 * @returns Promise with bulk update result
 */
export const copyPermissions = async (
  fromRole: Roles,
  toRole: Roles
): Promise<UpdateMultiplePermissionsResponse> => {
  try {
    const allFeatures: Feature[] = [
      "view_reports",
      "create_edit_checklist",
      "assign_template",
      "submit_checklist",
      "access_calendar_view",
      "access_dashboard",
    ];

    // Get current permissions for the source role
    const sourcePermissions = await checkMultipleRolePermissions(
      fromRole,
      allFeatures
    );

    // Create update array for target role
    const permissions: PermissionUpdate[] = allFeatures.map((feature) => ({
      role: toRole,
      feature,
      value: sourcePermissions[feature],
    }));

    return await updateMultiplePermissions(permissions);
  } catch (error) {
    console.error(
      `Failed to copy permissions from ${fromRole} to ${toRole}:`,
      error
    );
    throw error;
  }
};
