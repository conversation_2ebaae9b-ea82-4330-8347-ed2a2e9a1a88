import { DeleteSectionModal } from "@/components/DeleteSectionModal";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { deleteAssignment, fetchAssignments } from "@/data/assignments";
import { canUserAccess } from "@/data/permissions";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Edit,
  Eye,
  ListChecks,
  Loader2,
  Plus,
  Search,
  Trash2,
} from "lucide-react";
import { useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/contexts/AuthContext";

const Assignments = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useSearchParams();
  const [deletingAssignmentId, setDeletingAssignmentId] = useState<
    string | null
  >(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const currentPage = parseInt(searchParams.get("page") || "1", 10);
  const search = searchParams.get("search") || "";
  const branchId = searchParams.get("branchId") || "all";
  const status = searchParams.get("status") || "all";

  const auth = useAuth();
  const userId = auth.user?.id;
  const userEmail = auth.user?.email;

  const setParam = (key: string, value: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set(key, value);
    if (key !== "page") newParams.set("page", "1");
    setSearchParams(newParams);
  };
  // Pagination logic
  const ITEMS_PER_PAGE = 10;

  const { data, isLoading, isError, error, refetch, isFetching } = useQuery({
    queryKey: ["assignments", currentPage, branchId, status],
    queryFn: () =>
      fetchAssignments({
        page: currentPage,
        limit: ITEMS_PER_PAGE,
        search,
        branchId,
        status,
      }),
  });

  // Check if user can create/edit assignments
  const { data: canCreateAssignments = false } = useQuery({
    queryKey: ["user-can-access", "crud_checklist_assignments"],
    queryFn: () => canUserAccess("crud_checklist_assignments"),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const mutation = useMutation({
    mutationFn: (id: string) => deleteAssignment(id),
    retry: 3,
    retryDelay: 1000,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["assignments"] });
    },
  });

  const confirmDelete = () => {
    if (deletingAssignmentId) {
      mutation.mutate(deletingAssignmentId);
      setDeletingAssignmentId(null);
    }
  };

  const assignments = data?.data ?? [];
  console.log("ASSIGNMENTS: ", assignments);
  const totalPages = data?.totalPages ?? 1;
  const branches = [
    ...new Map(
      assignments.map((a) => [
        `${a.branchId}-${a.storeName}`,
        {
          id: a.branchId,
          name: a.storeName,
        },
      ])
    ).values(),
  ];

  if (isFetching || isLoading) {
    return (
      <div className="flex w-full min-h-screen">
        <div className="flex flex-col w-full">
          <div className="flex-1 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="font-bold text-gray-900 text-2xl">
                {t("assignment.title")}
              </h2>
              {canCreateAssignments && (
                <Link to="/assignments/new">
                  <Button className="bg-slate-700 hover:bg-slate-800">
                    <Plus className="mr-2 w-4 h-4" />
                    {t("assignment.assignmentChecklist")}
                  </Button>
                </Link>
              )}
            </div>
            <div className="flex justify-center items-center min-h-96">
              <Loader2 className="w-8 h-8 animate-spin" />
              <span className="ml-2 text-gray-600">
                {t("checklistAssignments.loadingAssignments")}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex w-full min-h-screen">
        <div className="flex flex-col w-full">
          <div className="flex-1 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="font-bold text-gray-900 text-2xl">
                {t("assignment.title")}
              </h2>
              {canCreateAssignments && (
                <Link to="/assignments/new">
                  <Button className="bg-slate-700 hover:bg-slate-800">
                    <Plus className="mr-2 w-4 h-4" />
                    {t("assignment.assignmentChecklist")}
                  </Button>
                </Link>
              )}
            </div>
            <div className="flex justify-center items-center min-h-96">
              <div className="text-center">
                <p className="mb-2 text-red-600">
                  {t("checklistAssignments.errorLoading")}
                </p>
                <p className="mb-4 text-gray-600 text-sm">
                  {error instanceof Error
                    ? error.message
                    : t("global.unknownError")}
                </p>
                <Button
                  variant="outline"
                  onClick={() =>
                    queryClient.refetchQueries({ queryKey: ["assignments"] })
                  }
                >
                  {t("global.retry")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen">
      <div className="border-b">
        <div className="mx-auto px-6 py-3">
          <div className="flex justify-between items-center">
            <h1 className="font-semibold text-gray-900 text-2xl">
              {t("assignment.title")}
            </h1>
            {canCreateAssignments && (
              <Link to="/assignments/new">
                <Button className="bg-slate-700 hover:bg-slate-800">
                  <Plus className="mr-2 w-4 h-4" />
                  {t("assignment.assignmentChecklist")}
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>

      <div className="mx-auto px-6 py-6">
        <div className="flex sm:flex-row flex-col gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="top-1/2 left-3 absolute w-4 h-4 text-gray-400 -translate-y-1/2 transform" />
            <Input
              placeholder={t("checklistAssignments.searchByName")}
              onChange={(e) => {
                setParam("search", e.target.value);
              }}
              className="pl-10"
            />
          </div>
          <Button
            onClick={() => {
              queryClient.invalidateQueries({ queryKey: ["assignments"] });
            }}
          >
            {t("assignment.search")}
          </Button>

          <div className="flex gap-4">
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-700 text-sm">
                {t("assignment.branch")} :
              </span>
              <Select
                value={branchId}
                onValueChange={(val) => {
                  setParam("branchId", val);
                }}
                defaultValue="all"
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("global.all")}</SelectItem>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-700 text-sm">
                {t("assignment.status")} :
              </span>
              <Select
                value={status}
                onValueChange={(value) => setParam("status", value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue
                    placeholder={t("checklistAssignments.selectStatus")}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("global.all")}</SelectItem>
                  <SelectItem value="pending">
                    {t("assignment.pending")}
                  </SelectItem>
                  <SelectItem value="in_progress">
                    {t("checklistAssignments.inProgress")}
                  </SelectItem>
                  <SelectItem value="completed">
                    {t("assignment.completed")}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <Card className="overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b">
                <tr>
                  <th className="px-6 py-4 font-medium text-gray-500 text-sm text-left">
                    {t("assignment.templateName")}
                  </th>
                  <th className="px-6 py-4 font-medium text-gray-500 text-sm text-left">
                    {t("assignment.branch")}
                  </th>
                  <th className="px-6 py-4 font-medium text-gray-500 text-sm text-left">
                    {t("assignment.assignTo")}
                  </th>
                  <th className="px-6 py-4 font-medium text-gray-500 text-sm text-left">
                    {t("assignment.frequency")}
                  </th>
                  <th className="px-6 py-4 font-medium text-gray-500 text-sm text-left">
                    {t("assignment.status")}
                  </th>
                  <th className="px-6 py-4 font-medium text-gray-500 text-sm text-left">
                    {t("assignment.startDue")}
                  </th>
                  <th className="px-6 py-4 font-medium text-gray-500 text-sm text-left">
                    {t("assignment.action")}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {assignments.map((assignment) => (
                  <tr key={assignment.id} className="hover:">
                    <td className="px-6 py-4 font-medium text-gray-900 text-sm">
                      {assignment.templateName}
                    </td>
                    <td className="px-6 py-4 text-gray-600 text-sm">
                      {assignment.storeName}
                    </td>
                    <td className="px-6 py-4 text-gray-600 text-sm">
                      {assignment.assignTo}
                    </td>
                    <td className="px-6 py-4 text-gray-600 text-sm capitalize">
                      {t("assignment." + assignment.frequency)}
                    </td>
                    <td className="px-6 py-4 text-sm capitalize">
                      {t("assignment." + assignment.status)}
                    </td>
                    <td className="px-6 py-4 text-gray-600 text-sm">
                      {assignment.startDate} — {assignment.dueDate}
                    </td>
                    <td className="px-6 py-4">
                      <TooltipProvider>
                        <div className="flex items-center gap-4">
                          <Link
                            to={
                              assignment.status === "completed"
                                ? `/assignment-response/response/${assignment.id}?isAssignmentId=true`
                                : `/assignments/${assignment.id}`
                            }
                          >
                            <Tooltip>
                              <TooltipTrigger>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="p-0"
                                >
                                  <Eye className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  {assignment.status === "completed"
                                    ? "View response"
                                    : "View assignment"}
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </Link>
                          {canCreateAssignments && (
                            <Tooltip>
                              <TooltipTrigger>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setDeletingAssignmentId(assignment.id);
                                    setShowDeleteModal(true);
                                  }}
                                  className="p-0"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{t("global.delete")}</p>
                              </TooltipContent>
                            </Tooltip>
                          )}
                          {assignment.status !== "completed" &&
                            userEmail === assignment.assignTo && (
                              <Link
                                to={`/assignment-response/${assignment.id}`}
                              >
                                <Tooltip>
                                  <TooltipTrigger>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="p-0"
                                    >
                                      <ListChecks className="w-4 h-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{t("assignment.respond")}</p>
                                  </TooltipContent>
                                </Tooltip>
                              </Link>
                            )}
                          {assignment.status !== "completed" &&
                            canCreateAssignments && (
                              <Link to={`/assignments/${assignment.id}/edit`}>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="p-0"
                                    >
                                      <Edit className="w-4 h-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{t("global.edit")}</p>
                                  </TooltipContent>
                                </Tooltip>
                              </Link>
                            )}
                        </div>
                      </TooltipProvider>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
        {!isLoading && totalPages > 1 && (
          <div className="flex justify-center space-x-2 mt-4">
            {Array.from({ length: totalPages }, (_, i) => (
              <Button
                key={i + 1}
                size="sm"
                variant={currentPage === i + 1 ? "default" : "outline"}
                onClick={() => setParam("page", (i + 1).toString())}
                disabled={currentPage === i + 1}
              >
                {i + 1}
              </Button>
            ))}
          </div>
        )}
      </div>
      {/* Delete Modal */}
      <DeleteSectionModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={confirmDelete}
        labelToDelete={t("checklistAssignments.checklistAssignment")}
        isPending={mutation.isPending}
      />
    </div>
  );
};

export default Assignments;
